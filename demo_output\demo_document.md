# Docling演示文档

## 简介

Docling是一个强大的文档处理工具，支持多种格式的文档解析和转换。

## 主要特性

- 📄 支持PDF、DOCX、PPTX等多种格式
- 🔍 高级PDF理解能力
- 🤖 AI集成支持
- 🔒 本地执行保护隐私

## 使用示例

```
from docling.document_converter import DocumentConverter

converter = DocumentConverter()
result = converter.convert("document.pdf")
markdown = result.document.export_to_markdown()
```

## 表格示例

| 功能    | 描述        | 状态   |
|-------|-----------|------|
| PDF解析 | 解析复杂PDF文档 | ✅    |
| 格式转换  | 多种输出格式    | ✅    |
| OCR支持 | 图像文字识别    | ✅    |

这是一个演示文档。
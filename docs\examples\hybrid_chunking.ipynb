{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Hybrid chunking"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overview"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Hybrid chunking applies tokenization-aware refinements on top of document-based hierarchical chunking.\n", "\n", "For more details, see [here](../../concepts/chunking#hybrid-chunker)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install -qU pip docling transformers"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["DOC_SOURCE = \"../../tests/data/md/wiki.md\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic usage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We first convert the document:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from docling.document_converter import DocumentConverter\n", "\n", "doc = DocumentConverter().convert(source=DOC_SOURCE).document"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For a basic chunking scenario, we can just instantiate a `HybridChunker`, which will use\n", "the default parameters."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Token indices sequence length is longer than the specified maximum sequence length for this model (531 > 512). Running this sequence through the model will result in indexing errors\n"]}], "source": ["from docling.chunking import HybridChunker\n", "\n", "chunker = HybridChunker()\n", "chunk_iter = chunker.chunk(dl_doc=doc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> 👉 **NOTE**: As you see above, using the `HybridChunker` can sometimes lead to a warning from the transformers library, however this is a \"false alarm\" — for details check [here](https://docling-project.github.io/docling/faq/#hybridchunker-triggers-warning-token-indices-sequence-length-is-longer-than-the-specified-maximum-sequence-length-for-this-model)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that the text you would typically want to embed is the context-enriched one as\n", "returned by the `contextualize()` method:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 0 ===\n", "chunk.text:\n", "'International Business Machines Corporation (using the trademark IBM), nicknamed Big Blue, is an American multinational technology company headquartered in Armonk, New York and present in over 175 countries.\\nIt is a publicly traded company and one of the 30 companies in the Dow Jones Industrial Aver…'\n", "chunker.contextualize(chunk):\n", "'IBM\\nInternational Business Machines Corporation (using the trademark IBM), nicknamed Big Blue, is an American multinational technology company headquartered in Armonk, New York and present in over 175 countries.\\nIt is a publicly traded company and one of the 30 companies in the Dow Jones Industrial …'\n", "\n", "=== 1 ===\n", "chunk.text:\n", "'IBM originated with several technological innovations developed and commercialized in the late 19th century. <PERSON> patented the computing scale in 1885;[17] <PERSON> invented the dial recorder (1888);[18] <PERSON> patented the Electric Tabulating Machine (1889);[19] and <PERSON><PERSON>'\n", "chunker.contextualize(chunk):\n", "'IBM\\n1910s–1950s\\nIBM originated with several technological innovations developed and commercialized in the late 19th century. <PERSON> patented the computing scale in 1885;[17] <PERSON> invented the dial recorder (1888);[18] <PERSON> patented the Electric Tabulating Machine (1889…'\n", "\n", "=== 2 ===\n", "chunk.text:\n", "'Collectively, the companies manufactured a wide array of machinery for sale and lease, ranging from commercial scales and industrial time recorders, meat and cheese slicers, to tabulators and punched cards. <PERSON>, Sr., fired from the National Cash Register Company by <PERSON>,…'\n", "chunker.contextualize(chunk):\n", "'IBM\\n1910s–1950s\\nCollectively, the companies manufactured a wide array of machinery for sale and lease, ranging from commercial scales and industrial time recorders, meat and cheese slicers, to tabulators and punched cards. <PERSON>, Sr., fired from the National Cash Register Company by <PERSON>'\n", "\n", "=== 3 ===\n", "chunk.text:\n", "'In 1961, IBM developed the SABRE reservation system for American Airlines and introduced the highly successful Selectric typewriter.…'\n", "chunker.contextualize(chunk):\n", "'IBM\\n1960s–1980s\\nIn 1961, IBM developed the SABRE reservation system for American Airlines and introduced the highly successful Selectric typewriter.…'\n", "\n"]}], "source": ["for i, chunk in enumerate(chunk_iter):\n", "    print(f\"=== {i} ===\")\n", "    print(f\"chunk.text:\\n{f'{chunk.text[:300]}…'!r}\")\n", "\n", "    enriched_text = chunker.contextualize(chunk=chunk)\n", "    print(f\"chunker.contextualize(chunk):\\n{f'{enriched_text[:300]}…'!r}\")\n", "\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuring tokenization\n", "\n", "For more control on the chunking, we can parametrize tokenization as shown below.\n", "\n", "In a RAG / retrieval context, it is important to make sure that the chunker and\n", "embedding model are using the same tokenizer.\n", "\n", "👉 HuggingFace transformers tokenizers can be used as shown in the following example:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from docling_core.transforms.chunker.tokenizer.huggingface import HuggingFaceTokenizer\n", "from transformers import AutoTokenizer\n", "\n", "from docling.chunking import HybridChunker\n", "\n", "EMBED_MODEL_ID = \"sentence-transformers/all-MiniLM-L6-v2\"\n", "MAX_TOKENS = 64  # set to a small number for illustrative purposes\n", "\n", "tokenizer = HuggingFaceTokenizer(\n", "    tokenizer=AutoTokenizer.from_pretrained(EMBED_MODEL_ID),\n", "    max_tokens=MAX_TOKENS,  # optional, by default derived from `tokenizer` for HF case\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["👉 Alternatively, [OpenAI tokenizers](https://github.com/openai/tiktoken) can be used as shown in the example below (uncomment to use — requires installing `docling-core[chunking-openai]`):"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# import tiktoken\n", "\n", "# from docling_core.transforms.chunker.tokenizer.openai import OpenAITokenizer\n", "\n", "# tokenizer = OpenAITokenizer(\n", "#     tokenizer=tiktoken.encoding_for_model(\"gpt-4o\"),\n", "#     max_tokens=128 * 1024,  # context window length required for OpenAI tokenizers\n", "# )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can now instantiate our chunker:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["chunker = HybridChunker(\n", "    tokenizer=tokenizer,\n", "    merge_peers=True,  # optional, defaults to True\n", ")\n", "chunk_iter = chunker.chunk(dl_doc=doc)\n", "chunks = list(chunk_iter)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Points to notice looking at the output chunks below:\n", "- Where possible, we fit the limit of 64 tokens for the metadata-enriched serialization form (see chunk 2)\n", "- Where needed, we stop before the limit, e.g. see cases of 63 as it would otherwise run into a comma (see chunk 6)\n", "- Where possible, we merge undersized peer chunks (see chunk 0)\n", "- \"Tail\" chunks trailing right after merges may still be undersized (see chunk 8)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 0 ===\n", "chunk.text (55 tokens):\n", "'International Business Machines Corporation (using the trademark IBM), nicknamed Big Blue, is an American multinational technology company headquartered in Armonk, New York and present in over 175 countries.\\nIt is a publicly traded company and one of the 30 companies in the Dow Jones Industrial Average.'\n", "chunker.contextualize(chunk) (56 tokens):\n", "'IBM\\nInternational Business Machines Corporation (using the trademark IBM), nicknamed Big Blue, is an American multinational technology company headquartered in Armonk, New York and present in over 175 countries.\\nIt is a publicly traded company and one of the 30 companies in the Dow Jones Industrial Average.'\n", "\n", "=== 1 ===\n", "chunk.text (45 tokens):\n", "'IBM is the largest industrial research organization in the world, with 19 research facilities across a dozen countries, having held the record for most annual U.S. patents generated by a business for 29 consecutive years from 1993 to 2021.'\n", "chunker.contextualize(chunk) (46 tokens):\n", "'IBM\\nIBM is the largest industrial research organization in the world, with 19 research facilities across a dozen countries, having held the record for most annual U.S. patents generated by a business for 29 consecutive years from 1993 to 2021.'\n", "\n", "=== 2 ===\n", "chunk.text (63 tokens):\n", "'IBM was founded in 1911 as the Computing-Tabulating-Recording Company (CTR), a holding company of manufacturers of record-keeping and measuring systems. It was renamed \"International Business Machines\" in 1924 and soon became the leading manufacturer of punch-card tabulating systems. During the 1960s and 1970s, the'\n", "chunker.contextualize(chunk) (64 tokens):\n", "'IBM\\nIBM was founded in 1911 as the Computing-Tabulating-Recording Company (CTR), a holding company of manufacturers of record-keeping and measuring systems. It was renamed \"International Business Machines\" in 1924 and soon became the leading manufacturer of punch-card tabulating systems. During the 1960s and 1970s, the'\n", "\n", "=== 3 ===\n", "chunk.text (44 tokens):\n", "\"IBM mainframe, exemplified by the System/360, was the world's dominant computing platform, with the company producing 80 percent of computers in the U.S. and 70 percent of computers worldwide.[11]\"\n", "chunker.contextualize(chunk) (45 tokens):\n", "\"IBM\\nIBM mainframe, exemplified by the System/360, was the world's dominant computing platform, with the company producing 80 percent of computers in the U.S. and 70 percent of computers worldwide.[11]\"\n", "\n", "=== 4 ===\n", "chunk.text (63 tokens):\n", "'IBM debuted in the microcomputer market in 1981 with the IBM Personal Computer, — its DOS software provided by Microsoft, — which became the basis for the majority of personal computers to the present day.[12] The company later also found success in the portable space with the ThinkPad. Since the 1990s,'\n", "chunker.contextualize(chunk) (64 tokens):\n", "'IBM\\nIBM debuted in the microcomputer market in 1981 with the IBM Personal Computer, — its DOS software provided by Microsoft, — which became the basis for the majority of personal computers to the present day.[12] The company later also found success in the portable space with the ThinkPad. Since the 1990s,'\n", "\n", "=== 5 ===\n", "chunk.text (61 tokens):\n", "'IBM has concentrated on computer services, software, supercomputers, and scientific research; it sold its microcomputer division to Lenovo in 2005. IBM continues to develop mainframes, and its supercomputers have consistently ranked among the most powerful in the world in the 21st century.'\n", "chunker.contextualize(chunk) (62 tokens):\n", "'IBM\\nIBM has concentrated on computer services, software, supercomputers, and scientific research; it sold its microcomputer division to Lenovo in 2005. IBM continues to develop mainframes, and its supercomputers have consistently ranked among the most powerful in the world in the 21st century.'\n", "\n", "=== 6 ===\n", "chunk.text (62 tokens):\n", "\"As one of the world's oldest and largest technology companies, IBM has been responsible for several technological innovations, including the automated teller machine (ATM), dynamic random-access memory (DRAM), the floppy disk, the hard disk drive, the magnetic stripe card, the relational database, the SQL programming\"\n", "chunker.contextualize(chunk) (63 tokens):\n", "\"IBM\\nAs one of the world's oldest and largest technology companies, IBM has been responsible for several technological innovations, including the automated teller machine (ATM), dynamic random-access memory (DRAM), the floppy disk, the hard disk drive, the magnetic stripe card, the relational database, the SQL programming\"\n", "\n", "=== 7 ===\n", "chunk.text (63 tokens):\n", "'language, and the UPC barcode. The company has made inroads in advanced computer chips, quantum computing, artificial intelligence, and data infrastructure.[13][14][15] IBM employees and alumni have won various recognitions for their scientific research and inventions, including six Nobel Prizes and six Turing'\n", "chunker.contextualize(chunk) (64 tokens):\n", "'IBM\\nlanguage, and the UPC barcode. The company has made inroads in advanced computer chips, quantum computing, artificial intelligence, and data infrastructure.[13][14][15] IBM employees and alumni have won various recognitions for their scientific research and inventions, including six Nobel Prizes and six Turing'\n", "\n", "=== 8 ===\n", "chunk.text (5 tokens):\n", "'Awards.[16]'\n", "chunker.contextualize(chunk) (6 tokens):\n", "'IBM\\nAwards.[16]'\n", "\n", "=== 9 ===\n", "chunk.text (56 tokens):\n", "'IBM originated with several technological innovations developed and commercialized in the late 19th century. <PERSON> patented the computing scale in 1885;[17] <PERSON> invented the dial recorder (1888);[18] <PERSON> patented the Electric Tabulating Machine'\n", "chunker.contextualize(chunk) (60 tokens):\n", "'IBM\\n1910s–1950s\\nIBM originated with several technological innovations developed and commercialized in the late 19th century. <PERSON> patented the computing scale in 1885;[17] <PERSON> invented the dial recorder (1888);[18] <PERSON> patented the Electric Tabulating Machine'\n", "\n", "=== 10 ===\n", "chunk.text (60 tokens):\n", "\"(1889);[19] and <PERSON> invented a time clock to record workers' arrival and departure times on a paper tape (1889).[20] On June 16, 1911, their four companies were amalgamated in New York State by <PERSON> forming a fifth company, the\"\n", "chunker.contextualize(chunk) (64 tokens):\n", "\"IBM\\n1910s–1950s\\n(1889);[19] and <PERSON> invented a time clock to record workers' arrival and departure times on a paper tape (1889).[20] On June 16, 1911, their four companies were amalgamated in New York State by <PERSON> forming a fifth company, the\"\n", "\n", "=== 11 ===\n", "chunk.text (59 tokens):\n", "'Computing-Tabulating-Recording Company (CTR) based in Endicott, New York.[1][21] The five companies had 1,300 employees and offices and plants in Endicott and Binghamton, New York; Dayton, Ohio; Detroit, Michigan; Washington,'\n", "chunker.contextualize(chunk) (63 tokens):\n", "'IBM\\n1910s–1950s\\nComputing-Tabulating-Recording Company (CTR) based in Endicott, New York.[1][21] The five companies had 1,300 employees and offices and plants in Endicott and Binghamton, New York; Dayton, Ohio; Detroit, Michigan; Washington,'\n", "\n", "=== 12 ===\n", "chunk.text (13 tokens):\n", "'D.C.; and Toronto, Canada.[22]'\n", "chunker.contextualize(chunk) (17 tokens):\n", "'IBM\\n1910s–1950s\\nD.C.; and Toronto, Canada.[22]'\n", "\n", "=== 13 ===\n", "chunk.text (60 tokens):\n", "'Collectively, the companies manufactured a wide array of machinery for sale and lease, ranging from commercial scales and industrial time recorders, meat and cheese slicers, to tabulators and punched cards. <PERSON>, Sr., fired from the National Cash Register Company by <PERSON>, called'\n", "chunker.contextualize(chunk) (64 tokens):\n", "'IBM\\n1910s–1950s\\nCollectively, the companies manufactured a wide array of machinery for sale and lease, ranging from commercial scales and industrial time recorders, meat and cheese slicers, to tabulators and punched cards. <PERSON>, Sr., fired from the National Cash Register Company by <PERSON>, called'\n", "\n", "=== 14 ===\n", "chunk.text (59 tokens):\n", "\"on <PERSON> and, in 1914, was offered a position at CTR.[23] <PERSON> joined CTR as general manager and then, 11 months later, was made President when antitrust cases relating to his time at NCR were resolved.[24] Having learned <PERSON>'s pioneering business\"\n", "chunker.contextualize(chunk) (63 tokens):\n", "\"IBM\\n1910s–1950s\\non Flint and, in 1914, was offered a position at CTR.[23] <PERSON> joined CTR as general manager and then, 11 months later, was made President when antitrust cases relating to his time at NCR were resolved.[24] Having learned <PERSON>'s pioneering business\"\n", "\n", "=== 15 ===\n", "chunk.text (23 tokens):\n", "\"practices, <PERSON> proceeded to put the stamp of NCR onto CTR's companies.[23]:\\n105\"\n", "chunker.contextualize(chunk) (27 tokens):\n", "\"IBM\\n1910s–1950s\\npractices, <PERSON> proceeded to put the stamp of NCR onto CTR's companies.[23]:\\n105\"\n", "\n", "=== 16 ===\n", "chunk.text (59 tokens):\n", "'He implemented sales conventions, \"generous sales incentives, a focus on customer service, an insistence on well-groomed, dark-suited salesmen and had an evangelical fervor for instilling company pride and loyalty in every worker\".[25][26] His favorite slogan,'\n", "chunker.contextualize(chunk) (63 tokens):\n", "'IBM\\n1910s–1950s\\nHe implemented sales conventions, \"generous sales incentives, a focus on customer service, an insistence on well-groomed, dark-suited salesmen and had an evangelical fervor for instilling company pride and loyalty in every worker\".[25][26] His favorite slogan,'\n", "\n", "=== 17 ===\n", "chunk.text (60 tokens):\n", "'\"THINK\", became a mantra for each company\\'s employees.[25] During <PERSON>\\'s first four years, revenues reached $9 million ($158 million today) and the company\\'s operations expanded to Europe, South America, Asia and Australia.[25] <PERSON> never liked the'\n", "chunker.contextualize(chunk) (64 tokens):\n", "'IBM\\n1910s–1950s\\n\"THINK\", became a mantra for each company\\'s employees.[25] During Watson\\'s first four years, revenues reached $9 million ($158 million today) and the company\\'s operations expanded to Europe, South America, Asia and Australia.[25] <PERSON> never liked the'\n", "\n", "=== 18 ===\n", "chunk.text (57 tokens):\n", "'clumsy hyphenated name \"Computing-Tabulating-Recording Company\" and chose to replace it with the more expansive title \"International Business Machines\" which had previously been used as the name of CTR\\'s Canadian Division;[27] the name was changed on February 14,'\n", "chunker.contextualize(chunk) (61 tokens):\n", "'IBM\\n1910s–1950s\\nclumsy hyphenated name \"Computing-Tabulating-Recording Company\" and chose to replace it with the more expansive title \"International Business Machines\" which had previously been used as the name of CTR\\'s Canadian Division;[27] the name was changed on February 14,'\n", "\n", "=== 19 ===\n", "chunk.text (21 tokens):\n", "'1924.[28] By 1933, most of the subsidiaries had been merged into one company, IBM.'\n", "chunker.contextualize(chunk) (25 tokens):\n", "'IBM\\n1910s–1950s\\n1924.[28] By 1933, most of the subsidiaries had been merged into one company, IBM.'\n", "\n", "=== 20 ===\n", "chunk.text (22 tokens):\n", "'In 1961, IBM developed the SABRE reservation system for American Airlines and introduced the highly successful Selectric typewriter.'\n", "chunker.contextualize(chunk) (26 tokens):\n", "'IBM\\n1960s–1980s\\nIn 1961, IBM developed the SABRE reservation system for American Airlines and introduced the highly successful Selectric typewriter.'\n", "\n"]}], "source": ["for i, chunk in enumerate(chunks):\n", "    print(f\"=== {i} ===\")\n", "    txt_tokens = tokenizer.count_tokens(chunk.text)\n", "    print(f\"chunk.text ({txt_tokens} tokens):\\n{chunk.text!r}\")\n", "\n", "    ser_txt = chunker.contextualize(chunk=chunk)\n", "    ser_tokens = tokenizer.count_tokens(ser_txt)\n", "    print(f\"chunker.contextualize(chunk) ({ser_tokens} tokens):\\n{ser_txt!r}\")\n", "\n", "    print()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}
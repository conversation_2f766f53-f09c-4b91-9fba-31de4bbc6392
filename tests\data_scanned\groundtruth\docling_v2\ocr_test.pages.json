[{"page_no": 0, "size": {"width": 595.2, "height": 841.92}, "parsed_page": {"dimension": {"angle": 0.0, "rect": {"r_x0": 0.0, "r_y0": 0.0, "r_x1": 595.2, "r_y1": 0.0, "r_x2": 595.2, "r_y2": 841.92, "r_x3": 0.0, "r_y3": 841.92, "coord_origin": "BOTTOMLEFT"}, "boundary_type": "crop_box", "art_bbox": {"l": 0.0, "t": 841.92, "r": 595.2, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "bleed_bbox": {"l": 0.0, "t": 841.92, "r": 595.2, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "crop_bbox": {"l": 0.0, "t": 841.92, "r": 595.2, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "media_bbox": {"l": 0.0, "t": 841.92, "r": 595.2, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "trim_bbox": {"l": 0.0, "t": 841.92, "r": 595.2, "b": 0.0, "coord_origin": "BOTTOMLEFT"}}, "bitmap_resources": [{"index": 0, "rect": {"r_x0": 0.0, "r_y0": 0.0, "r_x1": 595.2, "r_y1": 0.0, "r_x2": 595.2, "r_y2": 841.92, "r_x3": 0.0, "r_y3": 841.92, "coord_origin": "BOTTOMLEFT"}, "uri": null}], "char_cells": [], "word_cells": [], "textline_cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 73.35, "r_y0": 98.0, "r_x1": 503.65, "r_y1": 98.0, "r_x2": 503.65, "r_y2": 77.0, "r_x3": 73.35, "r_y3": 77.0, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 69.68, "r_y0": 124.83, "r_x1": 504.87, "r_y1": 124.83, "r_x2": 504.87, "r_y2": 104.0, "r_x3": 69.68, "r_y3": 104.0, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 71.84, "r_y0": 152.91, "r_x1": 153.09, "r_y1": 152.91, "r_x2": 153.09, "r_y2": 129.8, "r_x3": 71.84, "r_y3": 129.8, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "has_chars": false, "has_words": false, "has_lines": true, "image": null, "lines": []}, "predictions": {"layout": {"clusters": [{"id": 0, "label": "text", "bbox": {"l": 69.68, "t": 77.0, "r": 504.87, "b": 152.91, "coord_origin": "TOPLEFT"}, "confidence": 0.972, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 73.35, "r_y0": 98.0, "r_x1": 503.65, "r_y1": 98.0, "r_x2": 503.65, "r_y2": 77.0, "r_x3": 73.35, "r_y3": 77.0, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 69.68, "r_y0": 124.83, "r_x1": 504.87, "r_y1": 124.83, "r_x2": 504.87, "r_y2": 104.0, "r_x3": 69.68, "r_y3": 104.0, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 71.84, "r_y0": 152.91, "r_x1": 153.09, "r_y1": 152.91, "r_x2": 153.09, "r_y2": 129.8, "r_x3": 71.84, "r_y3": 129.8, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}]}, "tablestructure": {"table_map": {}}, "figures_classification": null, "equations_prediction": null, "vlm_response": null}, "assembled": {"elements": [{"label": "text", "id": 0, "page_no": 0, "cluster": {"id": 0, "label": "text", "bbox": {"l": 69.68, "t": 77.0, "r": 504.87, "b": 152.91, "coord_origin": "TOPLEFT"}, "confidence": 0.972, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 73.35, "r_y0": 98.0, "r_x1": 503.65, "r_y1": 98.0, "r_x2": 503.65, "r_y2": 77.0, "r_x3": 73.35, "r_y3": 77.0, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 69.68, "r_y0": 124.83, "r_x1": 504.87, "r_y1": 124.83, "r_x2": 504.87, "r_y2": 104.0, "r_x3": 69.68, "r_y3": 104.0, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 71.84, "r_y0": 152.91, "r_x1": 153.09, "r_y1": 152.91, "r_x2": 153.09, "r_y2": 129.8, "r_x3": 71.84, "r_y3": 129.8, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "Docling bundles PDF document conversion to JSON and Markdown in an easy self contained package"}], "body": [{"label": "text", "id": 0, "page_no": 0, "cluster": {"id": 0, "label": "text", "bbox": {"l": 69.68, "t": 77.0, "r": 504.87, "b": 152.91, "coord_origin": "TOPLEFT"}, "confidence": 0.972, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 73.35, "r_y0": 98.0, "r_x1": 503.65, "r_y1": 98.0, "r_x2": 503.65, "r_y2": 77.0, "r_x3": 73.35, "r_y3": 77.0, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 69.68, "r_y0": 124.83, "r_x1": 504.87, "r_y1": 124.83, "r_x2": 504.87, "r_y2": 104.0, "r_x3": 69.68, "r_y3": 104.0, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 71.84, "r_y0": 152.91, "r_x1": 153.09, "r_y1": 152.91, "r_x2": 153.09, "r_y2": 129.8, "r_x3": 71.84, "r_y3": 129.8, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "Docling bundles PDF document conversion to JSON and Markdown in an easy self contained package"}], "headers": []}}]
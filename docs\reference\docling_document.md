# Docling Document

This is an automatic generated API reference of the DoclingDocument type.

::: docling_core.types.doc
    handler: python
    options:
        members:
            - DoclingDocument
            - DocumentOrigin
            - DocItem
            - DocItemLabel
            - ProvenanceItem
            - GroupItem
            - GroupLabel
            - NodeItem
            - PageItem
            - FloatingItem
            - TextItem
            - TableItem
            - TableCell
            - TableData
            - TableCellLabel
            - KeyValueItem
            - SectionHeaderItem
            - PictureItem
            - ImageRef
            - PictureClassificationClass
            - PictureClassificationData
            - RefItem
            - BoundingBox
            - CoordOrigin
            - ImageRefMode
            - Size
        docstring_style: sphinx
        show_if_no_docstring: true
        show_submodules: true
        docstring_section_style: list
        filters: ["!^_"]
        heading_level: 2
        show_root_toc_entry: true
        inherited_members: true
        merge_init_into_class: true
        separate_signature: true
        show_root_heading: true
        show_root_full_path: false
        show_signature_annotations: true
        show_source: false
        show_symbol_type_heading: true
        show_symbol_type_toc: true
        show_labels: false
        signature_crossrefs: true
        summary: true

<html>
    <body>
        <h1>Introduction to parsing HTML files with <img src="https://docling-project.github.io/docling/assets/logo.png" alt="Docling" height="64"> Docling</h1>
        <p>Docling simplifies document processing, parsing diverse formats — including HTML — and providing seamless integrations with the gen AI ecosystem.</p>
        <h2>Supported file formats</h2>
        <p>Docling supports multiple file formats..</p>
        <ul>
            <li><img src="https://github.com/docling-project/docling/tree/main/docs/assets/pdf.png" height="32" alt="PDF">Advanced PDF understanding</li>
            <li><img src="https://github.com/docling-project/docling/tree/main/docs/assets/docx.png" height="32" alt="DOCX">Microsoft Office DOCX</li>
            <li><img src="https://github.com/docling-project/docling/tree/main/docs/assets/html.png" height="32" alt="HTML">HTML files (with optional support for images)</li>
        </ul>
        <h3>Three backends for handling HTML files</h3>
        <p>Docling has three backends for parsing HTML files:</p>
        <ol>
            <li><b>HTMLDocumentBackend</b> Ignores images</li>
            <li><b>HTMLDocumentBackendImagesInline</b> Extracts images inline</li>
            <li><b>HTMLDocumentBackendImagesReferenced</b> Extracts images as references</li>
        </ol>
    </body>
</html>


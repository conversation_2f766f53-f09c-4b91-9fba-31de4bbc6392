{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "example_07", "origin": {"mimetype": "text/html", "binary_hash": 623628706615267627, "filename": "example_07.html"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/groups/0"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/13"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/texts/4"}, "children": [{"$ref": "#/texts/5"}, {"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/10"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/texts/7"}, "children": [{"$ref": "#/groups/4"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/groups/3"}, "children": [{"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/5", "parent": {"$ref": "#/texts/10"}, "children": [{"$ref": "#/groups/6"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/6", "parent": {"$ref": "#/groups/5"}, "children": [{"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/groups/0"}, "children": [{"$ref": "#/groups/1"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Asia", "text": "Asia", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "China", "text": "China", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Japan", "text": "Japan", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Thailand", "text": "Thailand", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/groups/0"}, "children": [{"$ref": "#/groups/2"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Europe", "text": "Europe", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "UK", "text": "UK", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Germany", "text": "Germany", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/groups/2"}, "children": [{"$ref": "#/groups/3"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Switzerland", "text": "Switzerland", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Bern", "text": "Bern", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Aargau", "text": "Aargau", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/groups/2"}, "children": [{"$ref": "#/groups/5"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Italy", "text": "Italy", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Piedmont", "text": "Piedmont", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Liguria", "text": "Liguria", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Africa", "text": "Africa", "enumerated": false, "marker": ""}], "pictures": [], "tables": [], "key_value_items": [], "form_items": [], "pages": {}}
[{"page_no": 0, "size": {"width": 595.2, "height": 841.92}, "parsed_page": {"dimension": {"angle": 0.0, "rect": {"r_x0": 0.0, "r_y0": -0.0, "r_x1": -595.2, "r_y1": -0.0, "r_x2": -595.2, "r_y2": 841.92, "r_x3": 0.0, "r_y3": 841.92, "coord_origin": "BOTTOMLEFT"}, "boundary_type": "crop_box", "art_bbox": {"l": 0.0, "t": 841.92, "r": -595.2, "b": -0.0, "coord_origin": "BOTTOMLEFT"}, "bleed_bbox": {"l": 0.0, "t": 841.92, "r": -595.2, "b": -0.0, "coord_origin": "BOTTOMLEFT"}, "crop_bbox": {"l": 0.0, "t": 841.92, "r": -595.2, "b": -0.0, "coord_origin": "BOTTOMLEFT"}, "media_bbox": {"l": 0.0, "t": 841.92, "r": -595.2, "b": -0.0, "coord_origin": "BOTTOMLEFT"}, "trim_bbox": {"l": 0.0, "t": 841.92, "r": -595.2, "b": -0.0, "coord_origin": "BOTTOMLEFT"}}, "bitmap_resources": [], "char_cells": [], "word_cells": [], "textline_cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 89.24, "r_y0": 764.9, "r_x1": 521.99, "r_y1": 764.9, "r_x2": 521.99, "r_y2": 744.09, "r_x3": 89.24, "r_y3": 744.09, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 89.24, "r_y0": 739.2, "r_x1": 523.21, "r_y1": 739.2, "r_x2": 523.21, "r_y2": 717.17, "r_x3": 89.24, "r_y3": 717.17, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 441.26, "r_y0": 710.03, "r_x1": 522.03, "r_y1": 710.03, "r_x2": 522.03, "r_y2": 690.04, "r_x3": 441.26, "r_y3": 690.04, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "has_chars": false, "has_words": false, "has_lines": true, "image": null, "lines": []}, "predictions": {"layout": {"clusters": [{"id": 0, "label": "text", "bbox": {"l": 89.24, "t": 717.17, "r": 523.21, "b": 764.9, "coord_origin": "TOPLEFT"}, "confidence": 0.732, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 89.24, "r_y0": 764.9, "r_x1": 521.99, "r_y1": 764.9, "r_x2": 521.99, "r_y2": 744.09, "r_x3": 89.24, "r_y3": 744.09, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 89.24, "r_y0": 739.2, "r_x1": 523.21, "r_y1": 739.2, "r_x2": 523.21, "r_y2": 717.17, "r_x3": 89.24, "r_y3": 717.17, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, {"id": 2, "label": "text", "bbox": {"l": 441.26, "t": 690.04, "r": 522.03, "b": 710.03, "coord_origin": "TOPLEFT"}, "confidence": 0.598, "cells": [{"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 441.26, "r_y0": 710.03, "r_x1": 522.03, "r_y1": 710.03, "r_x2": 522.03, "r_y2": 690.04, "r_x3": 441.26, "r_y3": 690.04, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}]}, "tablestructure": {"table_map": {}}, "figures_classification": null, "equations_prediction": null, "vlm_response": null}, "assembled": {"elements": [{"label": "text", "id": 0, "page_no": 0, "cluster": {"id": 0, "label": "text", "bbox": {"l": 89.24, "t": 717.17, "r": 523.21, "b": 764.9, "coord_origin": "TOPLEFT"}, "confidence": 0.732, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 89.24, "r_y0": 764.9, "r_x1": 521.99, "r_y1": 764.9, "r_x2": 521.99, "r_y2": 744.09, "r_x3": 89.24, "r_y3": 744.09, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 89.24, "r_y0": 739.2, "r_x1": 523.21, "r_y1": 739.2, "r_x2": 523.21, "r_y2": 717.17, "r_x3": 89.24, "r_y3": 717.17, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "Docling bundles PDF document conversion to JSON and Markdown in an easy self contained"}, {"label": "text", "id": 2, "page_no": 0, "cluster": {"id": 2, "label": "text", "bbox": {"l": 441.26, "t": 690.04, "r": 522.03, "b": 710.03, "coord_origin": "TOPLEFT"}, "confidence": 0.598, "cells": [{"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 441.26, "r_y0": 710.03, "r_x1": 522.03, "r_y1": 710.03, "r_x2": 522.03, "r_y2": 690.04, "r_x3": 441.26, "r_y3": 690.04, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "package"}], "body": [{"label": "text", "id": 0, "page_no": 0, "cluster": {"id": 0, "label": "text", "bbox": {"l": 89.24, "t": 717.17, "r": 523.21, "b": 764.9, "coord_origin": "TOPLEFT"}, "confidence": 0.732, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 89.24, "r_y0": 764.9, "r_x1": 521.99, "r_y1": 764.9, "r_x2": 521.99, "r_y2": 744.09, "r_x3": 89.24, "r_y3": 744.09, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 89.24, "r_y0": 739.2, "r_x1": 523.21, "r_y1": 739.2, "r_x2": 523.21, "r_y2": 717.17, "r_x3": 89.24, "r_y3": 717.17, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "Docling bundles PDF document conversion to JSON and Markdown in an easy self contained"}, {"label": "text", "id": 2, "page_no": 0, "cluster": {"id": 2, "label": "text", "bbox": {"l": 441.26, "t": 690.04, "r": 522.03, "b": 710.03, "coord_origin": "TOPLEFT"}, "confidence": 0.598, "cells": [{"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 441.26, "r_y0": 710.03, "r_x1": 522.03, "r_y1": 710.03, "r_x2": 522.03, "r_y2": 690.04, "r_x3": 441.26, "r_y3": 690.04, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "package"}], "headers": []}}]
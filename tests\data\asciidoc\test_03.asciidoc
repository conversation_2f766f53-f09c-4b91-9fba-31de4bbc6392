:_mod-docs-content-type: PROCEDURE
:experimental:

[id="renaming-a-bookmark_{context}"]
= Renaming a bookmark

You can rename a bookmark to distinguish it from other bookmarks. If you have bookmarks to several folders that all share the same name, you can tell the bookmarks apart if you rename them.

Renaming the bookmark does not rename the folder.

.Procedure

. Right-click the bookmark in the side bar.

. Select *Rename…*.
+
image::rename-bookmark-menu.png[Rename bookmark menu]

. In the *Name* field, enter the new name for the bookmark.
+
image::rename-bookmark-text.png[Bookmark name field]

. Click btn:[Rename].

.Verification

* Check that the side bar lists the bookmark under the new name.
+
image::renamed-bookmark.png[Renamed bookmark]
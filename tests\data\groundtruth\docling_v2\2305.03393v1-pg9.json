{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "2305.03393v1-pg9", "origin": {"mimetype": "application/pdf", "binary_hash": 3463920545297462180, "filename": "2305.03393v1-pg9.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 194.48, "t": 698.34, "r": 447.55, "b": 689.96, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Optimized Table Tokenization for Table Structure Recognition", "text": "Optimized Table Tokenization for Table Structure Recognition"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 475.98, "t": 698.34, "r": 480.59, "b": 689.96, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "9", "text": "9"}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 673.13, "r": 480.6, "b": 639.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 163]}], "orig": "order to compute the TED score. Inference timing results for all experiments were obtained from the same machine on a single core with AMD EPYC 7763 CPU @2.45 GHz.", "text": "order to compute the TED score. Inference timing results for all experiments were obtained from the same machine on a single core with AMD EPYC 7763 CPU @2.45 GHz."}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 622.84, "r": 318.45, "b": 613.62, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "5.1 Hyper Parameter Optimization", "text": "5.1 Hyper Parameter Optimization", "level": 1}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 606.47, "r": 480.6, "b": 536.93, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 423]}], "orig": "We have chosen the PubTabNet data set to perform HPO, since it includes a highly diverse set of tables. Also we report TED scores separately for simple and complex tables (tables with cell spans). Results are presented in Table. 1. It is evident that with OTSL, our model achieves the same TED score and slightly better mAP scores in comparison to HTML. However OTSL yields a 2x speed up in the inference runtime over HTML.", "text": "We have chosen the PubTabNet data set to perform HPO, since it includes a highly diverse set of tables. Also we report TED scores separately for simple and complex tables (tables with cell spans). Results are presented in Table. 1. It is evident that with OTSL, our model achieves the same TED score and slightly better mAP scores in comparison to HTML. However OTSL yields a 2x speed up in the inference runtime over HTML."}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/tables/0"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 518.26, "r": 480.6, "b": 464.76, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 398]}], "orig": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart.", "text": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart."}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 283.88, "r": 264.41, "b": 274.65, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "5.2 Quantitative Results", "text": "5.2 Quantitative Results", "level": 1}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 267.51, "r": 480.72, "b": 174.53, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 557]}], "orig": "We picked the model parameter configuration that produced the best prediction quality (enc=6, dec=6, heads=8) with PubTabNet alone, then independently trained and evaluated it on three publicly available data sets: PubTabNet (395k samples), FinTabNet (113k samples) and PubTables-1M (about 1M samples). Performance results are presented in Table. 2. It is clearly evident that the model trained on OTSL outperforms HTML across the board, keeping high TEDs and mAP scores even on di ffi cult financial tables (FinTabNet) that contain sparse and large tables.", "text": "We picked the model parameter configuration that produced the best prediction quality (enc=6, dec=6, heads=8) with PubTabNet alone, then independently trained and evaluated it on three publicly available data sets: PubTabNet (395k samples), FinTabNet (113k samples) and PubTables-1M (about 1M samples). Performance results are presented in Table. 2. It is clearly evident that the model trained on OTSL outperforms HTML across the board, keeping high TEDs and mAP scores even on di ffi cult financial tables (FinTabNet) that contain sparse and large tables."}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 171.87, "r": 480.6, "b": 126.71, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 289]}], "orig": "Additionally, the results show that OTSL has an advantage over HTML when applied on a bigger data set like PubTables-1M and achieves significantly improved scores. Finally, OTSL achieves faster inference due to fewer decoding steps which is a result of the reduced sequence representation.", "text": "Additionally, the results show that OTSL has an advantage over HTML when applied on a bigger data set like PubTables-1M and achieves significantly improved scores. Finally, OTSL achieves faster inference due to fewer decoding steps which is a result of the reduced sequence representation."}], "pictures": [], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/5"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 139.67, "t": 454.45, "r": 475.01, "b": 322.51, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/5"}], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 160.37, "t": 341.63, "r": 168.04, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "# enc-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 207.97, "t": 341.63, "r": 215.65, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "# dec-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 239.8, "t": 347.11, "r": 278.33, "b": 355.48, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67, "t": 341.63, "r": 348.26, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 396.27, "t": 341.63, "r": 417.12, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "mAP", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 394.93, "t": 352.59, "r": 418.47, "b": 360.96, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "(0.75)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 430.77, "t": 341.63, "r": 467.14, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "Inference", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 427.15, "t": 352.59, "r": 445.07, "b": 360.96, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "time (secs)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 286.69, "t": 354.58, "r": 312.32, "b": 362.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "simple", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 320.7, "t": 354.58, "r": 353.72, "b": 362.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "complex", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 369.31, "t": 354.58, "r": 379.03, "b": 362.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "all", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 161.91, "t": 373.41, "r": 166.51, "b": 381.78, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 373.41, "r": 214.12, "b": 381.78, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 367.93, "r": 271.41, "b": 376.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 367.93, "r": 310.01, "b": 376.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.965 0.969", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 367.93, "r": 347.71, "b": 376.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.934 0.927", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 367.93, "r": 384.67, "b": 376.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.955 0.955", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 397.27, "t": 366.64, "r": 416.13, "b": 376.77, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.88 0.857", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 366.64, "r": 458.39, "b": 376.77, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "2.73 5.39", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 161.91, "t": 399.71, "r": 166.51, "b": 408.08, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 399.71, "r": 214.12, "b": 408.08, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 394.23, "r": 271.41, "b": 402.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 394.23, "r": 310.01, "b": 402.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.938 0.952", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 394.23, "r": 347.71, "b": 402.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.904 0.909", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 394.23, "r": 384.67, "b": 402.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.927 0.938", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 392.94, "r": 418.78, "b": 403.08, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.853 0.843", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 392.94, "r": 458.39, "b": 403.08, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.97 3.77", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 161.91, "t": 426.01, "r": 166.51, "b": 434.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 426.01, "r": 214.12, "b": 434.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 420.53, "r": 271.41, "b": 428.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 420.53, "r": 310.01, "b": 428.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.923 0.945", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 420.53, "r": 347.71, "b": 428.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.897 0.901", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 420.53, "r": 384.67, "b": 428.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.915 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 419.24, "r": 418.78, "b": 429.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.859 0.834", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 419.24, "r": 458.39, "b": 429.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.91 3.81", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 161.91, "t": 452.31, "r": 166.51, "b": 460.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 452.31, "r": 214.12, "b": 460.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 446.83, "r": 271.41, "b": 455.21, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 446.83, "r": 310.01, "b": 455.21, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.952 0.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 329.02, "t": 446.83, "r": 345.41, "b": 455.21, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.92 0.903", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 362.09, "t": 445.54, "r": 386.25, "b": 455.68, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.942 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 445.54, "r": 418.78, "b": 455.68, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.857 0.824", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 445.54, "r": 458.39, "b": 455.68, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.22 2", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 6, "num_cols": 8, "grid": [[{"bbox": {"l": 160.37, "t": 341.63, "r": 168.04, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "# enc-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 207.97, "t": 341.63, "r": 215.65, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "# dec-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 239.8, "t": 347.11, "r": 278.33, "b": 355.48, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67, "t": 341.63, "r": 348.26, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67, "t": 341.63, "r": 348.26, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67, "t": 341.63, "r": 348.26, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 396.27, "t": 341.63, "r": 417.12, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "mAP", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 430.77, "t": 341.63, "r": 467.14, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "Inference", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 160.37, "t": 341.63, "r": 168.04, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "# enc-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 207.97, "t": 341.63, "r": 215.65, "b": 350.0, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "# dec-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 239.8, "t": 347.11, "r": 278.33, "b": 355.48, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 286.69, "t": 354.58, "r": 312.32, "b": 362.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "simple", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 320.7, "t": 354.58, "r": 353.72, "b": 362.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "complex", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 369.31, "t": 354.58, "r": 379.03, "b": 362.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "all", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 394.93, "t": 352.59, "r": 418.47, "b": 360.96, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "(0.75)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 427.15, "t": 352.59, "r": 445.07, "b": 360.96, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "time (secs)", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.91, "t": 373.41, "r": 166.51, "b": 381.78, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 373.41, "r": 214.12, "b": 381.78, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 367.93, "r": 271.41, "b": 376.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 367.93, "r": 310.01, "b": 376.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.965 0.969", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 367.93, "r": 347.71, "b": 376.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.934 0.927", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 367.93, "r": 384.67, "b": 376.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.955 0.955", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 397.27, "t": 366.64, "r": 416.13, "b": 376.77, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.88 0.857", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 366.64, "r": 458.39, "b": 376.77, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "2.73 5.39", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.91, "t": 399.71, "r": 166.51, "b": 408.08, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 399.71, "r": 214.12, "b": 408.08, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 394.23, "r": 271.41, "b": 402.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 394.23, "r": 310.01, "b": 402.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.938 0.952", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 394.23, "r": 347.71, "b": 402.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.904 0.909", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 394.23, "r": 384.67, "b": 402.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.927 0.938", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 392.94, "r": 418.78, "b": 403.08, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.853 0.843", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 392.94, "r": 458.39, "b": 403.08, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.97 3.77", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.91, "t": 426.01, "r": 166.51, "b": 434.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 426.01, "r": 214.12, "b": 434.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 420.53, "r": 271.41, "b": 428.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 420.53, "r": 310.01, "b": 428.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.923 0.945", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 420.53, "r": 347.71, "b": 428.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.897 0.901", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 420.53, "r": 384.67, "b": 428.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.915 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 419.24, "r": 418.78, "b": 429.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.859 0.834", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 419.24, "r": 458.39, "b": 429.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.91 3.81", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.91, "t": 452.31, "r": 166.51, "b": 460.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 452.31, "r": 214.12, "b": 460.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 446.83, "r": 271.41, "b": 455.21, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 446.83, "r": 310.01, "b": 455.21, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.952 0.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 329.02, "t": 446.83, "r": 345.41, "b": 455.21, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.92 0.903", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 362.09, "t": 445.54, "r": 386.25, "b": 455.68, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.942 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 445.54, "r": 418.78, "b": 455.68, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.857 0.824", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 445.54, "r": 458.39, "b": 455.68, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.22 2", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 612.0, "height": 792.0}, "page_no": 1}}}
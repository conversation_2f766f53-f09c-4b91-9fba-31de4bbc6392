# Contribution guideline example

This is simple.

Foo *emphasis* **strong emphasis** ***both*** .

Create your feature branch: `git checkout -b feature/AmazingFeature` .

1. Pull the [**repository**](https://github.com/docling-project/docling) .
2. Create your feature branch ( `git checkout -b feature/AmazingFeature` )
3. Commit your changes ( `git commit -m 'Add some AmazingFeature'` )
4. Push to the branch ( `git push origin feature/AmazingFeature` )
5. Open a Pull Request
6. **Whole list item has same formatting**
7. List item has *mixed or partial* formatting

# *Whole heading is italic*

- **First** : Lorem ipsum.
- **Second** : Dolor `sit` amet.

Some *`formatted_code`*

## *Partially formatted* heading to\_escape `not_to_escape`

[$$E=mc^2$$](https://en.wikipedia.org/wiki/<PERSON>_Einstein)

## Table Heading

| Bold Heading   | Italic Heading   |
|----------------|------------------|
| data a         | data b           |

{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "redp5110_sampled", "origin": {"mimetype": "application/pdf", "binary_hash": 12110913468886801317, "filename": "redp5110_sampled.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/pictures/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/pictures/1"}, {"$ref": "#/pictures/2"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/pictures/3"}, {"$ref": "#/texts/13"}, {"$ref": "#/groups/0"}, {"$ref": "#/pictures/4"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/groups/1"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/pictures/5"}, {"$ref": "#/texts/40"}, {"$ref": "#/pictures/6"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}, {"$ref": "#/pictures/7"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/groups/2"}, {"$ref": "#/texts/54"}, {"$ref": "#/texts/55"}, {"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/groups/3"}, {"$ref": "#/texts/62"}, {"$ref": "#/groups/4"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}, {"$ref": "#/texts/66"}, {"$ref": "#/texts/67"}, {"$ref": "#/texts/68"}, {"$ref": "#/texts/69"}, {"$ref": "#/texts/70"}, {"$ref": "#/texts/71"}, {"$ref": "#/texts/72"}, {"$ref": "#/texts/73"}, {"$ref": "#/texts/74"}, {"$ref": "#/texts/75"}, {"$ref": "#/pictures/8"}, {"$ref": "#/texts/79"}, {"$ref": "#/texts/80"}, {"$ref": "#/texts/81"}, {"$ref": "#/texts/82"}, {"$ref": "#/groups/5"}, {"$ref": "#/texts/86"}, {"$ref": "#/texts/87"}, {"$ref": "#/texts/88"}, {"$ref": "#/texts/89"}, {"$ref": "#/tables/1"}, {"$ref": "#/texts/91"}, {"$ref": "#/texts/92"}, {"$ref": "#/groups/6"}, {"$ref": "#/texts/100"}, {"$ref": "#/texts/101"}, {"$ref": "#/texts/102"}, {"$ref": "#/texts/103"}, {"$ref": "#/texts/104"}, {"$ref": "#/texts/105"}, {"$ref": "#/texts/106"}, {"$ref": "#/texts/107"}, {"$ref": "#/texts/108"}, {"$ref": "#/texts/109"}, {"$ref": "#/tables/2"}, {"$ref": "#/texts/111"}, {"$ref": "#/texts/112"}, {"$ref": "#/texts/113"}, {"$ref": "#/pictures/9"}, {"$ref": "#/texts/165"}, {"$ref": "#/texts/166"}, {"$ref": "#/texts/167"}, {"$ref": "#/texts/168"}, {"$ref": "#/texts/169"}, {"$ref": "#/tables/3"}, {"$ref": "#/texts/171"}, {"$ref": "#/groups/7"}, {"$ref": "#/pictures/10"}, {"$ref": "#/texts/188"}, {"$ref": "#/texts/189"}, {"$ref": "#/texts/190"}, {"$ref": "#/texts/191"}, {"$ref": "#/texts/192"}, {"$ref": "#/texts/193"}, {"$ref": "#/tables/4"}, {"$ref": "#/texts/195"}, {"$ref": "#/texts/196"}, {"$ref": "#/texts/197"}, {"$ref": "#/texts/198"}, {"$ref": "#/groups/8"}, {"$ref": "#/texts/202"}, {"$ref": "#/texts/203"}, {"$ref": "#/texts/204"}, {"$ref": "#/texts/205"}, {"$ref": "#/texts/206"}, {"$ref": "#/texts/207"}, {"$ref": "#/groups/9"}, {"$ref": "#/texts/214"}, {"$ref": "#/texts/216"}, {"$ref": "#/texts/217"}, {"$ref": "#/groups/10"}, {"$ref": "#/pictures/11"}, {"$ref": "#/texts/220"}, {"$ref": "#/texts/221"}, {"$ref": "#/groups/11"}, {"$ref": "#/texts/223"}, {"$ref": "#/groups/12"}, {"$ref": "#/texts/226"}, {"$ref": "#/texts/227"}, {"$ref": "#/texts/228"}, {"$ref": "#/groups/13"}, {"$ref": "#/pictures/12"}, {"$ref": "#/texts/231"}, {"$ref": "#/texts/232"}, {"$ref": "#/groups/14"}, {"$ref": "#/pictures/13"}, {"$ref": "#/pictures/14"}, {"$ref": "#/texts/237"}, {"$ref": "#/texts/238"}, {"$ref": "#/texts/239"}, {"$ref": "#/texts/240"}, {"$ref": "#/texts/241"}, {"$ref": "#/groups/15"}, {"$ref": "#/texts/242"}, {"$ref": "#/texts/243"}, {"$ref": "#/texts/244"}, {"$ref": "#/texts/245"}, {"$ref": "#/texts/246"}, {"$ref": "#/texts/247"}, {"$ref": "#/texts/248"}, {"$ref": "#/pictures/15"}, {"$ref": "#/pictures/16"}, {"$ref": "#/texts/252"}, {"$ref": "#/texts/253"}, {"$ref": "#/texts/254"}, {"$ref": "#/texts/255"}, {"$ref": "#/texts/256"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}, {"$ref": "#/texts/17"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/34"}, {"$ref": "#/texts/35"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/60"}, {"$ref": "#/texts/61"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/63"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/5", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/83"}, {"$ref": "#/texts/84"}, {"$ref": "#/texts/85"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/6", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/93"}, {"$ref": "#/texts/94"}, {"$ref": "#/texts/95"}, {"$ref": "#/texts/96"}, {"$ref": "#/texts/97"}, {"$ref": "#/texts/98"}, {"$ref": "#/texts/99"}], "content_layer": "body", "name": "group", "label": "key_value_area"}, {"self_ref": "#/groups/7", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/172"}, {"$ref": "#/texts/173"}, {"$ref": "#/texts/174"}, {"$ref": "#/texts/175"}, {"$ref": "#/texts/176"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/8", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/199"}, {"$ref": "#/texts/200"}, {"$ref": "#/texts/201"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/9", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/208"}, {"$ref": "#/texts/209"}, {"$ref": "#/texts/210"}, {"$ref": "#/texts/211"}, {"$ref": "#/texts/212"}, {"$ref": "#/texts/213"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/10", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/218"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/11", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/222"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/12", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/224"}, {"$ref": "#/texts/225"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/13", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/229"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/14", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/233"}, {"$ref": "#/texts/235"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/15", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "name": "group", "label": "form_area"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 287.82, "t": 763.45, "r": 418.84, "b": 741.25, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "Front cover", "text": "Front cover"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 35.7, "t": 707.41, "r": 587.8, "b": 626.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Row and Column Access Control Support in IBM DB2 for i", "text": "Row and Column Access Control Support in IBM DB2 for i", "level": 1}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 497.7, "t": 216.29, "r": 581.39, "b": 93.59, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 115]}], "orig": "<PERSON><PERSON><PERSON>", "text": "<PERSON><PERSON><PERSON>"}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 36.12, "t": 495.86, "r": 216.0, "b": 466.44, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "Implement roles and separation of duties", "text": "Implement roles and separation of duties"}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 35.76, "t": 441.86, "r": 202.45, "b": 412.44, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "Leverage row permissions on the database", "text": "Leverage row permissions on the database"}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 36.06, "t": 387.86, "r": 195.27, "b": 358.44, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "Protect columns by defining column masks", "text": "Protect columns by defining column masks"}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 314.7, "t": 80.49, "r": 580.49, "b": 18.23, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "Redpaper", "text": "Redpaper"}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 1, "bbox": {"l": 36.9, "t": 40.77, "r": 164.46, "b": 26.89, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 17]}], "orig": "ibm.com /redbooks", "text": "ibm.com /redbooks"}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 2, "bbox": {"l": 64.8, "t": 718.15, "r": 168.74, "b": 695.95, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "Contents", "text": "Contents", "level": 1}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 2, "bbox": {"l": 64.8, "t": 36.46, "r": 257.25, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "' Copyright IBM Corp. 2014. All rights reserved.", "text": "' Copyright IBM Corp. 2014. All rights reserved."}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 2, "bbox": {"l": 538.86, "t": 37.15, "r": 547.21, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "iii", "text": "iii"}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 64.8, "t": 717.52, "r": 235.86, "b": 706.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "DB2 for i Center of Excellence", "text": "DB2 for i Center of Excellence"}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 94.13, "t": 653.55, "r": 234.0, "b": 636.62, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 52]}], "orig": "Solution Brief IBM Systems Lab Services and Training", "text": "Solution Brief IBM Systems Lab Services and Training"}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 144.89, "t": 464.54, "r": 188.89, "b": 455.19, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "Highlights", "text": "Highlights", "level": 1}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 144.89, "t": 448.6, "r": 242.97, "b": 433.33, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 213]}], "orig": "/g115/g3 /g40/g81/g75/g68/g81/g70/g72/g3 /g87/g75/g72/g3 /g83/g72/g85/g73/g82/g85/g80/g68/g81/g70/g72/g3 /g82/g73/g3 /g92/g82/g88/g85/g3 /g71/g68/g87/g68/g69/g68/g86/g72/g3 /g82/g83/g72/g85/g68/g87/g76/g82/g81/g86", "text": "/g115/g3 /g40/g81/g75/g68/g81/g70/g72/g3 /g87/g75/g72/g3 /g83/g72/g85/g73/g82/g85/g80/g68/g81/g70/g72/g3 /g82/g73/g3 /g92/g82/g88/g85/g3 /g71/g68/g87/g68/g69/g68/g86/g72/g3 /g82/g83/g72/g85/g68/g87/g76/g82/g81/g86", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 144.89, "t": 425.88, "r": 259.33, "b": 402.78, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 352]}], "orig": "/g115/g3 /g40/g68/g85/g81/g3 /g74/g85/g72/g68/g87/g72/g85/g3 /g85 /g72/g87/g88/g85/g81/g3 /g82/g81/g3 /g44/g55/g3 /g83/g85 /g82/g77/g72/g70/g87/g86/g3 /g87/g75/g85 /g82/g88/g74/g75/g3 /g80/g82/g71/g72/g85/g81/g76/g93/g68/g87/g76/g82/g81/g3 /g82/g73/g3 /g71/g68/g87/g68/g69/g68/g86/g72/g3 /g68/g81/g71/g3 /g68/g83/g83/g79/g76/g70/g68/g87/g76/g82/g81/g86", "text": "/g115/g3 /g40/g68/g85/g81/g3 /g74/g85/g72/g68/g87/g72/g85/g3 /g85 /g72/g87/g88/g85/g81/g3 /g82/g81/g3 /g44/g55/g3 /g83/g85 /g82/g77/g72/g70/g87/g86/g3 /g87/g75/g85 /g82/g88/g74/g75/g3 /g80/g82/g71/g72/g85/g81/g76/g93/g68/g87/g76/g82/g81/g3 /g82/g73/g3 /g71/g68/g87/g68/g69/g68/g86/g72/g3 /g68/g81/g71/g3 /g68/g83/g83/g79/g76/g70/g68/g87/g76/g82/g81/g86", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 144.89, "t": 395.34, "r": 249.94, "b": 380.07, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 269]}], "orig": "/g115/g3 /g53/g72/g79/g92/g3 /g82/g81/g3 /g44/g37/g48/g3 /g72/g91/g83/g72/g85/g87/g3 /g70/g82/g81/g86/g88/g79/g87/g76/g81/g74/g15/g3 /g86/g78/g76/g79/g79/g86/g3 /g86/g75/g68/g85/g76/g81/g74/g3 /g68/g81/g71/g3 /g85/g72/g81/g82/g90/g81/g3 /g86/g72/g85/g89/g76/g70/g72/g86", "text": "/g115/g3 /g53/g72/g79/g92/g3 /g82/g81/g3 /g44/g37/g48/g3 /g72/g91/g83/g72/g85/g87/g3 /g70/g82/g81/g86/g88/g79/g87/g76/g81/g74/g15/g3 /g86/g78/g76/g79/g79/g86/g3 /g86/g75/g68/g85/g76/g81/g74/g3 /g68/g81/g71/g3 /g85/g72/g81/g82/g90/g81/g3 /g86/g72/g85/g89/g76/g70/g72/g86", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 144.89, "t": 372.62, "r": 234.35, "b": 357.35, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 246]}], "orig": "/g115/g3 /g55 /g68/g78/g72/g3 /g68/g71/g89/g68/g81/g87/g68/g74/g72/g3 /g82/g73/g3 /g68/g70/g70/g72/g86/g86/g3 /g87/g82/g3 /g68/g3 /g90/g82/g85/g79/g71/g90/g76/g71/g72/g3 /g86/g82/g88/g85/g70/g72/g3 /g82/g73/g3 /g72/g91/g83/g72/g85/g87/g76/g86/g72", "text": "/g115/g3 /g55 /g68/g78/g72/g3 /g68/g71/g89/g68/g81/g87/g68/g74/g72/g3 /g82/g73/g3 /g68/g70/g70/g72/g86/g86/g3 /g87/g82/g3 /g68/g3 /g90/g82/g85/g79/g71/g90/g76/g71/g72/g3 /g86/g82/g88/g85/g70/g72/g3 /g82/g73/g3 /g72/g91/g83/g72/g85/g87/g76/g86/g72", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 461.09, "t": 653.59, "r": 506.26, "b": 646.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "Power Services", "text": "Power Services"}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 558.39, "r": 464.19, "b": 515.38, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "DB2 for i Center of Excellence", "text": "DB2 for i Center of Excellence", "level": 1}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 517.47, "r": 483.4, "b": 503.76, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 49]}], "orig": "Expert help to achieve your business requirements", "text": "Expert help to achieve your business requirements"}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 476.12, "r": 443.28, "b": 469.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 37]}], "orig": "We build confident, satisfied clients", "text": "We build confident, satisfied clients", "level": 1}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 464.62, "r": 488.15, "b": 447.04, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 122]}], "orig": "No one else has the vast consulting experiences, skills sharing and renown service offerings to do what we can do for you.", "text": "No one else has the vast consulting experiences, skills sharing and renown service offerings to do what we can do for you."}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 434.67, "r": 367.86, "b": 427.27, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 27]}], "orig": "Because no one else is IBM.", "text": "Because no one else is IBM."}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 414.9, "r": 500.32, "b": 366.78, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 318]}], "orig": "With combined experiences and direct access to development groups, we're the experts in IBM DB2® for i. The DB2 for i Center of Excellence (CoE) can help you achieve-perhaps reexamine and exceed-your business requirements and gain more confidence and satisfaction in IBM product data management products and solutions.", "text": "With combined experiences and direct access to development groups, we're the experts in IBM DB2® for i. The DB2 for i Center of Excellence (CoE) can help you achieve-perhaps reexamine and exceed-your business requirements and gain more confidence and satisfaction in IBM product data management products and solutions."}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 354.15, "r": 434.83, "b": 347.17, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "Who we are, some of what we do", "text": "Who we are, some of what we do", "level": 1}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 342.65, "r": 434.56, "b": 335.25, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 46]}], "orig": "Global CoE engagements cover topics including:", "text": "Global CoE engagements cover topics including:"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 322.88, "r": 401.64, "b": 315.48, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 83]}], "orig": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Database performance and scalability", "text": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Database performance and scalability", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 312.7, "r": 425.07, "b": 305.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 89]}], "orig": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Advanced SQL knowledge and skills transfer", "text": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Advanced SQL knowledge and skills transfer", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 302.52, "r": 392.23, "b": 295.11, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 82]}], "orig": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Business intelligence and analytics", "text": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Business intelligence and analytics", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 292.33, "r": 340.02, "b": 284.93, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> DB2 Web Query", "text": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> DB2 Web Query", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 282.15, "r": 504.27, "b": 274.75, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 117]}], "orig": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Query/400 modernization for better reporting and analysis capabilities", "text": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Query/400 modernization for better reporting and analysis capabilities", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 271.97, "r": 423.08, "b": 264.56, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 88]}], "orig": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Database modernization and re-engineering", "text": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Database modernization and re-engineering", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 261.79, "r": 399.73, "b": 254.38, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 83]}], "orig": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Data-centric architecture and design", "text": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Data-centric architecture and design", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 251.6, "r": 466.85, "b": 244.2, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 103]}], "orig": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Extremely large database and overcoming limits to growth", "text": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> Extremely large database and overcoming limits to growth", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 280.24, "t": 241.42, "r": 382.28, "b": 234.02, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 75]}], "orig": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> ISV education and enablement", "text": "rGLYPH<c=1,font=/NKDKKL+JansonTextLTStd-Roman> ISV education and enablement", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 64.8, "t": 718.15, "r": 151.47, "b": 695.95, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "Preface", "text": "Preface", "level": 1}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 136.8, "t": 659.35, "r": 547.56, "b": 590.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 469]}], "orig": "This IBMfi Redpaper™ publication provides information about the IBM i 7.2 feature of IBM DB2fi for i Row and Column Access Control (RCAC). It offers a broad description of the function and advantages of controlling access to data in a comprehensive and transparent way. This publication helps you understand the capabilities of RCAC and provides examples of defining, creating, and implementing the row permissions and column masks in a relational database environment.", "text": "This IBMfi Redpaper™ publication provides information about the IBM i 7.2 feature of IBM DB2fi for i Row and Column Access Control (RCAC). It offers a broad description of the function and advantages of controlling access to data in a comprehensive and transparent way. This publication helps you understand the capabilities of RCAC and provides examples of defining, creating, and implementing the row permissions and column masks in a relational database environment."}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 136.8, "t": 577.39, "r": 546.41, "b": 532.18, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 309]}], "orig": "This paper is intended for database engineers, data-centric application developers, and security officers who want to design and implement RCAC as a part of their data control and governance policy. A solid background in IBM i object level security, DB2 for i relational database concepts, and SQL is assumed.", "text": "This paper is intended for database engineers, data-centric application developers, and security officers who want to design and implement RCAC as a part of their data control and governance policy. A solid background in IBM i object level security, DB2 for i relational database concepts, and SQL is assumed."}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 136.8, "t": 471.37, "r": 547.25, "b": 450.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 172]}], "orig": "This paper was produced by the IBM DB2 for i Center of Excellence team in partnership with the International Technical Support Organization (ITSO), Rochester, Minnesota US.", "text": "This paper was produced by the IBM DB2 for i Center of Excellence team in partnership with the International Technical Support Organization (ITSO), Rochester, Minnesota US."}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 263.4, "t": 416.35, "r": 541.74, "b": 275.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 684]}], "orig": "<PERSON> is a senior DB2 consultant on the DB2 for i Center of Excellence team in the IBM Lab Services and Training organization. His primary role is training and implementation services for IBM DB2 Web Query for i and business analytics. <PERSON> began his career with IBM 30 years ago in the IBM Rochester Development Lab, where he developed cooperative processing products that paired IBM PCs with IBM S/36 and AS/.400 systems. In the years since, <PERSON> has held numerous technical roles, including independent software vendors technical support on a broad range of IBM technologies and products, and supporting customers in the IBM Executive Briefing Center and IBM Project Office.", "text": "<PERSON> is a senior DB2 consultant on the DB2 for i Center of Excellence team in the IBM Lab Services and Training organization. His primary role is training and implementation services for IBM DB2 Web Query for i and business analytics. <PERSON> began his career with IBM 30 years ago in the IBM Rochester Development Lab, where he developed cooperative processing products that paired IBM PCs with IBM S/36 and AS/.400 systems. In the years since, <PERSON> has held numerous technical roles, including independent software vendors technical support on a broad range of IBM technologies and products, and supporting customers in the IBM Executive Briefing Center and IBM Project Office."}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 263.4, "t": 264.37, "r": 541.58, "b": 111.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 726]}], "orig": "<PERSON><PERSON><PERSON> is a Senior IT Specialist at STG Lab Services and Training in Rochester, Minnesota. He writes extensively and teaches IBM classes worldwide in all areas of DB2 for i. Before joining STG Lab Services, he worked in the ITSO for nine years writing multiple IBM Redbooksfi publications. He also worked for IBM Colombia as an IBM AS/400fi IT Specialist doing presales support for the Andean countries. He has 28 years of experience in the computing field and has taught database classes in Colombian universities. He holds a Master's degree in Computer Science from EAFIT, Colombia. His areas of expertise are database technology, performance, and data warehousing. Hernan<PERSON> can be <NAME_EMAIL> .", "text": "<PERSON><PERSON><PERSON> is a Senior IT Specialist at STG Lab Services and Training in Rochester, Minnesota. He writes extensively and teaches IBM classes worldwide in all areas of DB2 for i. Before joining STG Lab Services, he worked in the ITSO for nine years writing multiple IBM Redbooksfi publications. He also worked for IBM Colombia as an IBM AS/400fi IT Specialist doing presales support for the Andean countries. He has 28 years of experience in the computing field and has taught database classes in Colombian universities. He holds a Master's degree in Computer Science from EAFIT, Colombia. His areas of expertise are database technology, performance, and data warehousing. Hernan<PERSON> can be <NAME_EMAIL> ."}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 64.8, "t": 503.7, "r": 125.34, "b": 488.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "Authors", "text": "Authors", "level": 1}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 4, "bbox": {"l": 64.8, "t": 36.46, "r": 257.25, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "' Copyright IBM Corp. 2014. All rights reserved.", "text": "' Copyright IBM Corp. 2014. All rights reserved."}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 4, "bbox": {"l": 538.86, "t": 37.15, "r": 547.21, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "xi", "text": "xi"}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 500.4, "t": 698.83, "r": 522.62, "b": 661.87, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 81.0, "t": 523.46, "r": 115.16, "b": 517.02, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "Chapter 1.", "text": "Chapter 1."}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 5, "bbox": {"l": 136.8, "t": 537.11, "r": 549.16, "b": 482.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "Securing and protecting IBM DB2 data", "text": "Securing and protecting IBM DB2 data", "level": 1}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 136.8, "t": 443.29, "r": 547.5, "b": 362.08, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 641]}], "orig": "Recent news headlines are filled with reports of data breaches and cyber-attacks impacting global businesses of all sizes. The Identity Theft Resource Center 1  reports that almost 5000 data breaches have occurred since 2005, exposing over 600 million records of data. The financial cost of these data breaches is skyrocketing. Studies from the Ponemon Institute 2 revealed that the average cost of a data breach increased in 2013 by 15% globally and resulted in a brand equity loss of $9.4 million per attack. The average cost that is incurred for each lost record containing sensitive information increased more than 9% to $145 per record.", "text": "Recent news headlines are filled with reports of data breaches and cyber-attacks impacting global businesses of all sizes. The Identity Theft Resource Center 1  reports that almost 5000 data breaches have occurred since 2005, exposing over 600 million records of data. The financial cost of these data breaches is skyrocketing. Studies from the Ponemon Institute 2 revealed that the average cost of a data breach increased in 2013 by 15% globally and resulted in a brand equity loss of $9.4 million per attack. The average cost that is incurred for each lost record containing sensitive information increased more than 9% to $145 per record."}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 136.8, "t": 349.27, "r": 527.16, "b": 304.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 304]}], "orig": "Businesses must make a serious effort to secure their data and recognize that securing information assets is a cost of doing business. In many parts of the world and in many industries, securing the data is required by law and subject to audits. Data security is no longer an option; it is a requirement.", "text": "Businesses must make a serious effort to secure their data and recognize that securing information assets is a cost of doing business. In many parts of the world and in many industries, securing the data is required by law and subject to audits. Data security is no longer an option; it is a requirement."}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 136.8, "t": 291.31, "r": 547.4, "b": 270.1, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 122]}], "orig": "This chapter describes how you can secure and protect data in DB2 for i. The following topics are covered in this chapter:", "text": "This chapter describes how you can secure and protect data in DB2 for i. The following topics are covered in this chapter:"}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 136.8, "t": 262.27, "r": 250.2, "b": 253.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "/SM590000 Security fundamentals", "text": "/SM590000 Security fundamentals", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 136.8, "t": 250.27, "r": 282.95, "b": 241.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 41]}], "orig": "/SM590000 Current state of IBM i security", "text": "/SM590000 Current state of IBM i security", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 5, "bbox": {"l": 136.8, "t": 238.27, "r": 264.87, "b": 229.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 37]}], "orig": "/SM590000 DB2 for i security controls", "text": "/SM590000 DB2 for i security controls", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 5, "bbox": {"l": 136.8, "t": 76.16, "r": 258.33, "b": 67.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "1   http://www.idtheftcenter.org", "text": "1   http://www.idtheftcenter.org"}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "footnote", "prov": [{"page_no": 5, "bbox": {"l": 136.8, "t": 66.43, "r": 234.06, "b": 57.03, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "2 http://www.ponemon.org /", "text": "2 http://www.ponemon.org /"}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 5, "bbox": {"l": 64.8, "t": 36.46, "r": 257.25, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "' Copyright IBM Corp. 2014. All rights reserved.", "text": "' Copyright IBM Corp. 2014. All rights reserved."}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 5, "bbox": {"l": 541.68, "t": 37.15, "r": 547.22, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 64.8, "t": 717.66, "r": 267.39, "b": 702.9, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "1.1  Security fundamentals", "text": "1.1  Security fundamentals", "level": 1}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 136.8, "t": 685.39, "r": 545.01, "b": 664.18, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 133]}], "orig": "Before reviewing database security techniques, there are two fundamental steps in securing information assets that must be described:", "text": "Before reviewing database security techniques, there are two fundamental steps in securing information assets that must be described:"}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 136.8, "t": 656.51, "r": 547.35, "b": 611.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 355]}], "orig": "/SM590000 First, and most important, is the definition of a company's security policy . Without a security policy, there is no definition of what are acceptable practices for using, accessing, and storing information by who, what, when, where, and how. A security policy should minimally address three things: confidentiality, integrity, and availability.", "text": "/SM590000 First, and most important, is the definition of a company's security policy . Without a security policy, there is no definition of what are acceptable practices for using, accessing, and storing information by who, what, when, where, and how. A security policy should minimally address three things: confidentiality, integrity, and availability.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 151.2, "t": 603.37, "r": 547.57, "b": 522.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 587]}], "orig": "The monitoring and assessment of adherence to the security policy determines whether your security strategy is working. Often, IBM security consultants are asked to perform security assessments for companies without regard to the security policy. Although these assessments can be useful for observing how the system is defined and how data is being accessed, they cannot determine the level of security without a security policy. Without a security policy, it really is not an assessment as much as it is a baseline for monitoring the changes in the security settings that are captured.", "text": "The monitoring and assessment of adherence to the security policy determines whether your security strategy is working. Often, IBM security consultants are asked to perform security assessments for companies without regard to the security policy. Although these assessments can be useful for observing how the system is defined and how data is being accessed, they cannot determine the level of security without a security policy. Without a security policy, it really is not an assessment as much as it is a baseline for monitoring the changes in the security settings that are captured.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 151.2, "t": 514.39, "r": 542.0, "b": 505.18, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 90]}], "orig": "A security policy is what defines whether the system and its settings are secure (or not).", "text": "A security policy is what defines whether the system and its settings are secure (or not)."}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 136.8, "t": 497.51, "r": 547.41, "b": 416.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 567]}], "orig": "/SM590000 The second fundamental in securing data assets is the use of resource security . If implemented properly, resource security prevents data breaches from both internal and external intrusions. Resource security controls are closely tied to the part of the security policy that defines who should have access to what information resources. A hacker might be good enough to get through your company firewalls and sift his way through to your system, but if they do not have explicit access to your database, the hacker cannot compromise your information assets.", "text": "/SM590000 The second fundamental in securing data assets is the use of resource security . If implemented properly, resource security prevents data breaches from both internal and external intrusions. Resource security controls are closely tied to the part of the security policy that defines who should have access to what information resources. A hacker might be good enough to get through your company firewalls and sift his way through to your system, but if they do not have explicit access to your database, the hacker cannot compromise your information assets.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 136.8, "t": 403.39, "r": 535.37, "b": 382.18, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 179]}], "orig": "With your eyes now open to the importance of securing information assets, the rest of this chapter reviews the methods that are available for securing database resources on IBM i.", "text": "With your eyes now open to the importance of securing information assets, the rest of this chapter reviews the methods that are available for securing database resources on IBM i."}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 64.8, "t": 353.7, "r": 323.37, "b": 338.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "1.2  Current state of IBM i security", "text": "1.2  Current state of IBM i security", "level": 1}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 136.8, "t": 321.37, "r": 547.57, "b": 276.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 306]}], "orig": "Because of the inherently secure nature of IBM i, many clients rely on the default system settings to protect their business data that is stored in DB2 for i. In most cases, this means no data protection because the default setting for the Create default public authority (QCRTAUT) system value is *CHANGE.", "text": "Because of the inherently secure nature of IBM i, many clients rely on the default system settings to protect their business data that is stored in DB2 for i. In most cases, this means no data protection because the default setting for the Create default public authority (QCRTAUT) system value is *CHANGE."}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 136.8, "t": 263.35, "r": 547.59, "b": 206.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 405]}], "orig": "Even more disturbing is that many IBM i clients remain in this state, despite the news headlines and the significant costs that are involved with databases being compromised. This default security configuration makes it quite challenging to implement basic security policies. A tighter implementation is required if you really want to protect one of your company's most valuable assets, which is the data.", "text": "Even more disturbing is that many IBM i clients remain in this state, despite the news headlines and the significant costs that are involved with databases being compromised. This default security configuration makes it quite challenging to implement basic security policies. A tighter implementation is required if you really want to protect one of your company's most valuable assets, which is the data."}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 136.8, "t": 193.33, "r": 547.65, "b": 112.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 640]}], "orig": "Traditionally, IBM i applications have employed menu-based security to counteract this default configuration that gives all users access to the data. The theory is that data is protected by the menu options controlling what database operations that the user can perform. This approach is ineffective, even if the user profile is restricted from running interactive commands. The reason is that in today's connected world there are a multitude of interfaces into the system, from web browsers to PC clients, that bypass application menus. If there are no object-level controls, users of these newer interfaces have an open door to your data.", "text": "Traditionally, IBM i applications have employed menu-based security to counteract this default configuration that gives all users access to the data. The theory is that data is protected by the menu options controlling what database operations that the user can perform. This approach is ineffective, even if the user profile is restricted from running interactive commands. The reason is that in today's connected world there are a multitude of interfaces into the system, from web browsers to PC clients, that bypass application menus. If there are no object-level controls, users of these newer interfaces have an open door to your data."}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 6, "bbox": {"l": 64.8, "t": 37.15, "r": 72.82, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 6, "bbox": {"l": 87.84, "t": 36.46, "r": 328.56, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Row and Column Access Control Support in IBM DB2 for i", "text": "Row and Column Access Control Support in IBM DB2 for i"}, {"self_ref": "#/texts/71", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 136.8, "t": 720.49, "r": 544.31, "b": 639.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 589]}], "orig": "Many businesses are trying to limit data access to a need-to-know basis. This security goal means that users should be given access only to the minimum set of data that is required to perform their job. Often, users with object-level access are given access to row and column values that are beyond what their business task requires because that object-level security provides an all-or-nothing solution. For example, object-level controls allow a manager to access data about all employees. Most security policies limit a manager to accessing data only for the employees that they manage.", "text": "Many businesses are trying to limit data access to a need-to-know basis. This security goal means that users should be given access only to the minimum set of data that is required to perform their job. Often, users with object-level access are given access to row and column values that are beyond what their business task requires because that object-level security provides an all-or-nothing solution. For example, object-level controls allow a manager to access data about all employees. Most security policies limit a manager to accessing data only for the employees that they manage."}, {"self_ref": "#/texts/72", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 7, "bbox": {"l": 64.8, "t": 618.66, "r": 301.46, "b": 606.68, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 38]}], "orig": "1.3.1  Existing row and column control", "text": "1.3.1  Existing row and column control", "level": 1}, {"self_ref": "#/texts/73", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 136.8, "t": 592.51, "r": 541.58, "b": 535.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 377]}], "orig": "Some IBM i clients have tried augmenting the all-or-nothing object-level security with SQL views (or logical files) and application logic, as shown in Figure 1-2. However, application-based logic is easy to bypass with all of the different data access interfaces that are provided by the IBM i operating system, such as Open Database Connectivity (ODBC) and System i Navigator.", "text": "Some IBM i clients have tried augmenting the all-or-nothing object-level security with SQL views (or logical files) and application logic, as shown in Figure 1-2. However, application-based logic is easy to bypass with all of the different data access interfaces that are provided by the IBM i operating system, such as Open Database Connectivity (ODBC) and System i Navigator."}, {"self_ref": "#/texts/74", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 136.8, "t": 522.49, "r": 547.69, "b": 477.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 340]}], "orig": "Using SQL views to limit access to a subset of the data in a table also has its own set of challenges. First, there is the complexity of managing all of the SQL view objects that are used for securing data access. Second, scaling a view-based security solution can be difficult as the amount of data grows and the number of users increases.", "text": "Using SQL views to limit access to a subset of the data in a table also has its own set of challenges. First, there is the complexity of managing all of the SQL view objects that are used for securing data access. Second, scaling a view-based security solution can be difficult as the amount of data grows and the number of users increases."}, {"self_ref": "#/texts/75", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 136.8, "t": 464.47, "r": 547.54, "b": 431.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 247]}], "orig": "Even if you are willing to live with these performance and management issues, a user with *ALLOBJ access still can directly access all of the data in the underlying DB2 table and easily bypass the security controls that are built into an SQL view.", "text": "Even if you are willing to live with these performance and management issues, a user with *ALLOBJ access still can directly access all of the data in the underlying DB2 table and easily bypass the security controls that are built into an SQL view."}, {"self_ref": "#/texts/76", "parent": {"$ref": "#/pictures/8"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 7, "bbox": {"l": 136.8, "t": 100.18, "r": 316.46, "b": 91.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 45]}], "orig": "Figure 1-2   Existing row and column controls", "text": "Figure 1-2   Existing row and column controls"}, {"self_ref": "#/texts/77", "parent": {"$ref": "#/pictures/8"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 180.96, "t": 408.75, "r": 212.54, "b": 402.61, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "User with", "text": "User with"}, {"self_ref": "#/texts/78", "parent": {"$ref": "#/pictures/8"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 170.01, "t": 401.25, "r": 226.93, "b": 395.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "*ALLOBJ access", "text": "*ALLOBJ access"}, {"self_ref": "#/texts/79", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 7, "bbox": {"l": 64.8, "t": 37.15, "r": 72.82, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "4", "text": "4"}, {"self_ref": "#/texts/80", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 7, "bbox": {"l": 87.84, "t": 36.46, "r": 328.56, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Row and Column Access Control Support in IBM DB2 for i", "text": "Row and Column Access Control Support in IBM DB2 for i"}, {"self_ref": "#/texts/81", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 8, "bbox": {"l": 64.8, "t": 720.66, "r": 335.49, "b": 708.68, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 39]}], "orig": "2.1.6  Change Function Usage CL command", "text": "2.1.6  Change Function Usage CL command", "level": 1}, {"self_ref": "#/texts/82", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 694.51, "r": 547.25, "b": 685.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 90]}], "orig": "The following CL commands can be used to work with, display, or change function usage IDs:", "text": "The following CL commands can be used to work with, display, or change function usage IDs:"}, {"self_ref": "#/texts/83", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 677.47, "r": 301.52, "b": 668.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 43]}], "orig": "/SM590000 Work Function Usage ( WRKFCNUSG )", "text": "/SM590000 Work Function Usage ( WRKFCNUSG )", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/84", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 665.47, "r": 313.4, "b": 656.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 45]}], "orig": "/SM590000 Change Function Usage ( CHGFCNUSG )", "text": "/SM590000 Change Function Usage ( CHGFCNUSG )", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/85", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 653.47, "r": 310.82, "b": 644.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 46]}], "orig": "/SM590000 Display Function Usage ( DSPFCNUSG )", "text": "/SM590000 Display Function Usage ( DSPFCNUSG )", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/86", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 631.51, "r": 512.55, "b": 610.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 126]}], "orig": "For example, the following CHGFCNUSG command shows granting authorization to user HBEDOYA to administer and manage RCAC rules:", "text": "For example, the following CHGFCNUSG command shows granting authorization to user HBEDOYA to administer and manage RCAC rules:"}, {"self_ref": "#/texts/87", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 602.02, "r": 441.6, "b": 593.62, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 61]}], "orig": "CHGFCNUSG FCNID(QIBM_DB_SECADM) USER(HBEDOYA) USAGE(*ALLOWED)", "text": "CHGFCNUSG FCNID(QIBM_DB_SECADM) USER(HBEDOYA) USAGE(*ALLOWED)"}, {"self_ref": "#/texts/88", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 8, "bbox": {"l": 64.8, "t": 572.64, "r": 544.46, "b": 560.66, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 73]}], "orig": "2.1.7  Verifying function usage IDs for RCAC with the FUNCTION_USAGE view", "text": "2.1.7  Verifying function usage IDs for RCAC with the FUNCTION_USAGE view", "level": 1}, {"self_ref": "#/texts/89", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 546.49, "r": 519.53, "b": 525.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 130]}], "orig": "The FUNCTION_USAGE view contains function usage configuration details. Table 2-1 describes the columns in the FUNCTION_USAGE view.", "text": "The FUNCTION_USAGE view contains function usage configuration details. Table 2-1 describes the columns in the FUNCTION_USAGE view."}, {"self_ref": "#/texts/90", "parent": {"$ref": "#/tables/1"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 512.44, "r": 283.95, "b": 504.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "Table 2-1   FUNCTION_USAGE view", "text": "Table 2-1   FUNCTION_USAGE view"}, {"self_ref": "#/texts/91", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 339.49, "r": 547.59, "b": 318.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 112]}], "orig": "To discover who has authorization to define and manage RCAC, you can use the query that is shown in Example 2-1.", "text": "To discover who has authorization to define and manage RCAC, you can use the query that is shown in Example 2-1."}, {"self_ref": "#/texts/92", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 305.44, "r": 462.37, "b": 297.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 76]}], "orig": "Example 2-1   Query to determine who has authority to define and manage RCAC", "text": "Example 2-1   Query to determine who has authority to define and manage RCAC"}, {"self_ref": "#/texts/93", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 288.04, "r": 251.7, "b": 279.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "SELECT     function_id,", "text": "SELECT     function_id,"}, {"self_ref": "#/texts/94", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 276.04, "r": 241.74, "b": 267.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "user_name,", "text": "user_name,"}, {"self_ref": "#/texts/95", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 264.04, "r": 221.7, "b": 255.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "usage,", "text": "usage,"}, {"self_ref": "#/texts/96", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 252.04, "r": 236.7, "b": 243.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "user_type", "text": "user_type"}, {"self_ref": "#/texts/97", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 240.04, "r": 261.72, "b": 231.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 25]}], "orig": "FROM       function_usage", "text": "FROM       function_usage"}, {"self_ref": "#/texts/98", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 228.04, "r": 331.68, "b": 219.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 39]}], "orig": "WHERE      function_id='QIBM_DB_SECADM'", "text": "WHERE      function_id='QIBM_DB_SECADM'"}, {"self_ref": "#/texts/99", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 216.04, "r": 241.74, "b": 207.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "ORDER BY   user_name;", "text": "ORDER BY   user_name;"}, {"self_ref": "#/texts/100", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 8, "bbox": {"l": 64.8, "t": 171.78, "r": 249.58, "b": 157.02, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 25]}], "orig": "2.2  Separation of duties", "text": "2.2  Separation of duties", "level": 1}, {"self_ref": "#/texts/101", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 136.8, "t": 139.45, "r": 547.59, "b": 82.24, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 463]}], "orig": "Separation of duties helps businesses comply with industry regulations or organizational requirements and simplifies the management of authorities. Separation of duties is commonly used to prevent fraudulent activities or errors by a single person. It provides the ability for administrative functions to be divided across individuals without overlapping responsibilities, so that one user does not possess unlimited authority, such as with the *ALLOBJ authority.", "text": "Separation of duties helps businesses comply with industry regulations or organizational requirements and simplifies the management of authorities. Separation of duties is commonly used to prevent fraudulent activities or errors by a single person. It provides the ability for administrative functions to be divided across individuals without overlapping responsibilities, so that one user does not possess unlimited authority, such as with the *ALLOBJ authority."}, {"self_ref": "#/texts/102", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 8, "bbox": {"l": 64.8, "t": 37.15, "r": 78.4, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "10", "text": "10"}, {"self_ref": "#/texts/103", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 8, "bbox": {"l": 93.42, "t": 36.46, "r": 334.26, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Row and Column Access Control Support in IBM DB2 for i", "text": "Row and Column Access Control Support in IBM DB2 for i"}, {"self_ref": "#/texts/104", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 136.8, "t": 720.49, "r": 542.7, "b": 651.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 516]}], "orig": "For example, assume that a business has assigned the duty to manage security on IBM i to <PERSON>. Before release IBM i 7.2, to grant privileges, <PERSON> had to have the same privileges <PERSON> was granting to others. Therefore, to grant *USE privileges to the PAYROLL table, <PERSON> had to have *OBJMGT and *USE authority (or a higher level of authority, such as *ALLOBJ). This requirement allowed <PERSON> to access the data in the PAYROLL table even though <PERSON>'s job description was only to manage its security.", "text": "For example, assume that a business has assigned the duty to manage security on IBM i to <PERSON>. Before release IBM i 7.2, to grant privileges, <PERSON> had to have the same privileges <PERSON> was granting to others. Therefore, to grant *USE privileges to the PAYROLL table, <PERSON> had to have *OBJMGT and *USE authority (or a higher level of authority, such as *ALLOBJ). This requirement allowed <PERSON> to access the data in the PAYROLL table even though <PERSON>'s job description was only to manage its security."}, {"self_ref": "#/texts/105", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 136.8, "t": 638.47, "r": 547.67, "b": 593.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 285]}], "orig": "In IBM i 7.2, the QIBM_DB_SECADM function usage grants authorities, revokes authorities, changes ownership, or changes the primary group without giving access to the object or, in the case of a database table, to the data that is in the table or allowing other operations on the table.", "text": "In IBM i 7.2, the QIBM_DB_SECADM function usage grants authorities, revokes authorities, changes ownership, or changes the primary group without giving access to the object or, in the case of a database table, to the data that is in the table or allowing other operations on the table."}, {"self_ref": "#/texts/106", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 136.8, "t": 580.51, "r": 538.66, "b": 559.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 129]}], "orig": "QIBM_DB_SECADM function usage can be granted only by a user with *SECADM special authority and can be given to a user or a group.", "text": "QIBM_DB_SECADM function usage can be granted only by a user with *SECADM special authority and can be given to a user or a group."}, {"self_ref": "#/texts/107", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 136.8, "t": 546.49, "r": 545.8, "b": 513.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 204]}], "orig": "QIBM_DB_SECADM also is responsible for administering RCAC, which restricts which rows a user is allowed to access in a table and whether a user is allowed to see information in certain columns of a table.", "text": "QIBM_DB_SECADM also is responsible for administering RCAC, which restricts which rows a user is allowed to access in a table and whether a user is allowed to see information in certain columns of a table."}, {"self_ref": "#/texts/108", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 136.8, "t": 500.48, "r": 539.82, "b": 455.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 285]}], "orig": "A preferred practice is that the RCAC administrator has the QIBM_DB_SECADM function usage ID, but absolutely no other data privileges. The result is that the RCAC administrator can deploy and maintain the RCAC constructs, but cannot grant themselves unauthorized access to data itself.", "text": "A preferred practice is that the RCAC administrator has the QIBM_DB_SECADM function usage ID, but absolutely no other data privileges. The result is that the RCAC administrator can deploy and maintain the RCAC constructs, but cannot grant themselves unauthorized access to data itself."}, {"self_ref": "#/texts/109", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 136.8, "t": 442.51, "r": 543.08, "b": 421.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 136]}], "orig": "Table 2-2 shows a comparison of the different function usage IDs and *JOBCTL authority to the different CL commands and DB2 for i tools.", "text": "Table 2-2 shows a comparison of the different function usage IDs and *JOBCTL authority to the different CL commands and DB2 for i tools."}, {"self_ref": "#/texts/110", "parent": {"$ref": "#/tables/2"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 9, "bbox": {"l": 64.8, "t": 408.46, "r": 391.77, "b": 400.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 81]}], "orig": "Table 2-2    Comparison of the different function usage IDs and *JOBCTL authority", "text": "Table 2-2    Comparison of the different function usage IDs and *JOBCTL authority"}, {"self_ref": "#/texts/111", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 9, "bbox": {"l": 355.32, "t": 36.46, "r": 523.52, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 41]}], "orig": "Chapter 2. Roles and separation of duties", "text": "Chapter 2. Roles and separation of duties"}, {"self_ref": "#/texts/112", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 9, "bbox": {"l": 536.1, "t": 37.15, "r": 547.22, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "11", "text": "11"}, {"self_ref": "#/texts/113", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 10, "bbox": {"l": 136.8, "t": 720.49, "r": 528.74, "b": 699.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "The SQL CREATE PERMISSION statement that is shown in Figure 3-1 is used to define and initially enable or disable the row access rules.", "text": "The SQL CREATE PERMISSION statement that is shown in Figure 3-1 is used to define and initially enable or disable the row access rules."}, {"self_ref": "#/texts/114", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 10, "bbox": {"l": 136.8, "t": 377.86, "r": 341.99, "b": 369.54, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 44]}], "orig": "Figure 3-1   CREATE PERMISSION SQL statement", "text": "Figure 3-1   CREATE PERMISSION SQL statement"}, {"self_ref": "#/texts/115", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 148.13, "t": 651.79, "r": 251.92, "b": 642.63, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 17]}], "orig": "CREATE PERMISSION", "text": "CREATE PERMISSION"}, {"self_ref": "#/texts/116", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 251.87, "t": 651.79, "r": 257.59, "b": 642.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "<", "text": "<"}, {"self_ref": "#/texts/117", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 257.59, "t": 651.79, "r": 337.0, "b": 642.63, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "permission name", "text": "permission name"}, {"self_ref": "#/texts/118", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 337.01, "t": 651.79, "r": 342.73, "b": 642.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": ">", "text": ">"}, {"self_ref": "#/texts/119", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 346.56, "t": 670.11, "r": 530.75, "b": 662.78, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 47]}], "orig": "Names the row permission for row access control", "text": "Names the row permission for row access control"}, {"self_ref": "#/texts/120", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 148.13, "t": 610.41, "r": 163.46, "b": 601.25, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "ON", "text": "ON"}, {"self_ref": "#/texts/121", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 168.58, "t": 610.41, "r": 174.3, "b": 601.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "<", "text": "<"}, {"self_ref": "#/texts/122", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 174.31, "t": 610.41, "r": 226.88, "b": 601.25, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "table name", "text": "table name"}, {"self_ref": "#/texts/123", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 226.87, "t": 610.41, "r": 232.58, "b": 601.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": ">", "text": ">"}, {"self_ref": "#/texts/124", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 311.32, "t": 625.28, "r": 529.94, "b": 617.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 59]}], "orig": "Identifies the table on which the row permission is created", "text": "Identifies the table on which the row permission is created"}, {"self_ref": "#/texts/125", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 148.13, "t": 569.03, "r": 163.11, "b": 559.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "AS", "text": "AS"}, {"self_ref": "#/texts/126", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 165.69, "t": 569.03, "r": 171.41, "b": 559.87, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "<", "text": "<"}, {"self_ref": "#/texts/127", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 171.41, "t": 569.03, "r": 251.21, "b": 559.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "correlation name", "text": "correlation name"}, {"self_ref": "#/texts/128", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 251.21, "t": 569.03, "r": 256.93, "b": 559.87, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": ">", "text": ">"}, {"self_ref": "#/texts/129", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 235.8, "t": 587.35, "r": 532.9, "b": 580.01, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 79]}], "orig": "Specifies an optional correlation name that can be used within search-condition", "text": "Specifies an optional correlation name that can be used within search-condition"}, {"self_ref": "#/texts/130", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 148.13, "t": 527.64, "r": 199.78, "b": 518.48, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "FOR ROWS", "text": "FOR ROWS"}, {"self_ref": "#/texts/131", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 321.56, "t": 545.48, "r": 476.49, "b": 538.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 42]}], "orig": "Indicates that a row permission is created", "text": "Indicates that a row permission is created"}, {"self_ref": "#/texts/132", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 321.6, "t": 525.27, "r": 530.96, "b": 517.93, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 57]}], "orig": "Specifies a condition that can be true, false, or unknown", "text": "Specifies a condition that can be true, false, or unknown"}, {"self_ref": "#/texts/133", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 148.13, "t": 500.06, "r": 183.43, "b": 490.89, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "WHERE", "text": "WHERE"}, {"self_ref": "#/texts/134", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 188.62, "t": 500.06, "r": 194.34, "b": 490.9, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "<", "text": "<"}, {"self_ref": "#/texts/135", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 194.34, "t": 500.06, "r": 437.05, "b": 490.89, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 52]}], "orig": "logic to test: user and/or group and/or column value", "text": "logic to test: user and/or group and/or column value"}, {"self_ref": "#/texts/136", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 437.09, "t": 500.06, "r": 442.81, "b": 490.9, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": ">", "text": ">"}, {"self_ref": "#/texts/137", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 148.13, "t": 458.67, "r": 278.79, "b": 449.51, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "ENFORCED FOR ALL ACCESS", "text": "ENFORCED FOR ALL ACCESS"}, {"self_ref": "#/texts/138", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 271.56, "t": 476.99, "r": 531.75, "b": 469.65, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 72]}], "orig": "Specifies that the row permission applies to all references of the table", "text": "Specifies that the row permission applies to all references of the table"}, {"self_ref": "#/texts/139", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 148.13, "t": 417.29, "r": 185.18, "b": 408.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "ENABLE", "text": "ENABLE"}, {"self_ref": "#/texts/140", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 312.29, "t": 435.61, "r": 527.06, "b": 428.27, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Specifies that the row permission is to be initially enabled", "text": "Specifies that the row permission is to be initially enabled"}, {"self_ref": "#/texts/141", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 311.73, "t": 414.92, "r": 315.96, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "S", "text": "S"}, {"self_ref": "#/texts/142", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 329.28, "t": 414.92, "r": 336.29, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "ifi", "text": "ifi"}, {"self_ref": "#/texts/143", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 346.45, "t": 414.92, "r": 354.35, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "th", "text": "th"}, {"self_ref": "#/texts/144", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 358.65, "t": 414.92, "r": 371.73, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "t th", "text": "t th"}, {"self_ref": "#/texts/145", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 415.0, "t": 414.92, "r": 417.11, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "i", "text": "i"}, {"self_ref": "#/texts/146", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 424.27, "t": 414.92, "r": 426.38, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "i", "text": "i"}, {"self_ref": "#/texts/147", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 438.13, "t": 414.92, "r": 440.24, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "i", "text": "i"}, {"self_ref": "#/texts/148", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 445.89, "t": 414.92, "r": 448.97, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "t", "text": "t"}, {"self_ref": "#/texts/149", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 455.85, "t": 414.92, "r": 460.69, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "b", "text": "b"}, {"self_ref": "#/texts/150", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 467.37, "t": 414.92, "r": 469.48, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "i", "text": "i"}, {"self_ref": "#/texts/151", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 474.29, "t": 414.92, "r": 481.57, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "iti", "text": "iti"}, {"self_ref": "#/texts/152", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 485.98, "t": 414.92, "r": 490.18, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "ll", "text": "ll"}, {"self_ref": "#/texts/153", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 496.34, "t": 414.92, "r": 503.27, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "di", "text": "di"}, {"self_ref": "#/texts/154", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 511.26, "t": 414.92, "r": 527.61, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "bl d", "text": "bl d"}, {"self_ref": "#/texts/155", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 148.13, "t": 403.5, "r": 187.62, "b": 394.33, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "DISABLE", "text": "DISABLE"}, {"self_ref": "#/texts/156", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 187.59, "t": 403.5, "r": 190.66, "b": 394.34, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": ";", "text": ";"}, {"self_ref": "#/texts/157", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 315.98, "t": 414.92, "r": 329.29, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "pec", "text": "pec"}, {"self_ref": "#/texts/158", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 336.29, "t": 414.92, "r": 346.55, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "es", "text": "es"}, {"self_ref": "#/texts/159", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 354.36, "t": 414.92, "r": 363.82, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "a", "text": "a"}, {"self_ref": "#/texts/160", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 371.74, "t": 414.92, "r": 455.82, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "e row perm ss on  s  o", "text": "e row perm ss on  s  o"}, {"self_ref": "#/texts/161", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 460.71, "t": 414.92, "r": 474.34, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "e  n", "text": "e  n"}, {"self_ref": "#/texts/162", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 481.61, "t": 414.92, "r": 496.49, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "a y", "text": "a y"}, {"self_ref": "#/texts/163", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 503.27, "t": 414.92, "r": 511.26, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "sa", "text": "sa"}, {"self_ref": "#/texts/164", "parent": {"$ref": "#/pictures/9"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 518.2, "t": 414.92, "r": 522.79, "b": 407.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "e", "text": "e"}, {"self_ref": "#/texts/165", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 10, "bbox": {"l": 136.8, "t": 352.06, "r": 215.38, "b": 340.96, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "Column mask", "text": "Column mask", "level": 1}, {"self_ref": "#/texts/166", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 136.8, "t": 336.91, "r": 542.78, "b": 291.7, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 297]}], "orig": "A column mask is a database object that manifests a column value access control rule for a specific column in a specific table. It uses a CASE expression that describes what you see when you access the column. For example, a teller can see only the last four digits of a tax identification number.", "text": "A column mask is a database object that manifests a column value access control rule for a specific column in a specific table. It uses a CASE expression that describes what you see when you access the column. For example, a teller can see only the last four digits of a tax identification number."}, {"self_ref": "#/texts/167", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 10, "bbox": {"l": 344.94, "t": 36.46, "r": 523.58, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "Chapter 3. Row and Column Access Control", "text": "Chapter 3. Row and Column Access Control"}, {"self_ref": "#/texts/168", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 10, "bbox": {"l": 536.1, "t": 37.15, "r": 547.22, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "15", "text": "15"}, {"self_ref": "#/texts/169", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 720.49, "r": 412.17, "b": 711.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 62]}], "orig": "Table 3-1 summarizes these special registers and their values.", "text": "Table 3-1 summarizes these special registers and their values."}, {"self_ref": "#/texts/170", "parent": {"$ref": "#/tables/3"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 698.5, "r": 372.62, "b": 690.18, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Table 3-1   Special registers and their corresponding values", "text": "Table 3-1   Special registers and their corresponding values"}, {"self_ref": "#/texts/171", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 577.51, "r": 538.5, "b": 556.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 97]}], "orig": "Figure 3-5 shows the difference in the special register values when an adopted authority is used:", "text": "Figure 3-5 shows the difference in the special register values when an adopted authority is used:"}, {"self_ref": "#/texts/172", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 548.47, "r": 411.33, "b": 539.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 69]}], "orig": "/SM590000 A user connects to the server using the user profile ALICE.", "text": "/SM590000 A user connects to the server using the user profile ALICE.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/173", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 531.49, "r": 453.25, "b": 522.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 71]}], "orig": "/SM590000 USER and CURRENT USER initially have the same value of ALICE.", "text": "/SM590000 USER and CURRENT USER initially have the same value of ALICE.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/174", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 514.51, "r": 541.46, "b": 493.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 154]}], "orig": "/SM590000 ALICE calls an SQL procedure that is named proc1, which is owned by user profile JOE and was created to adopt JOE's authority when it is called.", "text": "/SM590000 ALICE calls an SQL procedure that is named proc1, which is owned by user profile JOE and was created to adopt JOE's authority when it is called.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/175", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 485.47, "r": 547.58, "b": 452.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 247]}], "orig": "/SM590000 While the procedure is running, the special register USER still contains the value of ALICE because it excludes any adopted authority. The special register CURRENT USER contains the value of JOE because it includes any adopted authority.", "text": "/SM590000 While the procedure is running, the special register USER still contains the value of ALICE because it excludes any adopted authority. The special register CURRENT USER contains the value of JOE because it includes any adopted authority.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/176", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 444.49, "r": 547.48, "b": 423.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 127]}], "orig": "/SM590000 When proc1 ends, the session reverts to its original state with both USER and CURRENT USER having the value of ALICE.", "text": "/SM590000 When proc1 ends, the session reverts to its original state with both USER and CURRENT USER having the value of ALICE.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/177", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 195.28, "r": 341.27, "b": 186.96, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 52]}], "orig": "Figure 3-5   Special registers and adopted authority", "text": "Figure 3-5   Special registers and adopted authority"}, {"self_ref": "#/texts/178", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 140.73, "t": 404.56, "r": 216.38, "b": 396.62, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "Signed on as ALICE", "text": "Signed on as ALICE"}, {"self_ref": "#/texts/179", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 138.48, "t": 380.67, "r": 191.71, "b": 372.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "USER = ALICE", "text": "USER = ALICE"}, {"self_ref": "#/texts/180", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 138.48, "t": 368.72, "r": 232.57, "b": 360.79, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "CURRENT USER = ALICE", "text": "CURRENT USER = ALICE"}, {"self_ref": "#/texts/181", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 138.48, "t": 344.83, "r": 183.27, "b": 336.9, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "CALL proc1", "text": "CALL proc1"}, {"self_ref": "#/texts/182", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 148.43, "t": 317.96, "r": 174.06, "b": 310.02, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "P roc1:", "text": "P roc1:"}, {"self_ref": "#/texts/183", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 157.52, "t": 306.01, "r": 209.1, "b": 298.08, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "Owner = JOE", "text": "Owner = JOE"}, {"self_ref": "#/texts/184", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 157.52, "t": 294.07, "r": 281.69, "b": 286.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "SET OPTION USRPRF=*OWNER", "text": "SET OPTION USRPRF=*OWNER"}, {"self_ref": "#/texts/185", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 148.43, "t": 270.18, "r": 201.66, "b": 262.24, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "USER = ALICE", "text": "USER = ALICE"}, {"self_ref": "#/texts/186", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 148.43, "t": 258.23, "r": 234.58, "b": 250.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "CURRENT USER = JOE", "text": "CURRENT USER = JOE"}, {"self_ref": "#/texts/187", "parent": {"$ref": "#/pictures/10"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 138.48, "t": 225.38, "r": 232.57, "b": 205.5, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 33]}], "orig": "USER = ALICE CURRENT USER = ALICE", "text": "USER = ALICE CURRENT USER = ALICE"}, {"self_ref": "#/texts/188", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 11, "bbox": {"l": 64.8, "t": 166.44, "r": 247.02, "b": 154.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "3.2.2  Built-in global variables", "text": "3.2.2  Built-in global variables", "level": 1}, {"self_ref": "#/texts/189", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 140.29, "r": 518.01, "b": 119.08, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 161]}], "orig": "Built-in global variables are provided with the database manager and are used in SQL statements to retrieve scalar values that are associated with the variables.", "text": "Built-in global variables are provided with the database manager and are used in SQL statements to retrieve scalar values that are associated with the variables."}, {"self_ref": "#/texts/190", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 136.8, "t": 106.27, "r": 532.35, "b": 73.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 233]}], "orig": "IBM DB2 for i supports nine different built-in global variables that are read only and maintained by the system. These global variables can be used to identify attributes of the database connection and used as part of the RCAC logic.", "text": "IBM DB2 for i supports nine different built-in global variables that are read only and maintained by the system. These global variables can be used to identify attributes of the database connection and used as part of the RCAC logic."}, {"self_ref": "#/texts/191", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 11, "bbox": {"l": 344.94, "t": 36.46, "r": 523.58, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "Chapter 3. Row and Column Access Control", "text": "Chapter 3. Row and Column Access Control"}, {"self_ref": "#/texts/192", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 11, "bbox": {"l": 536.1, "t": 37.15, "r": 547.22, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "19", "text": "19"}, {"self_ref": "#/texts/193", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 12, "bbox": {"l": 136.8, "t": 720.49, "r": 342.52, "b": 711.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 51]}], "orig": "Table 3-2 lists the nine built-in global variables.", "text": "Table 3-2 lists the nine built-in global variables."}, {"self_ref": "#/texts/194", "parent": {"$ref": "#/tables/4"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 12, "bbox": {"l": 64.8, "t": 698.5, "r": 201.2, "b": 690.18, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 37]}], "orig": "Table 3-2   Built-in global variables", "text": "Table 3-2   Built-in global variables"}, {"self_ref": "#/texts/195", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 12, "bbox": {"l": 64.8, "t": 469.8, "r": 384.35, "b": 455.04, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 35]}], "orig": "3.3  VERIFY_GROUP_FOR_USER function", "text": "3.3  VERIFY_GROUP_FOR_USER function", "level": 1}, {"self_ref": "#/texts/196", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 12, "bbox": {"l": 136.8, "t": 437.47, "r": 547.54, "b": 356.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 576]}], "orig": "The VERIFY_GROUP_FOR_USER function was added in IBM i 7.2. Although it is primarily intended for use with RCAC permissions and masks, it can be used in other SQL statements. The first parameter must be one of these three special registers: SESSION_USER, USER, or CURRENT_USER. The second and subsequent parameters are a list of user or group profiles. Each of these values must be 1 - 10 characters in length. These values are not validated for their existence, which means that you can specify the names of user profiles that do not exist without receiving any kind of error.", "text": "The VERIFY_GROUP_FOR_USER function was added in IBM i 7.2. Although it is primarily intended for use with RCAC permissions and masks, it can be used in other SQL statements. The first parameter must be one of these three special registers: SESSION_USER, USER, or CURRENT_USER. The second and subsequent parameters are a list of user or group profiles. Each of these values must be 1 - 10 characters in length. These values are not validated for their existence, which means that you can specify the names of user profiles that do not exist without receiving any kind of error."}, {"self_ref": "#/texts/197", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 12, "bbox": {"l": 136.8, "t": 343.51, "r": 547.39, "b": 310.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 235]}], "orig": "If a special register value is in the list of user profiles or it is a member of a group profile included in the list, the function returns a long integer value of 1. Otherwise, it returns a value of 0. It never returns the null value.", "text": "If a special register value is in the list of user profiles or it is a member of a group profile included in the list, the function returns a long integer value of 1. Otherwise, it returns a value of 0. It never returns the null value."}, {"self_ref": "#/texts/198", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 12, "bbox": {"l": 136.8, "t": 297.49, "r": 458.43, "b": 288.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 63]}], "orig": "Here is an example of using the VERIFY_GROUP_FOR_USER function:", "text": "Here is an example of using the VERIFY_GROUP_FOR_USER function:"}, {"self_ref": "#/texts/199", "parent": {"$ref": "#/groups/8"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 136.8, "t": 280.45, "r": 406.05, "b": 271.24, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 57]}], "orig": "1. There are user profiles for MGR, JANE, JUDY, and TONY.", "text": "There are user profiles for MGR, JANE, JUDY, and TONY.", "enumerated": true, "marker": "1."}, {"self_ref": "#/texts/200", "parent": {"$ref": "#/groups/8"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 136.8, "t": 263.47, "r": 396.96, "b": 254.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 58]}], "orig": "2. The user profile JANE specifies a group profile of MGR.", "text": "The user profile JANE specifies a group profile of MGR.", "enumerated": true, "marker": "2."}, {"self_ref": "#/texts/201", "parent": {"$ref": "#/groups/8"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 136.8, "t": 246.49, "r": 536.58, "b": 225.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 127]}], "orig": "3. If a user is connected to the server using user profile JANE, all of the following function invocations return a value of 1:", "text": "If a user is connected to the server using user profile JANE, all of the following function invocations return a value of 1:", "enumerated": true, "marker": "3."}, {"self_ref": "#/texts/202", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "code", "prov": [{"page_no": 12, "bbox": {"l": 151.2, "t": 217.01, "r": 451.02, "b": 150.65, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 265]}], "orig": "VERIFY_GROUP_FOR_USER (CURRENT_USER, 'MGR') VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JAN<PERSON>', 'MGR') VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JAN<PERSON>', 'MGR', 'STEVE') The following function invocation returns a value of 0: VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JUDY', 'TONY')", "text": "VERIFY_GROUP_FOR_USER (CURRENT_USER, 'MGR') VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JAN<PERSON>', 'MGR') VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JAN<PERSON>', 'MGR', 'STEVE') The following function invocation returns a value of 0: VERIFY_GROUP_FOR_USER (CURRENT_USER, 'JUDY', 'TONY')", "captions": [], "references": [], "footnotes": [], "code_language": "unknown"}, {"self_ref": "#/texts/203", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 12, "bbox": {"l": 64.8, "t": 37.15, "r": 78.4, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "20", "text": "20"}, {"self_ref": "#/texts/204", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 12, "bbox": {"l": 93.42, "t": 36.46, "r": 334.26, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Row and Column Access Control Support in IBM DB2 for i", "text": "Row and Column Access Control Support in IBM DB2 for i"}, {"self_ref": "#/texts/205", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 13, "bbox": {"l": 136.8, "t": 720.04, "r": 166.74, "b": 711.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "RETURN", "text": "RETURN"}, {"self_ref": "#/texts/206", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 13, "bbox": {"l": 136.8, "t": 708.04, "r": 156.78, "b": 699.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "CASE", "text": "CASE"}, {"self_ref": "#/texts/207", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "code", "prov": [{"page_no": 13, "bbox": {"l": 136.8, "t": 696.04, "r": 521.57, "b": 531.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 443]}], "orig": "WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'HR', 'EMP' ) = 1 THEN EMPLOYEES . DATE_OF_BIRTH WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER = EMPLOYEES . USER_ID THEN EMPLOYEES . DATE_OF_BIRTH WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER <> EMPLOYEES . USER_ID THEN ( 9999 || '-' ||  MONTH ( EMPLOYEES . DATE_OF_BIRTH ) || '-'     || DAY (EMPLOYEES.DATE_OF_BIRTH )) ELSE NULL END ENABLE  ;", "text": "WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'HR', 'EMP' ) = 1 THEN EMPLOYEES . DATE_OF_BIRTH WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER = EMPLOYEES . USER_ID THEN EMPLOYEES . DATE_OF_BIRTH WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER <> EMPLOYEES . USER_ID THEN ( 9999 || '-' ||  MONTH ( EMPLOYEES . DATE_OF_BIRTH ) || '-'     || DAY (EMPLOYEES.DATE_OF_BIRTH )) ELSE NULL END ENABLE  ;", "captions": [], "references": [], "footnotes": [], "code_language": "unknown"}, {"self_ref": "#/texts/208", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 136.8, "t": 516.49, "r": 547.28, "b": 495.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 136]}], "orig": "2. The other column to mask in this example is the TAX_ID information. In this example, the rules to enforce include the following ones:", "text": "The other column to mask in this example is the TAX_ID information. In this example, the rules to enforce include the following ones:", "enumerated": true, "marker": "2."}, {"self_ref": "#/texts/209", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 152.04, "t": 487.51, "r": 469.12, "b": 478.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 62]}], "orig": "-Human Resources can see the unmasked TAX_ID of the employees.", "text": "-Human Resources can see the unmasked TAX_ID of the employees.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/210", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 152.04, "t": 470.48, "r": 403.93, "b": 461.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "-Employees can see only their own unmasked TAX_ID.", "text": "-Employees can see only their own unmasked TAX_ID.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/211", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 152.04, "t": 453.5, "r": 545.12, "b": 432.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 129]}], "orig": "-Managers see a masked version of TAX_ID with the first five characters replaced with the X character (for example, XXX-XX-1234).", "text": "-Managers see a masked version of TAX_ID with the first five characters replaced with the X character (for example, XXX-XX-1234).", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/212", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 152.04, "t": 424.52, "r": 529.44, "b": 415.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 77]}], "orig": "-Any other person sees the entire TAX_ID as masked, for example, XXX-XX-XXXX.", "text": "-Any other person sees the entire TAX_ID as masked, for example, XXX-XX-XXXX.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/213", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 151.2, "t": 407.48, "r": 530.02, "b": 398.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 82]}], "orig": "To implement this column mask, run the SQL statement that is shown in Example 3-9.", "text": "To implement this column mask, run the SQL statement that is shown in Example 3-9.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/214", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/215"}], "content_layer": "body", "label": "code", "prov": [{"page_no": 13, "bbox": {"l": 136.8, "t": 368.02, "r": 526.55, "b": 107.63, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 607]}], "orig": "CREATE MASK   HR_SCHEMA.MASK_TAX_ID_ON_EMPLOYEES ON            HR_SCHEMA.EMPLOYEES AS EMPLOYEES FOR COLUMN    TAX_ID RETURN CASE WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'HR' ) = 1 THEN EMPLOYEES . TAX_ID WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER = EMPLOYEES . USER_ID THEN EMPLOYEES . TAX_ID WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER <> EMPLOYEES . USER_ID THEN ( 'XXX-XX-' CONCAT QSYS2 . SUBSTR ( EMPLOYEES . TAX_ID , 8 , 4 ) ) WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'EMP' ) = 1 THEN EMPLOYEES . TAX_ID ELSE 'XXX-XX-XXXX' END ENABLE  ;", "text": "CREATE MASK   HR_SCHEMA.MASK_TAX_ID_ON_EMPLOYEES ON            HR_SCHEMA.EMPLOYEES AS EMPLOYEES FOR COLUMN    TAX_ID RETURN CASE WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'HR' ) = 1 THEN EMPLOYEES . TAX_ID WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER = EMPLOYEES . USER_ID THEN EMPLOYEES . TAX_ID WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'MGR' ) = 1 AND SESSION_USER <> EMPLOYEES . USER_ID THEN ( 'XXX-XX-' CONCAT QSYS2 . SUBSTR ( EMPLOYEES . TAX_ID , 8 , 4 ) ) WHEN VERIFY_GROUP_FOR_USER ( SESSION_USER , 'EMP' ) = 1 THEN EMPLOYEES . TAX_ID ELSE 'XXX-XX-XXXX' END ENABLE  ;", "captions": [{"$ref": "#/texts/215"}], "references": [], "footnotes": [], "code_language": "unknown"}, {"self_ref": "#/texts/215", "parent": {"$ref": "#/texts/214"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 13, "bbox": {"l": 136.8, "t": 385.48, "r": 352.0, "b": 377.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "Example 3-9   Creating a mask on the TAX_ID column", "text": "Example 3-9   Creating a mask on the TAX_ID column"}, {"self_ref": "#/texts/216", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 13, "bbox": {"l": 344.94, "t": 36.46, "r": 523.58, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "Chapter 3. Row and Column Access Control", "text": "Chapter 3. Row and Column Access Control"}, {"self_ref": "#/texts/217", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 13, "bbox": {"l": 536.1, "t": 37.15, "r": 547.22, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "27", "text": "27"}, {"self_ref": "#/texts/218", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 720.49, "r": 449.92, "b": 711.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 65]}], "orig": "3. Figure 3-10 shows the masks that are created in the HR_SCHEMA.", "text": "Figure 3-10 shows the masks that are created in the HR_SCHEMA.", "enumerated": true, "marker": "3."}, {"self_ref": "#/texts/219", "parent": {"$ref": "#/pictures/11"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 14, "bbox": {"l": 64.8, "t": 618.46, "r": 293.15, "b": 610.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Figure 3-10   Column masks shown in System i Navigator", "text": "Figure 3-10   Column masks shown in System i Navigator"}, {"self_ref": "#/texts/220", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 14, "bbox": {"l": 64.8, "t": 589.62, "r": 203.98, "b": 577.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 22]}], "orig": "3.6.6  Activating RCAC", "text": "3.6.6  Activating RCAC", "level": 1}, {"self_ref": "#/texts/221", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 563.47, "r": 547.21, "b": 530.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 265]}], "orig": "Now that you have created the row permission and the two column masks, RCAC must be activated. The row permission and the two column masks are enabled (last clause in the scripts), but now you must activate RCAC on the table. To do so, complete the following steps:", "text": "Now that you have created the row permission and the two column masks, RCAC must be activated. The row permission and the two column masks are enabled (last clause in the scripts), but now you must activate RCAC on the table. To do so, complete the following steps:"}, {"self_ref": "#/texts/222", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 522.49, "r": 409.44, "b": 513.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 57]}], "orig": "1. Run the SQL statements that are shown in Example 3-10.", "text": "Run the SQL statements that are shown in Example 3-10.", "enumerated": true, "marker": "1."}, {"self_ref": "#/texts/223", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 500.44, "r": 375.27, "b": 492.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 53]}], "orig": "Example 3-10   Activating RCAC on the EMPLOYEES table", "text": "Example 3-10   Activating RCAC on the EMPLOYEES table", "level": 1}, {"self_ref": "#/texts/224", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 483.04, "r": 376.68, "b": 474.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "/*   Active Row Access Control (permissions)  */", "text": "/*   Active Row Access Control (permissions)  */", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/225", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 471.04, "r": 376.68, "b": 462.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 48]}], "orig": "/*   Active Column Access Control (masks)     */", "text": "/*   Active Column Access Control (masks)     */", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/226", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 459.04, "r": 291.72, "b": 450.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "ALTER TABLE HR_SCHEMA.EMPLOYEES", "text": "ALTER TABLE HR_SCHEMA.EMPLOYEES"}, {"self_ref": "#/texts/227", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 447.04, "r": 271.68, "b": 438.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 27]}], "orig": "ACTIVATE ROW ACCESS CONTROL", "text": "ACTIVATE ROW ACCESS CONTROL"}, {"self_ref": "#/texts/228", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 435.04, "r": 291.72, "b": 426.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "ACTIVATE COLUMN ACCESS CONTROL;", "text": "ACTIVATE COLUMN ACCESS CONTROL;"}, {"self_ref": "#/texts/229", "parent": {"$ref": "#/groups/13"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 136.8, "t": 411.49, "r": 540.81, "b": 378.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 231]}], "orig": "2. Look at the definition of the EMPLOYEE table, as shown in Figure 3-11. To do this, from the main navigation pane of System i Navigator, click Schemas  HR_SCHEMA  Tables , right-click the EMPLOYEES table, and click Definition .", "text": "Look at the definition of the EMPLOYEE table, as shown in Figure 3-11. To do this, from the main navigation pane of System i Navigator, click Schemas  HR_SCHEMA  Tables , right-click the EMPLOYEES table, and click Definition .", "enumerated": true, "marker": "2."}, {"self_ref": "#/texts/230", "parent": {"$ref": "#/pictures/12"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 14, "bbox": {"l": 64.8, "t": 142.96, "r": 347.44, "b": 134.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 67]}], "orig": "Figure 3-11   Selecting the EMPLOYEES table from System i Navigator", "text": "Figure 3-11   Selecting the EMPLOYEES table from System i Navigator"}, {"self_ref": "#/texts/231", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 14, "bbox": {"l": 64.8, "t": 37.15, "r": 78.4, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "28", "text": "28"}, {"self_ref": "#/texts/232", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 14, "bbox": {"l": 93.42, "t": 36.46, "r": 334.26, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Row and Column Access Control Support in IBM DB2 for i", "text": "Row and Column Access Control Support in IBM DB2 for i"}, {"self_ref": "#/texts/233", "parent": {"$ref": "#/groups/14"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 136.8, "t": 720.49, "r": 514.06, "b": 687.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 228]}], "orig": "2. Figure 4-68 shows the Visual Explain of the same SQL statement, but with RCAC enabled. It is clear that the implementation of the SQL statement is more complex because the row permission rule becomes part of the WHERE clause.", "text": "Figure 4-68 shows the Visual Explain of the same SQL statement, but with RCAC enabled. It is clear that the implementation of the SQL statement is more complex because the row permission rule becomes part of the WHERE clause.", "enumerated": true, "marker": "2."}, {"self_ref": "#/texts/234", "parent": {"$ref": "#/pictures/13"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 15, "bbox": {"l": 136.8, "t": 311.44, "r": 327.11, "b": 303.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 46]}], "orig": "Figure 4-68   Visual Explain with RCAC enabled", "text": "Figure 4-68   Visual Explain with RCAC enabled"}, {"self_ref": "#/texts/235", "parent": {"$ref": "#/groups/14"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 15, "bbox": {"l": 136.8, "t": 285.43, "r": 547.31, "b": 252.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 232]}], "orig": "3. Compare the advised indexes that are provided by the Optimizer without RCAC and with RCAC enabled. Figure 4-69 shows the index advice for the SQL statement without RCAC enabled. The index being advised is for the ORDER BY clause.", "text": "Compare the advised indexes that are provided by the Optimizer without RCAC and with RCAC enabled. Figure 4-69 shows the index advice for the SQL statement without RCAC enabled. The index being advised is for the ORDER BY clause.", "enumerated": true, "marker": "3."}, {"self_ref": "#/texts/236", "parent": {"$ref": "#/pictures/14"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 15, "bbox": {"l": 64.8, "t": 124.48, "r": 227.12, "b": 116.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 39]}], "orig": "Figure 4-69   Index advice with no RCAC", "text": "Figure 4-69   Index advice with no RCAC"}, {"self_ref": "#/texts/237", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 15, "bbox": {"l": 214.8, "t": 36.46, "r": 523.58, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 70]}], "orig": "Chapter 4. Implementing Row and Column Access Control: Banking example", "text": "Chapter 4. Implementing Row and Column Access Control: Banking example"}, {"self_ref": "#/texts/238", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 15, "bbox": {"l": 536.1, "t": 37.15, "r": 547.22, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "77", "text": "77"}, {"self_ref": "#/texts/239", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "code", "prov": [{"page_no": 16, "bbox": {"l": 64.8, "t": 720.06, "r": 500.7, "b": 85.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2007]}], "orig": "THEN C . CUSTOMER_TAX_ID WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'TELLER' ) = 1 THEN ( 'XXX-XX-' CONCAT QSYS2 . SUBSTR ( C . CUSTOMER_TAX_ID , 8 , 4 ) ) WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_TAX_ID ELSE 'XXX-XX-XXXX' END ENABLE  ; CREATE MASK BANK_SCHEMA.MASK_DRIVERS_LICENSE_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_DRIVERS_LICENSE_NUMBER RETURN  CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'TELLER' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER ELSE '*************' END ENABLE  ; CREATE MASK BANK_SCHEMA.MASK_LOGIN_ID_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_LOGIN_ID RETURN  CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_LOGIN_ID WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_LOGIN_ID ELSE '*****' END ENABLE  ; CREATE MASK BANK_SCHEMA.MASK_SECURITY_QUESTION_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_SECURITY_QUESTION RETURN  CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION ELSE '*****' END ENABLE  ; CREATE MASK BANK_SCHEMA.MASK_SECURITY_QUESTION_ANSWER_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_SECURITY_QUESTION_ANSWER RETURN  CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION_ANSWER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION_ANSWER ELSE '*****' END ENABLE  ; ALTER TABLE BANK_SCHEMA.CUSTOMERS ACTIVATE ROW ACCESS CONTROL ACTIVATE COLUMN ACCESS CONTROL ;", "text": "THEN C . CUSTOMER_TAX_ID WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'TELLER' ) = 1 THEN ( 'XXX-XX-' CONCAT QSYS2 . SUBSTR ( C . CUSTOMER_TAX_ID , 8 , 4 ) ) WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_TAX_ID ELSE 'XXX-XX-XXXX' END ENABLE  ; CREATE MASK BANK_SCHEMA.MASK_DRIVERS_LICENSE_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_DRIVERS_LICENSE_NUMBER RETURN  CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'TELLER' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_DRIVERS_LICENSE_NUMBER ELSE '*************' END ENABLE  ; CREATE MASK BANK_SCHEMA.MASK_LOGIN_ID_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_LOGIN_ID RETURN  CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_LOGIN_ID WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_LOGIN_ID ELSE '*****' END ENABLE  ; CREATE MASK BANK_SCHEMA.MASK_SECURITY_QUESTION_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_SECURITY_QUESTION RETURN  CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION ELSE '*****' END ENABLE  ; CREATE MASK BANK_SCHEMA.MASK_SECURITY_QUESTION_ANSWER_ON_CUSTOMERS ON BANK_SCHEMA.CUSTOMERS AS C FOR COLUMN CUSTOMER_SECURITY_QUESTION_ANSWER RETURN  CASE WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'ADMIN' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION_ANSWER WHEN QSYS2 . VERIFY_GROUP_FOR_USER ( SESSION_USER , 'CUSTOMER' ) = 1 THEN C . CUSTOMER_SECURITY_QUESTION_ANSWER ELSE '*****' END ENABLE  ; ALTER TABLE BANK_SCHEMA.CUSTOMERS ACTIVATE ROW ACCESS CONTROL ACTIVATE COLUMN ACCESS CONTROL ;", "captions": [], "references": [], "footnotes": [], "code_language": "unknown"}, {"self_ref": "#/texts/240", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 16, "bbox": {"l": 64.8, "t": 37.15, "r": 83.98, "b": 27.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "124", "text": "124"}, {"self_ref": "#/texts/241", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 16, "bbox": {"l": 98.94, "t": 36.46, "r": 339.65, "b": 28.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Row and Column Access Control Support in IBM DB2 for i", "text": "Row and Column Access Control Support in IBM DB2 for i"}, {"self_ref": "#/texts/242", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 287.22, "t": 763.45, "r": 414.26, "b": 741.25, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "Back cover", "text": "Back cover"}, {"self_ref": "#/texts/243", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 18, "bbox": {"l": 27.0, "t": 718.36, "r": 447.36, "b": 651.54, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 54]}], "orig": "Row and Column Access Control Support in IBM DB2 for i", "text": "Row and Column Access Control Support in IBM DB2 for i", "level": 1}, {"self_ref": "#/texts/244", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 26.7, "t": 549.83, "r": 127.44, "b": 525.17, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "Implement roles and separation of duties", "text": "Implement roles and separation of duties"}, {"self_ref": "#/texts/245", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 26.7, "t": 507.83, "r": 120.28, "b": 469.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "Leverage row permissions on the database", "text": "Leverage row permissions on the database"}, {"self_ref": "#/texts/246", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 152.94, "t": 549.27, "r": 414.46, "b": 468.41, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 464]}], "orig": "This IBM Redpaper publication provides information about the IBM i 7.2 feature of IBM DB2 for i Row and Column Access Control (RCAC). It offers a broad description of the function and advantages of controlling access to data in a comprehensive and transparent way. This publication helps you understand the capabilities of RCAC and provides examples of defining, creating, and implementing the row permissions and column masks in a relational database environment.", "text": "This IBM Redpaper publication provides information about the IBM i 7.2 feature of IBM DB2 for i Row and Column Access Control (RCAC). It offers a broad description of the function and advantages of controlling access to data in a comprehensive and transparent way. This publication helps you understand the capabilities of RCAC and provides examples of defining, creating, and implementing the row permissions and column masks in a relational database environment."}, {"self_ref": "#/texts/247", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 26.7, "t": 451.85, "r": 121.45, "b": 413.15, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "Protect columns by defining column masks", "text": "Protect columns by defining column masks"}, {"self_ref": "#/texts/248", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 152.94, "t": 460.29, "r": 414.46, "b": 403.43, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 309]}], "orig": "This paper is intended for database engineers, data-centric application developers, and security officers who want to design and implement RCAC as a part of their data control and governance policy. A solid background in IBM i object level security, DB2 for i relational database concepts, and SQL is assumed.", "text": "This paper is intended for database engineers, data-centric application developers, and security officers who want to design and implement RCAC as a part of their data control and governance policy. A solid background in IBM i object level security, DB2 for i relational database concepts, and SQL is assumed."}, {"self_ref": "#/texts/249", "parent": {"$ref": "#/pictures/15"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 558.12, "t": 746.53, "r": 565.46, "b": 737.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "fi", "text": "fi"}, {"self_ref": "#/texts/250", "parent": {"$ref": "#/pictures/16"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 474.6, "t": 627.94, "r": 580.87, "b": 603.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "Redpaper", "text": "Redpaper"}, {"self_ref": "#/texts/251", "parent": {"$ref": "#/pictures/16"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 582.54, "t": 619.67, "r": 592.14, "b": 610.79, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "™", "text": "™"}, {"self_ref": "#/texts/252", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 467.34, "t": 544.28, "r": 559.8, "b": 489.84, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 44]}], "orig": "INTERNATIONAL TECHNICAL SUPPORT ORGANIZATION", "text": "INTERNATIONAL TECHNICAL SUPPORT ORGANIZATION"}, {"self_ref": "#/texts/253", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 467.34, "t": 440.21, "r": 587.78, "b": 405.53, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "BUILDING TECHNICAL INFORMATION BASED ON PRACTICAL EXPERIENCE", "text": "BUILDING TECHNICAL INFORMATION BASED ON PRACTICAL EXPERIENCE"}, {"self_ref": "#/texts/254", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 467.34, "t": 392.14, "r": 587.75, "b": 250.37, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 323]}], "orig": "IBM Redbooks are developed by the IBM International Technical Support Organization. Experts from IBM, Customers and Partners from around the world create timely technical information based on realistic scenarios. Specific recommendations are provided to help you implement IT solutions more effectively in your environment.", "text": "IBM Redbooks are developed by the IBM International Technical Support Organization. Experts from IBM, Customers and Partners from around the world create timely technical information based on realistic scenarios. Specific recommendations are provided to help you implement IT solutions more effectively in your environment."}, {"self_ref": "#/texts/255", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 18, "bbox": {"l": 467.34, "t": 213.17, "r": 570.95, "b": 190.49, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 39]}], "orig": "For more information: ibm.com /redbooks", "text": "For more information: ibm.com /redbooks"}, {"self_ref": "#/texts/256", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_footer", "prov": [{"page_no": 18, "bbox": {"l": 171.0, "t": 160.66, "r": 231.9, "b": 152.34, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "REDP-5110-00", "text": "REDP-5110-00"}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 513.46, "t": 765.91, "r": 586.16, "b": 737.18, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 33.09, "t": 498.97, "r": 585.15, "b": 89.55, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/6"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 1, "bbox": {"l": 316.94, "t": 81.87, "r": 581.35, "b": 17.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 3, "bbox": {"l": 143.4, "t": 521.74, "r": 179.56, "b": 506.38, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 3, "bbox": {"l": 64.17, "t": 188.49, "r": 258.77, "b": 103.87, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/5", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 4, "bbox": {"l": 142.53, "t": 416.96, "r": 251.48, "b": 288.79, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/6", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 4, "bbox": {"l": 145.41, "t": 264.76, "r": 252.09, "b": 156.62, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/7", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "picture", "prov": [{"page_no": 5, "bbox": {"l": 32.08, "t": 721.42, "r": 239.62, "b": 554.04, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/8", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/76"}, {"$ref": "#/texts/77"}, {"$ref": "#/texts/78"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 7, "bbox": {"l": 135.92, "t": 416.07, "r": 546.45, "b": 103.39, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/76"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/9", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/114"}, {"$ref": "#/texts/115"}, {"$ref": "#/texts/116"}, {"$ref": "#/texts/117"}, {"$ref": "#/texts/118"}, {"$ref": "#/texts/119"}, {"$ref": "#/texts/120"}, {"$ref": "#/texts/121"}, {"$ref": "#/texts/122"}, {"$ref": "#/texts/123"}, {"$ref": "#/texts/124"}, {"$ref": "#/texts/125"}, {"$ref": "#/texts/126"}, {"$ref": "#/texts/127"}, {"$ref": "#/texts/128"}, {"$ref": "#/texts/129"}, {"$ref": "#/texts/130"}, {"$ref": "#/texts/131"}, {"$ref": "#/texts/132"}, {"$ref": "#/texts/133"}, {"$ref": "#/texts/134"}, {"$ref": "#/texts/135"}, {"$ref": "#/texts/136"}, {"$ref": "#/texts/137"}, {"$ref": "#/texts/138"}, {"$ref": "#/texts/139"}, {"$ref": "#/texts/140"}, {"$ref": "#/texts/141"}, {"$ref": "#/texts/142"}, {"$ref": "#/texts/143"}, {"$ref": "#/texts/144"}, {"$ref": "#/texts/145"}, {"$ref": "#/texts/146"}, {"$ref": "#/texts/147"}, {"$ref": "#/texts/148"}, {"$ref": "#/texts/149"}, {"$ref": "#/texts/150"}, {"$ref": "#/texts/151"}, {"$ref": "#/texts/152"}, {"$ref": "#/texts/153"}, {"$ref": "#/texts/154"}, {"$ref": "#/texts/155"}, {"$ref": "#/texts/156"}, {"$ref": "#/texts/157"}, {"$ref": "#/texts/158"}, {"$ref": "#/texts/159"}, {"$ref": "#/texts/160"}, {"$ref": "#/texts/161"}, {"$ref": "#/texts/162"}, {"$ref": "#/texts/163"}, {"$ref": "#/texts/164"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 10, "bbox": {"l": 135.97, "t": 684.59, "r": 545.42, "b": 381.39, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/114"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/10", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/177"}, {"$ref": "#/texts/178"}, {"$ref": "#/texts/179"}, {"$ref": "#/texts/180"}, {"$ref": "#/texts/181"}, {"$ref": "#/texts/182"}, {"$ref": "#/texts/183"}, {"$ref": "#/texts/184"}, {"$ref": "#/texts/185"}, {"$ref": "#/texts/186"}, {"$ref": "#/texts/187"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 11, "bbox": {"l": 135.65, "t": 407.83, "r": 301.24, "b": 197.24, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/177"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/11", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/219"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 14, "bbox": {"l": 63.8, "t": 696.62, "r": 547.11, "b": 621.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/219"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/12", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/230"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 14, "bbox": {"l": 63.99, "t": 364.1, "r": 530.05, "b": 145.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/230"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/13", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/234"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 15, "bbox": {"l": 136.5, "t": 672.75, "r": 545.45, "b": 314.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/234"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/14", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/236"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 15, "bbox": {"l": 64.28, "t": 238.42, "r": 506.39, "b": 127.91, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/236"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/15", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/249"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 18, "bbox": {"l": 485.17, "t": 766.74, "r": 566.3, "b": 737.81, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/16", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/250"}, {"$ref": "#/texts/251"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 18, "bbox": {"l": 474.36, "t": 711.95, "r": 592.27, "b": 602.19, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "document_index", "prov": [{"page_no": 2, "bbox": {"l": 136.15, "t": 659.97, "r": 547.53, "b": 76.35, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 135.94, "t": 132.65, "r": 497.33, "b": 144.51, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Notices . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.53, "t": 133.25, "r": 547.33, "b": 143.68, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". vii", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 136.2, "t": 143.81, "r": 497.29, "b": 154.53, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Trademarks . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 532.72, "t": 145.24, "r": 547.26, "b": 156.01, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "viii", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 136.03, "t": 165.3, "r": 497.36, "b": 176.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "DB2 for i Center of Excellence . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.97, "t": 165.18, "r": 547.47, "b": 176.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". ix", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 136.02, "t": 188.17, "r": 497.33, "b": 199.42, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Preface . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.61, "t": 188.05, "r": 546.93, "b": 198.54, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". xi", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 135.96, "t": 200.2, "r": 497.31, "b": 211.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Authors. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.46, "t": 200.04, "r": 547.17, "b": 211.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". xi", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 136.06, "t": 213.48, "r": 497.33, "b": 224.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Now you can become a published author, too! . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.89, "t": 213.67, "r": 547.26, "b": 224.11, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "xiii", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 135.36, "t": 225.56, "r": 497.31, "b": 236.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Comments welcome. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.87, "t": 226.24, "r": 547.04, "b": 236.61, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "xiii", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 134.98, "t": 238.64, "r": 497.3, "b": 249.32, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Stay connected to IBM Redbooks . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.3, "t": 239.21, "r": 546.87, "b": 249.57, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "xiv", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 134.33, "t": 260.01, "r": 497.34, "b": 271.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Chapter 1. Securing and protecting IBM DB2 data . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.45, "t": 259.78, "r": 547.04, "b": 271.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 1", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 135.42, "t": 272.4, "r": 497.33, "b": 283.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.1 Security fundamentals. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.45, "t": 272.25, "r": 546.86, "b": 283.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 135.55, "t": 285.19, "r": 497.33, "b": 296.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.2 Current state of IBM i security. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.44, "t": 284.81, "r": 546.98, "b": 296.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 135.66, "t": 297.92, "r": 497.3, "b": 308.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.3 DB2 for i security controls . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.41, "t": 297.42, "r": 547.21, "b": 308.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 3", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 152.14, "t": 311.22, "r": 497.28, "b": 321.64, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.3.1 Existing row and column control . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.4, "t": 310.34, "r": 547.21, "b": 321.32, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 146.73, "t": 323.04, "r": 497.31, "b": 333.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.3.2 New controls: Row and Column Access Control. . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.44, "t": 323.21, "r": 547.54, "b": 333.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 5", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 137.53, "t": 346.06, "r": 497.34, "b": 356.66, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Chapter 2. Roles and separation of duties . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.45, "t": 344.64, "r": 547.62, "b": 356.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 7", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 136.03, "t": 356.23, "r": 497.31, "b": 368.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1 Roles. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.82, "t": 356.92, "r": 547.59, "b": 367.53, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 149.04, "t": 369.94, "r": 497.27, "b": 380.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.1 DDM and DRDA application server access: QIBM_DB_DDMDRDA . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.98, "t": 369.45, "r": 547.53, "b": 380.03, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 142.25, "t": 382.91, "r": 497.26, "b": 393.57, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.2 Toolbox application server access: QIBM_DB_ZDA. . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.28, "t": 383.25, "r": 547.59, "b": 393.92, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 8", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 142.79, "t": 395.06, "r": 497.31, "b": 405.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.3 Database Administrator function: QIBM_DB_SQLADM . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.43, "t": 394.69, "r": 547.5, "b": 405.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 149.22, "t": 406.45, "r": 497.31, "b": 418.34, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.4 Database Information function: QIBM_DB_SYSMON . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.75, "t": 407.55, "r": 547.67, "b": 418.19, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 146.28, "t": 419.72, "r": 497.28, "b": 430.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.5 Security Administrator function: QIBM_DB_SECADM . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.17, "t": 420.33, "r": 547.73, "b": 430.94, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 9", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 148.77, "t": 431.07, "r": 497.31, "b": 443.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.6 Change Function Usage CL command. . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.81, "t": 432.33, "r": 547.75, "b": 442.94, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 139.27, "t": 444.17, "r": 497.57, "b": 454.8, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.7 Verifying function usage IDs for RCAC with the FUNCTION_USAGE view", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.57, "t": 445.59, "r": 547.79, "b": 456.23, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 134.74, "t": 456.99, "r": 497.33, "b": 468.32, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.2 Separation of duties . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.56, "t": 457.52, "r": 547.76, "b": 468.15, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 135.67, "t": 479.26, "r": 497.34, "b": 490.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Chapter 3. Row and Column Access Control . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.71, "t": 480.47, "r": 547.86, "b": 491.09, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "13", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 136.36, "t": 493.6, "r": 497.29, "b": 510.26, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.1 Explanation of RCAC and the concept of access control . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.49, "t": 494.23, "r": 547.9, "b": 504.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "14", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 141.05, "t": 503.86, "r": 497.28, "b": 515.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.1.1 Row permission and column mask definitions . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.87, "t": 504.79, "r": 547.91, "b": 515.41, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "14", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 146.38, "t": 516.06, "r": 497.25, "b": 527.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.1.2 Enabling and activating RCAC . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.75, "t": 517.05, "r": 547.86, "b": 527.61, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "16", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 138.47, "t": 528.54, "r": 497.29, "b": 540.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.2 Special registers and built-in global variables . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.91, "t": 529.33, "r": 547.79, "b": 540.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "18", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 144.76, "t": 541.55, "r": 497.3, "b": 552.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.2.1 Special registers . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.57, "t": 541.59, "r": 547.72, "b": 552.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "18", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 147.58, "t": 554.81, "r": 497.31, "b": 565.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.2.2 Built-in global variables . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.23, "t": 555.44, "r": 547.61, "b": 566.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "19", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 136.68, "t": 566.58, "r": 497.24, "b": 577.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 31, "end_row_offset_idx": 32, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.3 VERIFY_GROUP_FOR_USER function. . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.14, "t": 566.73, "r": 547.6, "b": 577.41, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 31, "end_row_offset_idx": 32, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "20", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 135.05, "t": 581.1, "r": 497.32, "b": 591.78, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 32, "end_row_offset_idx": 33, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.4 Establishing and controlling accessibility by using the RCAC rule text. . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.67, "t": 580.32, "r": 547.48, "b": 591.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 32, "end_row_offset_idx": 33, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "21", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 140.32, "t": 604.16, "r": 497.35, "b": 615.37, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 34, "end_row_offset_idx": 35, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6 Human resources example . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 140.71, "t": 602.84, "r": 359.85, "b": 613.62, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 33, "end_row_offset_idx": 34, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": ". . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.3, "t": 603.82, "r": 547.12, "b": 614.66, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 34, "end_row_offset_idx": 35, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "22", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 146.29, "t": 618.64, "r": 497.28, "b": 635.79, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 35, "end_row_offset_idx": 36, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.1 Assigning the QIBM_DB_SECADM function ID to the consultants. . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.45, "t": 618.64, "r": 547.16, "b": 639.32, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 35, "end_row_offset_idx": 36, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23 23", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 147.88, "t": 628.86, "r": 497.3, "b": 640.33, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 36, "end_row_offset_idx": 37, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.2 Creating group profiles for the users and their roles. . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 150.04, "t": 641.13, "r": 497.28, "b": 652.87, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 37, "end_row_offset_idx": 38, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.3 Demonstrating data access without RCAC. . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.75, "t": 641.6, "r": 547.01, "b": 652.22, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 37, "end_row_offset_idx": 38, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "24", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 152.02, "t": 654.2, "r": 497.29, "b": 665.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 38, "end_row_offset_idx": 39, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.4 Defining and creating row permissions . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.53, "t": 654.57, "r": 547.04, "b": 664.97, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 38, "end_row_offset_idx": 39, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "25", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 153.64, "t": 666.14, "r": 497.3, "b": 677.83, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 39, "end_row_offset_idx": 40, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.5 Defining and creating column masks . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.36, "t": 665.36, "r": 546.29, "b": 675.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 39, "end_row_offset_idx": 40, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "26", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 157.05, "t": 679.64, "r": 497.32, "b": 690.37, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 40, "end_row_offset_idx": 41, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.6 Activating RCAC. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.61, "t": 683.05, "r": 546.47, "b": 693.61, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 40, "end_row_offset_idx": 41, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "28", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 155.22, "t": 704.9, "r": 497.25, "b": 715.41, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 42, "end_row_offset_idx": 43, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.8 RCAC . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 156.69, "t": 704.87, "r": 386.37, "b": 715.4, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 41, "end_row_offset_idx": 42, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Demonstrating data access with a view and", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 531.63, "t": 705.31, "r": 547.03, "b": 716.07, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 42, "end_row_offset_idx": 43, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "32", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 43, "num_cols": 2, "grid": [[{"bbox": {"l": 135.94, "t": 132.65, "r": 497.33, "b": 144.51, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Notices . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.53, "t": 133.25, "r": 547.33, "b": 143.68, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". vii", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 136.2, "t": 143.81, "r": 497.29, "b": 154.53, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Trademarks . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 532.72, "t": 145.24, "r": 547.26, "b": 156.01, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "viii", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 136.03, "t": 165.3, "r": 497.36, "b": 176.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "DB2 for i Center of Excellence . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.97, "t": 165.18, "r": 547.47, "b": 176.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". ix", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 136.02, "t": 188.17, "r": 497.33, "b": 199.42, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Preface . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.61, "t": 188.05, "r": 546.93, "b": 198.54, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". xi", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 135.96, "t": 200.2, "r": 497.31, "b": 211.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Authors. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.46, "t": 200.04, "r": 547.17, "b": 211.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". xi", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 136.06, "t": 213.48, "r": 497.33, "b": 224.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Now you can become a published author, too! . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.89, "t": 213.67, "r": 547.26, "b": 224.11, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "xiii", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 135.36, "t": 225.56, "r": 497.31, "b": 236.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Comments welcome. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.87, "t": 226.24, "r": 547.04, "b": 236.61, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "xiii", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 134.98, "t": 238.64, "r": 497.3, "b": 249.32, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Stay connected to IBM Redbooks . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.3, "t": 239.21, "r": 546.87, "b": 249.57, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "xiv", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 134.33, "t": 260.01, "r": 497.34, "b": 271.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Chapter 1. Securing and protecting IBM DB2 data . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.45, "t": 259.78, "r": 547.04, "b": 271.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 1", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 135.42, "t": 272.4, "r": 497.33, "b": 283.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.1 Security fundamentals. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.45, "t": 272.25, "r": 546.86, "b": 283.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 2", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 135.55, "t": 285.19, "r": 497.33, "b": 296.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.2 Current state of IBM i security. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.44, "t": 284.81, "r": 546.98, "b": 296.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 2", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 135.66, "t": 297.92, "r": 497.3, "b": 308.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.3 DB2 for i security controls . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.41, "t": 297.42, "r": 547.21, "b": 308.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 3", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 152.14, "t": 311.22, "r": 497.28, "b": 321.64, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.3.1 Existing row and column control . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.4, "t": 310.34, "r": 547.21, "b": 321.32, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 4", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 146.73, "t": 323.04, "r": 497.31, "b": 333.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "1.3.2 New controls: Row and Column Access Control. . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.44, "t": 323.21, "r": 547.54, "b": 333.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 13, "end_row_offset_idx": 14, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 5", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 137.53, "t": 346.06, "r": 497.34, "b": 356.66, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Chapter 2. Roles and separation of duties . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.45, "t": 344.64, "r": 547.62, "b": 356.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 14, "end_row_offset_idx": 15, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 7", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 136.03, "t": 356.23, "r": 497.31, "b": 368.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1 Roles. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.82, "t": 356.92, "r": 547.59, "b": 367.53, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 15, "end_row_offset_idx": 16, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 8", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 149.04, "t": 369.94, "r": 497.27, "b": 380.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.1 DDM and DRDA application server access: QIBM_DB_DDMDRDA . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.98, "t": 369.45, "r": 547.53, "b": 380.03, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 16, "end_row_offset_idx": 17, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 8", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 142.25, "t": 382.91, "r": 497.26, "b": 393.57, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.2 Toolbox application server access: QIBM_DB_ZDA. . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.28, "t": 383.25, "r": 547.59, "b": 393.92, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 17, "end_row_offset_idx": 18, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 8", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 142.79, "t": 395.06, "r": 497.31, "b": 405.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.3 Database Administrator function: QIBM_DB_SQLADM . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.43, "t": 394.69, "r": 547.5, "b": 405.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 18, "end_row_offset_idx": 19, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 9", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 149.22, "t": 406.45, "r": 497.31, "b": 418.34, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.4 Database Information function: QIBM_DB_SYSMON . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.75, "t": 407.55, "r": 547.67, "b": 418.19, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 19, "end_row_offset_idx": 20, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 9", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 146.28, "t": 419.72, "r": 497.28, "b": 430.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.5 Security Administrator function: QIBM_DB_SECADM . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.17, "t": 420.33, "r": 547.73, "b": 430.94, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 20, "end_row_offset_idx": 21, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": ". 9", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 148.77, "t": 431.07, "r": 497.31, "b": 443.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.6 Change Function Usage CL command. . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.81, "t": 432.33, "r": 547.75, "b": 442.94, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 21, "end_row_offset_idx": 22, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 139.27, "t": 444.17, "r": 497.57, "b": 454.8, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.1.7 Verifying function usage IDs for RCAC with the FUNCTION_USAGE view", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.57, "t": 445.59, "r": 547.79, "b": 456.23, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 22, "end_row_offset_idx": 23, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 134.74, "t": 456.99, "r": 497.33, "b": 468.32, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2.2 Separation of duties . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.56, "t": 457.52, "r": 547.76, "b": 468.15, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 23, "end_row_offset_idx": 24, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "10", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 135.67, "t": 479.26, "r": 497.34, "b": 490.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Chapter 3. Row and Column Access Control . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.71, "t": 480.47, "r": 547.86, "b": 491.09, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 24, "end_row_offset_idx": 25, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "13", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 136.36, "t": 493.6, "r": 497.29, "b": 510.26, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.1 Explanation of RCAC and the concept of access control . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.49, "t": 494.23, "r": 547.9, "b": 504.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 25, "end_row_offset_idx": 26, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "14", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 141.05, "t": 503.86, "r": 497.28, "b": 515.36, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.1.1 Row permission and column mask definitions . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.87, "t": 504.79, "r": 547.91, "b": 515.41, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 26, "end_row_offset_idx": 27, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "14", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 146.38, "t": 516.06, "r": 497.25, "b": 527.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.1.2 Enabling and activating RCAC . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.75, "t": 517.05, "r": 547.86, "b": 527.61, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 27, "end_row_offset_idx": 28, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "16", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 138.47, "t": 528.54, "r": 497.29, "b": 540.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.2 Special registers and built-in global variables . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.91, "t": 529.33, "r": 547.79, "b": 540.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 28, "end_row_offset_idx": 29, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "18", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 144.76, "t": 541.55, "r": 497.3, "b": 552.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.2.1 Special registers . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.57, "t": 541.59, "r": 547.72, "b": 552.3, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 29, "end_row_offset_idx": 30, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "18", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 147.58, "t": 554.81, "r": 497.31, "b": 565.38, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.2.2 Built-in global variables . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 535.23, "t": 555.44, "r": 547.61, "b": 566.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 30, "end_row_offset_idx": 31, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "19", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 136.68, "t": 566.58, "r": 497.24, "b": 577.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 31, "end_row_offset_idx": 32, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.3 VERIFY_GROUP_FOR_USER function. . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.14, "t": 566.73, "r": 547.6, "b": 577.41, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 31, "end_row_offset_idx": 32, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "20", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 135.05, "t": 581.1, "r": 497.32, "b": 591.78, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 32, "end_row_offset_idx": 33, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.4 Establishing and controlling accessibility by using the RCAC rule text. . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.67, "t": 580.32, "r": 547.48, "b": 591.0, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 32, "end_row_offset_idx": 33, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "21", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 140.71, "t": 602.84, "r": 359.85, "b": 613.62, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 33, "end_row_offset_idx": 34, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": ". . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 33, "end_row_offset_idx": 34, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 140.32, "t": 604.16, "r": 497.35, "b": 615.37, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 34, "end_row_offset_idx": 35, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6 Human resources example . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.3, "t": 603.82, "r": 547.12, "b": 614.66, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 34, "end_row_offset_idx": 35, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "22", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 146.29, "t": 618.64, "r": 497.28, "b": 635.79, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 35, "end_row_offset_idx": 36, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.1 Assigning the QIBM_DB_SECADM function ID to the consultants. . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.45, "t": 618.64, "r": 547.16, "b": 639.32, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 35, "end_row_offset_idx": 36, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "23 23", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 147.88, "t": 628.86, "r": 497.3, "b": 640.33, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 36, "end_row_offset_idx": 37, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.2 Creating group profiles for the users and their roles. . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 36, "end_row_offset_idx": 37, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 150.04, "t": 641.13, "r": 497.28, "b": 652.87, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 37, "end_row_offset_idx": 38, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.3 Demonstrating data access without RCAC. . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.75, "t": 641.6, "r": 547.01, "b": 652.22, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 37, "end_row_offset_idx": 38, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "24", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 152.02, "t": 654.2, "r": 497.29, "b": 665.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 38, "end_row_offset_idx": 39, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.4 Defining and creating row permissions . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.53, "t": 654.57, "r": 547.04, "b": 664.97, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 38, "end_row_offset_idx": 39, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "25", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 153.64, "t": 666.14, "r": 497.3, "b": 677.83, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 39, "end_row_offset_idx": 40, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.5 Defining and creating column masks . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 534.36, "t": 665.36, "r": 546.29, "b": 675.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 39, "end_row_offset_idx": 40, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "26", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 157.05, "t": 679.64, "r": 497.32, "b": 690.37, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 40, "end_row_offset_idx": 41, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.6 Activating RCAC. . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 533.61, "t": 683.05, "r": 546.47, "b": 693.61, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 40, "end_row_offset_idx": 41, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "28", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 156.69, "t": 704.87, "r": 386.37, "b": 715.4, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 41, "end_row_offset_idx": 42, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Demonstrating data access with a view and", "column_header": false, "row_header": true, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 41, "end_row_offset_idx": 42, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 155.22, "t": 704.9, "r": 497.25, "b": 715.41, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 42, "end_row_offset_idx": 43, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "3.6.8 RCAC . . . . . . . . . . . . . . . . .", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 531.63, "t": 705.31, "r": 547.03, "b": 716.07, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 42, "end_row_offset_idx": 43, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "32", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/90"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 8, "bbox": {"l": 135.52, "t": 502.27, "r": 545.87, "b": 349.95, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/90"}], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 142.8, "t": 296.54, "r": 176.26, "b": 304.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Column name", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 216.81, "t": 296.54, "r": 236.29, "b": 304.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Data type", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 289.48, "t": 296.54, "r": 338.9, "b": 304.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Description", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 142.8, "t": 315.56, "r": 203.24, "b": 323.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "FUNCTION_ID", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.78, "t": 315.56, "r": 276.01, "b": 323.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(30)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.46, "t": 315.56, "r": 298.45, "b": 323.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "ID of the function.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 142.8, "t": 334.52, "r": 198.69, "b": 342.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "USER_NAME", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.74, "t": 334.52, "r": 275.94, "b": 342.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(10)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.38, "t": 334.52, "r": 313.39, "b": 342.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Name of the user profile that has a usage setting for this function.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 142.8, "t": 364.52, "r": 174.0, "b": 372.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "USAGE", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.77, "t": 364.52, "r": 270.99, "b": 372.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(7)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.42, "t": 364.52, "r": 315.42, "b": 372.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Usage setting: /SM590000 ALLOWED: The user profile is allowed to use the function. /SM590000 DENIED: The user profile is not allowed to use the function.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 142.8, "t": 405.56, "r": 196.24, "b": 413.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "USER_TYPE", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.75, "t": 405.56, "r": 271.01, "b": 413.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.43, "t": 405.56, "r": 308.37, "b": 413.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Type of user profile: /SM590000 USER: The user profile is a user. /SM590000 GROUP: The user profile is a group.", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 5, "num_cols": 3, "grid": [[{"bbox": {"l": 142.8, "t": 296.54, "r": 176.26, "b": 304.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Column name", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 216.81, "t": 296.54, "r": 236.29, "b": 304.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Data type", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 289.48, "t": 296.54, "r": 338.9, "b": 304.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Description", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 142.8, "t": 315.56, "r": 203.24, "b": 323.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "FUNCTION_ID", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.78, "t": 315.56, "r": 276.01, "b": 323.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(30)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.46, "t": 315.56, "r": 298.45, "b": 323.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "ID of the function.", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 142.8, "t": 334.52, "r": 198.69, "b": 342.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "USER_NAME", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.74, "t": 334.52, "r": 275.94, "b": 342.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(10)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.38, "t": 334.52, "r": 313.39, "b": 342.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Name of the user profile that has a usage setting for this function.", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 142.8, "t": 364.52, "r": 174.0, "b": 372.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "USAGE", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.77, "t": 364.52, "r": 270.99, "b": 372.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(7)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.42, "t": 364.52, "r": 315.42, "b": 372.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Usage setting: /SM590000 ALLOWED: The user profile is allowed to use the function. /SM590000 DENIED: The user profile is not allowed to use the function.", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 142.8, "t": 405.56, "r": 196.24, "b": 413.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "USER_TYPE", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 216.75, "t": 405.56, "r": 271.01, "b": 413.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(5)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.43, "t": 405.56, "r": 308.37, "b": 413.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Type of user profile: /SM590000 USER: The user profile is a user. /SM590000 GROUP: The user profile is a group.", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/110"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 9, "bbox": {"l": 64.41, "t": 398.39, "r": 547.4, "b": 70.39, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/110"}], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 70.8, "t": 400.52, "r": 90.79, "b": 408.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "User action", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 424.94, "t": 447.56, "r": 433.26, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "*JOBCTL", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 450.14, "t": 401.59, "r": 458.46, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "QIBM_DB_SECADM", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 475.94, "t": 401.56, "r": 484.26, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "QIBM_DB_SQLADM", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 501.14, "t": 401.61, "r": 509.46, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "QIBM_DB_SYSMON", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 526.4, "t": 475.02, "r": 534.72, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "No Authority", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 498.96, "r": 84.3, "b": 506.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "SET CURRENT DEGREE (SQL statement)", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 498.56, "r": 435.0, "b": 506.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 498.56, "r": 486.0, "b": 506.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 517.92, "r": 102.24, "b": 525.56, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CHGQRYA command targeting a different user's job", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 517.52, "r": 435.0, "b": 525.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 517.52, "r": 486.0, "b": 525.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 536.94, "r": 106.74, "b": 544.58, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "STRDBMON or ENDDBMON commands targeting a different user's job", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 536.54, "r": 435.0, "b": 544.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 536.54, "r": 486.0, "b": 544.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 555.96, "r": 106.74, "b": 563.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "STRDBMON or ENDDBMON commands targeting a job that matches the current user", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 555.56, "r": 435.0, "b": 563.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 555.56, "r": 486.0, "b": 563.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.26, "t": 555.56, "r": 511.26, "b": 563.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 530.76, "t": 555.56, "r": 536.76, "b": 563.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 574.52, "r": 122.63, "b": 582.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "QUSRJOBI() API format 900 or System i Navigator's SQL Details for Job", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 574.52, "r": 435.0, "b": 582.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 574.52, "r": 486.0, "b": 582.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.26, "t": 574.52, "r": 511.26, "b": 582.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 593.54, "r": 95.23, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Visual Explain within Run SQL scripts", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 593.54, "r": 435.0, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 593.54, "r": 486.0, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.26, "t": 593.54, "r": 511.26, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 530.76, "t": 593.54, "r": 536.76, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 612.56, "r": 95.21, "b": 620.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Visual Explain outside of Run SQL scripts", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 612.56, "r": 435.0, "b": 620.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 612.56, "r": 486.0, "b": 620.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 631.52, "r": 110.5, "b": 639.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ANALYZE PLAN CACHE procedure", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 631.52, "r": 435.0, "b": 639.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 631.52, "r": 486.0, "b": 639.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 650.54, "r": 97.26, "b": 658.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "DUMP PLAN CACHE procedure", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 650.54, "r": 435.0, "b": 658.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 650.54, "r": 486.0, "b": 658.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 669.56, "r": 105.78, "b": 677.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "MODIFY PLAN CACHE procedure", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 669.56, "r": 435.0, "b": 677.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 669.56, "r": 486.0, "b": 677.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 688.58, "r": 105.76, "b": 696.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "MODIFY PLAN CACHE PROPERTIES procedure (currently does not check authority)", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 688.58, "r": 435.0, "b": 696.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 688.58, "r": 486.0, "b": 696.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 707.54, "r": 109.25, "b": 715.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CHANGE PLAN CACHE SIZE procedure (currently does not check authority)", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 707.54, "r": 435.0, "b": 715.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 707.54, "r": 486.0, "b": 715.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 13, "num_cols": 6, "grid": [[{"bbox": {"l": 70.8, "t": 400.52, "r": 90.79, "b": 408.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "User action", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 424.94, "t": 447.56, "r": 433.26, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "*JOBCTL", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 450.14, "t": 401.59, "r": 458.46, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "QIBM_DB_SECADM", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 475.94, "t": 401.56, "r": 484.26, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "QIBM_DB_SQLADM", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 501.14, "t": 401.61, "r": 509.46, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "QIBM_DB_SYSMON", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 526.4, "t": 475.02, "r": 534.72, "b": 487.02, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "No Authority", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 498.96, "r": 84.3, "b": 506.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "SET CURRENT DEGREE (SQL statement)", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 498.56, "r": 435.0, "b": 506.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 498.56, "r": 486.0, "b": 506.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 517.92, "r": 102.24, "b": 525.56, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CHGQRYA command targeting a different user's job", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 517.52, "r": 435.0, "b": 525.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 517.52, "r": 486.0, "b": 525.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 536.94, "r": 106.74, "b": 544.58, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "STRDBMON or ENDDBMON commands targeting a different user's job", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 536.54, "r": 435.0, "b": 544.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 536.54, "r": 486.0, "b": 544.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 555.96, "r": 106.74, "b": 563.6, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "STRDBMON or ENDDBMON commands targeting a job that matches the current user", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 555.56, "r": 435.0, "b": 563.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 555.56, "r": 486.0, "b": 563.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.26, "t": 555.56, "r": 511.26, "b": 563.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 530.76, "t": 555.56, "r": 536.76, "b": 563.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "X", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 574.52, "r": 122.63, "b": 582.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "QUSRJOBI() API format 900 or System i Navigator's SQL Details for Job", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 574.52, "r": 435.0, "b": 582.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 574.52, "r": 486.0, "b": 582.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.26, "t": 574.52, "r": 511.26, "b": 582.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 593.54, "r": 95.23, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Visual Explain within Run SQL scripts", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 593.54, "r": 435.0, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 593.54, "r": 486.0, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 505.26, "t": 593.54, "r": 511.26, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 530.76, "t": 593.54, "r": 536.76, "b": 601.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "X", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 612.56, "r": 95.21, "b": 620.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Visual Explain outside of Run SQL scripts", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 612.56, "r": 435.0, "b": 620.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 612.56, "r": 486.0, "b": 620.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 631.52, "r": 110.5, "b": 639.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ANALYZE PLAN CACHE procedure", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 631.52, "r": 435.0, "b": 639.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 631.52, "r": 486.0, "b": 639.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 650.54, "r": 97.26, "b": 658.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "DUMP PLAN CACHE procedure", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 650.54, "r": 435.0, "b": 658.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 650.54, "r": 486.0, "b": 658.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 669.56, "r": 105.78, "b": 677.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "MODIFY PLAN CACHE procedure", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 669.56, "r": 435.0, "b": 677.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 669.56, "r": 486.0, "b": 677.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 10, "end_row_offset_idx": 11, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 688.58, "r": 105.76, "b": 696.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "MODIFY PLAN CACHE PROPERTIES procedure (currently does not check authority)", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 688.58, "r": 435.0, "b": 696.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 688.58, "r": 486.0, "b": 696.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 11, "end_row_offset_idx": 12, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 707.54, "r": 109.25, "b": 715.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CHANGE PLAN CACHE SIZE procedure (currently does not check authority)", "column_header": false, "row_header": true, "row_section": false}, {"bbox": {"l": 429.0, "t": 707.54, "r": 435.0, "b": 715.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 480.0, "t": 707.54, "r": 486.0, "b": 715.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "X", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 12, "end_row_offset_idx": 13, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/170"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 11, "bbox": {"l": 134.55, "t": 688.58, "r": 542.05, "b": 587.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/170"}], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 142.8, "t": 110.54, "r": 174.25, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Special register", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 230.19, "t": 110.54, "r": 294.63, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Corresponding value", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 142.8, "t": 129.5, "r": 167.77, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "USER or SESSION_USER", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 230.22, "t": 129.5, "r": 245.71, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "The effective user of the thread excluding adopted authority.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 142.8, "t": 159.56, "r": 216.66, "b": 167.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CURRENT_USER", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 230.2, "t": 159.56, "r": 245.67, "b": 167.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "The effective user of the thread including adopted authority. When no adopted authority is present, this has the same value as USER.", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 142.8, "t": 189.56, "r": 209.74, "b": 197.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "SYSTEM_USER", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 230.25, "t": 189.56, "r": 245.74, "b": 197.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "The authorization ID that initiated the connection.", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 4, "num_cols": 2, "grid": [[{"bbox": {"l": 142.8, "t": 110.54, "r": 174.25, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Special register", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 230.19, "t": 110.54, "r": 294.63, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Corresponding value", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 142.8, "t": 129.5, "r": 167.77, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "USER or SESSION_USER", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 230.22, "t": 129.5, "r": 245.71, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "The effective user of the thread excluding adopted authority.", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 142.8, "t": 159.56, "r": 216.66, "b": 167.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CURRENT_USER", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 230.2, "t": 159.56, "r": 245.67, "b": 167.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "The effective user of the thread including adopted authority. When no adopted authority is present, this has the same value as USER.", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 142.8, "t": 189.56, "r": 209.74, "b": 197.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "SYSTEM_USER", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 230.25, "t": 189.56, "r": 245.74, "b": 197.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "The authorization ID that initiated the connection.", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/194"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 12, "bbox": {"l": 63.56, "t": 687.77, "r": 548.57, "b": 495.78, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/194"}], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 70.8, "t": 110.54, "r": 98.79, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Global variable", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 202.89, "t": 110.54, "r": 223.35, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Type", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 281.82, "t": 110.54, "r": 331.35, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Description", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 129.5, "r": 132.73, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CLIENT_HOST", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.89, "t": 129.5, "r": 267.08, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(255)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.85, "t": 129.5, "r": 300.33, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Host name of the current client as returned by the system", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 148.52, "r": 140.67, "b": 156.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CLIENT_IPADDR", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.87, "t": 148.52, "r": 267.08, "b": 156.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.85, "t": 148.52, "r": 290.35, "b": 156.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "IP address of the current client as returned by the system", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 167.54, "r": 132.48, "b": 175.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CLIENT_PORT", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.9, "t": 167.54, "r": 242.82, "b": 175.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "INTEGER", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.8, "t": 167.54, "r": 298.23, "b": 175.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Port used by the current client to communicate with the server", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 186.56, "r": 143.53, "b": 194.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PACKAGE_NAME", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.81, "t": 186.56, "r": 267.09, "b": 194.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.85, "t": 186.56, "r": 305.85, "b": 194.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Name of the currently running package", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 205.52, "r": 156.04, "b": 213.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PACKAGE_SCHEMA", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.84, "t": 205.52, "r": 267.11, "b": 213.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.87, "t": 205.52, "r": 314.83, "b": 213.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Schema name of the currently running package", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 224.54, "r": 157.92, "b": 232.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PACKAGE_VERSION", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.72, "t": 224.54, "r": 262.01, "b": 232.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(64)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.75, "t": 224.54, "r": 311.48, "b": 232.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Version identifier of the currently running package", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 243.56, "r": 154.44, "b": 251.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ROUTINE_SCHEMA", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.79, "t": 243.56, "r": 267.11, "b": 251.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.87, "t": 243.56, "r": 314.86, "b": 251.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Schema name of the currently running routine", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 262.52, "r": 188.45, "b": 270.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ROUTINE_SPECIFIC_NAME", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.84, "t": 262.52, "r": 267.04, "b": 270.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.81, "t": 262.52, "r": 305.74, "b": 270.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Name of the currently running routine", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 70.8, "t": 281.54, "r": 139.45, "b": 289.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ROUTINE_TYPE", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.75, "t": 281.54, "r": 239.31, "b": 289.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "CHAR(1)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.79, "t": 281.54, "r": 300.7, "b": 289.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Type of the currently running routine", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 10, "num_cols": 3, "grid": [[{"bbox": {"l": 70.8, "t": 110.54, "r": 98.79, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Global variable", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 202.89, "t": 110.54, "r": 223.35, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Type", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 281.82, "t": 110.54, "r": 331.35, "b": 118.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Description", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 129.5, "r": 132.73, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CLIENT_HOST", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.89, "t": 129.5, "r": 267.08, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(255)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.85, "t": 129.5, "r": 300.33, "b": 137.82, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Host name of the current client as returned by the system", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 148.52, "r": 140.67, "b": 156.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CLIENT_IPADDR", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.87, "t": 148.52, "r": 267.08, "b": 156.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.85, "t": 148.52, "r": 290.35, "b": 156.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "IP address of the current client as returned by the system", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 167.54, "r": 132.48, "b": 175.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "CLIENT_PORT", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.9, "t": 167.54, "r": 242.82, "b": 175.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "INTEGER", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.8, "t": 167.54, "r": 298.23, "b": 175.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Port used by the current client to communicate with the server", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 186.56, "r": 143.53, "b": 194.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PACKAGE_NAME", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.81, "t": 186.56, "r": 267.09, "b": 194.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.85, "t": 186.56, "r": 305.85, "b": 194.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Name of the currently running package", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 205.52, "r": 156.04, "b": 213.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PACKAGE_SCHEMA", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.84, "t": 205.52, "r": 267.11, "b": 213.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.87, "t": 205.52, "r": 314.83, "b": 213.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Schema name of the currently running package", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 224.54, "r": 157.92, "b": 232.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PACKAGE_VERSION", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.72, "t": 224.54, "r": 262.01, "b": 232.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(64)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.75, "t": 224.54, "r": 311.48, "b": 232.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Version identifier of the currently running package", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 243.56, "r": 154.44, "b": 251.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ROUTINE_SCHEMA", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.79, "t": 243.56, "r": 267.11, "b": 251.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.87, "t": 243.56, "r": 314.86, "b": 251.88, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Schema name of the currently running routine", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 262.52, "r": 188.45, "b": 270.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ROUTINE_SPECIFIC_NAME", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.84, "t": 262.52, "r": 267.04, "b": 270.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "VARCHAR(128)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.81, "t": 262.52, "r": 305.74, "b": 270.84, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Name of the currently running routine", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 70.8, "t": 281.54, "r": 139.45, "b": 289.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "ROUTINE_TYPE", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 202.75, "t": 281.54, "r": 239.31, "b": 289.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "CHAR(1)", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 281.79, "t": 281.54, "r": 300.7, "b": 289.86, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 9, "end_row_offset_idx": 10, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Type of the currently running routine", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 612.0, "height": 792.0}, "page_no": 1}, "2": {"size": {"width": 612.0, "height": 792.0}, "page_no": 2}, "3": {"size": {"width": 612.0, "height": 792.0}, "page_no": 3}, "4": {"size": {"width": 612.0, "height": 792.0}, "page_no": 4}, "5": {"size": {"width": 612.0, "height": 792.0}, "page_no": 5}, "6": {"size": {"width": 612.0, "height": 792.0}, "page_no": 6}, "7": {"size": {"width": 612.0, "height": 792.0}, "page_no": 7}, "8": {"size": {"width": 612.0, "height": 792.0}, "page_no": 8}, "9": {"size": {"width": 612.0, "height": 792.0}, "page_no": 9}, "10": {"size": {"width": 612.0, "height": 792.0}, "page_no": 10}, "11": {"size": {"width": 612.0, "height": 792.0}, "page_no": 11}, "12": {"size": {"width": 612.0, "height": 792.0}, "page_no": 12}, "13": {"size": {"width": 612.0, "height": 792.0}, "page_no": 13}, "14": {"size": {"width": 612.0, "height": 792.0}, "page_no": 14}, "15": {"size": {"width": 612.0, "height": 792.0}, "page_no": 15}, "16": {"size": {"width": 612.0, "height": 792.0}, "page_no": 16}, "17": {"size": {"width": 612.0, "height": 792.0}, "page_no": 17}, "18": {"size": {"width": 612.0, "height": 792.0}, "page_no": 18}}}
{"_name": "", "type": "pdf-document", "description": {"title": null, "abstract": null, "authors": null, "affiliations": null, "subjects": null, "keywords": null, "publication_date": null, "languages": null, "license": null, "publishers": null, "url_refs": null, "references": null, "publication": null, "reference_count": null, "citation_count": null, "citation_date": null, "advanced": null, "analytics": null, "logs": [], "collection": null, "acquisition": null}, "file-info": {"filename": "code_and_formula.pdf", "filename-prov": null, "document-hash": "821fdb0aa6d749c0adf24279b59d8030f6725a82e6566b5710c69c635d6a5e5f", "#-pages": 2, "collection-name": null, "description": null, "page-hashes": [{"hash": "321a2b1c88480306c4b1861c6db6764166f689017eb00dc38cfd50b526a68274", "model": "default", "page": 1}, {"hash": "6b116ec9598ebf64cc6de54e9ab2e896990e68d68a5e0fdf6b6bd314b29cdd5d", "model": "default", "page": 2}]}, "main-text": [{"prov": [{"bbox": [133.76801, 654.45184, 315.91595, 667.19122], "page": 1, "span": [0, 23], "__ref_s3_data": null}], "text": "JavaScript Code Example", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [133.76801, 501.97412, 477.48276, 642.32806], "page": 1, "span": [0, 887], "__ref_s3_data": null}], "text": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [133.76801, 454.15417, 477.47876, 498.86591], "page": 1, "span": [0, 298], "__ref_s3_data": null}], "text": "Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet,", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.239, 385.25446, 263.22409, 425.6004899999999], "page": 1, "span": [0, 60], "__ref_s3_data": null}], "text": "function add(a, b) { return a + b; } console.log(add(3, 5));", "type": "paragraph", "payload": null, "name": "Code", "font": null}, {"prov": [{"bbox": [223.15500000000003, 433.23218, 388.09375, 442.07895], "page": 1, "span": [0, 36], "__ref_s3_data": null}], "text": "Listing 1: Simple JavaScript Program", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [133.76801, 232.58536000000004, 477.48172000000005, 372.93902999999995], "page": 1, "span": [0, 887], "__ref_s3_data": null}], "text": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [133.76801, 184.76436, 477.47876, 229.47713999999996], "page": 1, "span": [0, 298], "__ref_s3_data": null}], "text": "Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet,", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [133.76801021944917, 704.341863888975, 191.5272403142044, 717.0812439593145], "page": 2, "span": [0, 7], "__ref_s3_data": null}], "text": "Formula", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [133.76801021944917, 551.8641430470798, 477.48276078332026, 692.2180838220343], "page": 2, "span": [0, 887], "__ref_s3_data": null}], "text": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [133.76801021944917, 492.0881027170305, 477.48163078331845, 548.7559230299179], "page": 2, "span": [0, 369], "__ref_s3_data": null}], "text": "Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [280.5540204602546, 468.178102585013, 330.6965605425145, 479.06467264512247], "page": 2, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "equation", "payload": null, "name": "Formula", "font": null}, {"prov": [{"bbox": [133.76799021944913, 318.7382217598911, 477.4816907833186, 459.091862534844], "page": 2, "span": [0, 887], "__ref_s3_data": null}], "text": "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [133.76799021944913, 247.0072913638337, 477.48370078332186, 315.6300017427293], "page": 2, "span": [0, 415], "__ref_s3_data": null}], "text": "Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [133.76799021944913, 175.27629096777594, 477.48370078332186, 243.8990813466719], "page": 2, "span": [0, 415], "__ref_s3_data": null}], "text": "Duis autem vel eum iriure dolor in hendrerit in vulputate velit esse molestie consequat, vel illum dolore eu feugiat nulla facilisis at vero eros et accumsan et iusto odio dignissim qui blandit praesent luptatum zzril delenit augue duis dolore te feugait nulla facilisi. Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod tincidunt ut laoreet dolore magna aliquam erat volutpat.", "type": "paragraph", "payload": null, "name": "Text", "font": null}], "figures": [], "tables": [], "bitmaps": null, "equations": [], "footnotes": [], "page-dimensions": [{"height": 792.0, "page": 1, "width": 612.0}, {"height": 841.8900146484375, "page": 2, "width": 595.2760009765625}], "page-footers": [], "page-headers": [], "_s3_data": null, "identifiers": null}
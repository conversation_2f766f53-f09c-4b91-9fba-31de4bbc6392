{"base_code": {"magic": "pir", "trainable": true, "version": 1}, "program": {"regions": [{"#": "region_0", "blocks": [{"#": "block_0", "args": [], "ops": [{"#": "p", "A": [0, 1, 1, "linear_8.b_0"], "DA": [], "O": {"%": 1, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [15631], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_8.w_0"], "DA": [], "O": {"%": 2, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 15631], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.w_2"], "DA": [], "O": {"%": 3, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.w_1"], "DA": [], "O": {"%": 4, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.b_0"], "DA": [], "O": {"%": 5, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.w_0"], "DA": [], "O": {"%": 6, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_51.w_0"], "DA": [], "O": {"%": 7, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 128, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.w_2"], "DA": [], "O": {"%": 8, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.w_1"], "DA": [], "O": {"%": 9, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.b_0"], "DA": [], "O": {"%": 10, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.w_0"], "DA": [], "O": {"%": 11, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_50.w_0"], "DA": [], "O": {"%": 12, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 2048, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_2"], "DA": [], "O": {"%": 13, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_1"], "DA": [], "O": {"%": 14, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.b_0"], "DA": [], "O": {"%": 15, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_0"], "DA": [], "O": {"%": 16, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_49.w_0"], "DA": [], "O": {"%": 17, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 120, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_4.b_0"], "DA": [], "O": {"%": 18, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_4.w_0"], "DA": [], "O": {"%": 19, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_7.b_0"], "DA": [], "O": {"%": 20, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_7.w_0"], "DA": [], "O": {"%": 21, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_6.b_0"], "DA": [], "O": {"%": 22, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_6.w_0"], "DA": [], "O": {"%": 23, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_3.b_0"], "DA": [], "O": {"%": 24, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_3.w_0"], "DA": [], "O": {"%": 25, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_5.b_0"], "DA": [], "O": {"%": 26, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_5.w_0"], "DA": [], "O": {"%": 27, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_4.b_0"], "DA": [], "O": {"%": 28, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_4.w_0"], "DA": [], "O": {"%": 29, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_2.b_0"], "DA": [], "O": {"%": 30, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_2.w_0"], "DA": [], "O": {"%": 31, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_3.b_0"], "DA": [], "O": {"%": 32, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_3.w_0"], "DA": [], "O": {"%": 33, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_2.b_0"], "DA": [], "O": {"%": 34, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_2.w_0"], "DA": [], "O": {"%": 35, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_1.b_0"], "DA": [], "O": {"%": 36, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_1.w_0"], "DA": [], "O": {"%": 37, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_1.b_0"], "DA": [], "O": {"%": 38, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_1.w_0"], "DA": [], "O": {"%": 39, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_0.b_0"], "DA": [], "O": {"%": 40, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_0.w_0"], "DA": [], "O": {"%": 41, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_0.b_0"], "DA": [], "O": {"%": 42, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_0.w_0"], "DA": [], "O": {"%": 43, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_2"], "DA": [], "O": {"%": 44, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_1"], "DA": [], "O": {"%": 45, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.b_0"], "DA": [], "O": {"%": 46, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_0"], "DA": [], "O": {"%": 47, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_48.w_0"], "DA": [], "O": {"%": 48, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 128, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_2"], "DA": [], "O": {"%": 49, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_1"], "DA": [], "O": {"%": 50, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.b_0"], "DA": [], "O": {"%": 51, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_0"], "DA": [], "O": {"%": 52, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_47.w_0"], "DA": [], "O": {"%": 53, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 1024, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_46.b_0"], "DA": [], "O": {"%": 54, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_46.w_0"], "DA": [], "O": {"%": 55, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_2"], "DA": [], "O": {"%": 56, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_1"], "DA": [], "O": {"%": 57, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.b_0"], "DA": [], "O": {"%": 58, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_0"], "DA": [], "O": {"%": 59, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_45.w_0"], "DA": [], "O": {"%": 60, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 2112, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_2"], "DA": [], "O": {"%": 61, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_1"], "DA": [], "O": {"%": 62, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.b_0"], "DA": [], "O": {"%": 63, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_0"], "DA": [], "O": {"%": 64, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_44.w_0"], "DA": [], "O": {"%": 65, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224, 224, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_2"], "DA": [], "O": {"%": 66, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_1"], "DA": [], "O": {"%": 67, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.b_0"], "DA": [], "O": {"%": 68, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_0"], "DA": [], "O": {"%": 69, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_43.w_0"], "DA": [], "O": {"%": 70, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224, 224, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_2"], "DA": [], "O": {"%": 71, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_1"], "DA": [], "O": {"%": 72, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.b_0"], "DA": [], "O": {"%": 73, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_0"], "DA": [], "O": {"%": 74, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_42.w_0"], "DA": [], "O": {"%": 75, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224, 224, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_2"], "DA": [], "O": {"%": 76, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_1"], "DA": [], "O": {"%": 77, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.b_0"], "DA": [], "O": {"%": 78, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_0"], "DA": [], "O": {"%": 79, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_41.w_0"], "DA": [], "O": {"%": 80, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224, 224, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_2"], "DA": [], "O": {"%": 81, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_1"], "DA": [], "O": {"%": 82, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.b_0"], "DA": [], "O": {"%": 83, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_0"], "DA": [], "O": {"%": 84, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_40.w_0"], "DA": [], "O": {"%": 85, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224, 224, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_2"], "DA": [], "O": {"%": 86, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_1"], "DA": [], "O": {"%": 87, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.b_0"], "DA": [], "O": {"%": 88, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_0"], "DA": [], "O": {"%": 89, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_39.w_0"], "DA": [], "O": {"%": 90, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224, 768, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_2"], "DA": [], "O": {"%": 91, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_1"], "DA": [], "O": {"%": 92, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.b_0"], "DA": [], "O": {"%": 93, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_0"], "DA": [], "O": {"%": 94, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_38.w_0"], "DA": [], "O": {"%": 95, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_37.b_0"], "DA": [], "O": {"%": 96, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_37.w_0"], "DA": [], "O": {"%": 97, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768, 768, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_2"], "DA": [], "O": {"%": 98, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_1"], "DA": [], "O": {"%": 99, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.b_0"], "DA": [], "O": {"%": 100, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_0"], "DA": [], "O": {"%": 101, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_36.w_0"], "DA": [], "O": {"%": 102, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768, 1920, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_2"], "DA": [], "O": {"%": 103, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_1"], "DA": [], "O": {"%": 104, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.b_0"], "DA": [], "O": {"%": 105, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_0"], "DA": [], "O": {"%": 106, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_35.w_0"], "DA": [], "O": {"%": 107, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_2"], "DA": [], "O": {"%": 108, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_1"], "DA": [], "O": {"%": 109, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.b_0"], "DA": [], "O": {"%": 110, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_0"], "DA": [], "O": {"%": 111, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_34.w_0"], "DA": [], "O": {"%": 112, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_2"], "DA": [], "O": {"%": 113, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_1"], "DA": [], "O": {"%": 114, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.b_0"], "DA": [], "O": {"%": 115, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_0"], "DA": [], "O": {"%": 116, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_33.w_0"], "DA": [], "O": {"%": 117, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_2"], "DA": [], "O": {"%": 118, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_1"], "DA": [], "O": {"%": 119, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.b_0"], "DA": [], "O": {"%": 120, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_0"], "DA": [], "O": {"%": 121, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_32.w_0"], "DA": [], "O": {"%": 122, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_2"], "DA": [], "O": {"%": 123, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_1"], "DA": [], "O": {"%": 124, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.b_0"], "DA": [], "O": {"%": 125, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_0"], "DA": [], "O": {"%": 126, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_31.w_0"], "DA": [], "O": {"%": 127, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_2"], "DA": [], "O": {"%": 128, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_1"], "DA": [], "O": {"%": 129, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.b_0"], "DA": [], "O": {"%": 130, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_0"], "DA": [], "O": {"%": 131, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_30.w_0"], "DA": [], "O": {"%": 132, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 768, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_29.b_0"], "DA": [], "O": {"%": 133, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_29.w_0"], "DA": [], "O": {"%": 134, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768, 768, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_2"], "DA": [], "O": {"%": 135, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_1"], "DA": [], "O": {"%": 136, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.b_0"], "DA": [], "O": {"%": 137, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_0"], "DA": [], "O": {"%": 138, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_28.w_0"], "DA": [], "O": {"%": 139, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768, 1664, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_2"], "DA": [], "O": {"%": 140, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_1"], "DA": [], "O": {"%": 141, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.b_0"], "DA": [], "O": {"%": 142, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_0"], "DA": [], "O": {"%": 143, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_27.w_0"], "DA": [], "O": {"%": 144, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_2"], "DA": [], "O": {"%": 145, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_1"], "DA": [], "O": {"%": 146, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.b_0"], "DA": [], "O": {"%": 147, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_0"], "DA": [], "O": {"%": 148, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_26.w_0"], "DA": [], "O": {"%": 149, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_2"], "DA": [], "O": {"%": 150, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_1"], "DA": [], "O": {"%": 151, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.b_0"], "DA": [], "O": {"%": 152, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_0"], "DA": [], "O": {"%": 153, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_25.w_0"], "DA": [], "O": {"%": 154, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_2"], "DA": [], "O": {"%": 155, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_1"], "DA": [], "O": {"%": 156, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.b_0"], "DA": [], "O": {"%": 157, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_0"], "DA": [], "O": {"%": 158, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_24.w_0"], "DA": [], "O": {"%": 159, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_2"], "DA": [], "O": {"%": 160, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_1"], "DA": [], "O": {"%": 161, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.b_0"], "DA": [], "O": {"%": 162, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_0"], "DA": [], "O": {"%": 163, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_23.w_0"], "DA": [], "O": {"%": 164, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_2"], "DA": [], "O": {"%": 165, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_1"], "DA": [], "O": {"%": 166, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.b_0"], "DA": [], "O": {"%": 167, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_0"], "DA": [], "O": {"%": 168, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_22.w_0"], "DA": [], "O": {"%": 169, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 512, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_2"], "DA": [], "O": {"%": 170, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_1"], "DA": [], "O": {"%": 171, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.b_0"], "DA": [], "O": {"%": 172, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_0"], "DA": [], "O": {"%": 173, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_21.w_0"], "DA": [], "O": {"%": 174, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_20.b_0"], "DA": [], "O": {"%": 175, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_20.w_0"], "DA": [], "O": {"%": 176, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_2"], "DA": [], "O": {"%": 177, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_1"], "DA": [], "O": {"%": 178, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.b_0"], "DA": [], "O": {"%": 179, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_0"], "DA": [], "O": {"%": 180, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_19.w_0"], "DA": [], "O": {"%": 181, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 1216, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_2"], "DA": [], "O": {"%": 182, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_1"], "DA": [], "O": {"%": 183, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.b_0"], "DA": [], "O": {"%": 184, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_0"], "DA": [], "O": {"%": 185, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_18.w_0"], "DA": [], "O": {"%": 186, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160, 160, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_2"], "DA": [], "O": {"%": 187, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_1"], "DA": [], "O": {"%": 188, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.b_0"], "DA": [], "O": {"%": 189, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_0"], "DA": [], "O": {"%": 190, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_17.w_0"], "DA": [], "O": {"%": 191, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160, 160, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_2"], "DA": [], "O": {"%": 192, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_1"], "DA": [], "O": {"%": 193, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.b_0"], "DA": [], "O": {"%": 194, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_0"], "DA": [], "O": {"%": 195, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_16.w_0"], "DA": [], "O": {"%": 196, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160, 160, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_2"], "DA": [], "O": {"%": 197, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_1"], "DA": [], "O": {"%": 198, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.b_0"], "DA": [], "O": {"%": 199, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_0"], "DA": [], "O": {"%": 200, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_15.w_0"], "DA": [], "O": {"%": 201, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160, 160, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_2"], "DA": [], "O": {"%": 202, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_1"], "DA": [], "O": {"%": 203, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.b_0"], "DA": [], "O": {"%": 204, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_0"], "DA": [], "O": {"%": 205, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_14.w_0"], "DA": [], "O": {"%": 206, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160, 160, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_2"], "DA": [], "O": {"%": 207, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_1"], "DA": [], "O": {"%": 208, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.b_0"], "DA": [], "O": {"%": 209, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_0"], "DA": [], "O": {"%": 210, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_13.w_0"], "DA": [], "O": {"%": 211, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160, 256, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_2"], "DA": [], "O": {"%": 212, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_1"], "DA": [], "O": {"%": 213, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.b_0"], "DA": [], "O": {"%": 214, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_0"], "DA": [], "O": {"%": 215, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_12.w_0"], "DA": [], "O": {"%": 216, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_11.b_0"], "DA": [], "O": {"%": 217, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_11.w_0"], "DA": [], "O": {"%": 218, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 256, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_2"], "DA": [], "O": {"%": 219, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_1"], "DA": [], "O": {"%": 220, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.b_0"], "DA": [], "O": {"%": 221, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_0"], "DA": [], "O": {"%": 222, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_10.w_0"], "DA": [], "O": {"%": 223, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 896, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_2"], "DA": [], "O": {"%": 224, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_1"], "DA": [], "O": {"%": 225, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.b_0"], "DA": [], "O": {"%": 226, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_0"], "DA": [], "O": {"%": 227, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_9.w_0"], "DA": [], "O": {"%": 228, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_2"], "DA": [], "O": {"%": 229, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_1"], "DA": [], "O": {"%": 230, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.b_0"], "DA": [], "O": {"%": 231, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_0"], "DA": [], "O": {"%": 232, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_8.w_0"], "DA": [], "O": {"%": 233, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_2"], "DA": [], "O": {"%": 234, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_1"], "DA": [], "O": {"%": 235, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.b_0"], "DA": [], "O": {"%": 236, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_0"], "DA": [], "O": {"%": 237, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_7.w_0"], "DA": [], "O": {"%": 238, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_2"], "DA": [], "O": {"%": 239, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_1"], "DA": [], "O": {"%": 240, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.b_0"], "DA": [], "O": {"%": 241, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_0"], "DA": [], "O": {"%": 242, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_6.w_0"], "DA": [], "O": {"%": 243, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_2"], "DA": [], "O": {"%": 244, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_1"], "DA": [], "O": {"%": 245, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.b_0"], "DA": [], "O": {"%": 246, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_0"], "DA": [], "O": {"%": 247, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_5.w_0"], "DA": [], "O": {"%": 248, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_2"], "DA": [], "O": {"%": 249, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_1"], "DA": [], "O": {"%": 250, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.b_0"], "DA": [], "O": {"%": 251, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_0"], "DA": [], "O": {"%": 252, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_4.w_0"], "DA": [], "O": {"%": 253, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_2"], "DA": [], "O": {"%": 254, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_1"], "DA": [], "O": {"%": 255, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.b_0"], "DA": [], "O": {"%": 256, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_0"], "DA": [], "O": {"%": 257, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_3.w_0"], "DA": [], "O": {"%": 258, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_2"], "DA": [], "O": {"%": 259, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_1"], "DA": [], "O": {"%": 260, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.b_0"], "DA": [], "O": {"%": 261, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_0"], "DA": [], "O": {"%": 262, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_2.w_0"], "DA": [], "O": {"%": 263, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 64, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_2"], "DA": [], "O": {"%": 264, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_1"], "DA": [], "O": {"%": 265, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.b_0"], "DA": [], "O": {"%": 266, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_0"], "DA": [], "O": {"%": 267, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_1.w_0"], "DA": [], "O": {"%": 268, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_2"], "DA": [], "O": {"%": 269, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_1"], "DA": [], "O": {"%": 270, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.b_0"], "DA": [], "O": {"%": 271, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_0"], "DA": [], "O": {"%": 272, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_0.w_0"], "DA": [], "O": {"%": 273, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 3, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "1.data", "A": [{"AT": {"#": "0.a_str", "D": "x"}, "N": "name"}, {"AT": {"#": "1.a_intarray", "D": [-1, 3, 48, -1]}, "N": "shape"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [0, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 274, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, 48, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/Sequential/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 274}, {"%": 273}], "O": [{"%": 275, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/Sequential/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 275}, {"%": 270}, {"%": 269}, {"%": 272}, {"%": 271}], "O": [{"%": 276, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}, {"%": 277, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 278, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 279, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 280, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 281, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/Sequential/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 276}], "O": [{"%": 282, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/Sequential/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 282}, {"%": 268}], "O": [{"%": 283, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/Sequential/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 283}, {"%": 265}, {"%": 264}, {"%": 267}, {"%": 266}], "O": [{"%": 284, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}, {"%": 285, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 286, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 287, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 288, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 289, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/Sequential/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 284}], "O": [{"%": 290, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/Sequential/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 290}, {"%": 263}], "O": [{"%": 291, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/Sequential/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 291}, {"%": 260}, {"%": 259}, {"%": 262}, {"%": 261}], "O": [{"%": 292, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 24, -1], "NCHW", [], 0]}}, {"%": 293, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 294, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 295, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 296, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 297, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/Sequential/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 292}], "O": [{"%": 298, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 128}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 298}, {"%": 258}], "O": [{"%": 299, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 299}, {"%": 255}, {"%": 254}, {"%": 257}, {"%": 256}], "O": [{"%": 300, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}, {"%": 301, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 302, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 303, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 304, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 305, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 300}, {"%": 253}], "O": [{"%": 306, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 306}, {"%": 250}, {"%": 249}, {"%": 252}, {"%": 251}], "O": [{"%": 307, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}, {"%": 308, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 309, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 310, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 311, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 312, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 307}], "O": [{"%": 313, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 313}, {"%": 248}], "O": [{"%": 314, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 314}, {"%": 245}, {"%": 244}, {"%": 247}, {"%": 246}], "O": [{"%": 315, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}, {"%": 316, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 317, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 318, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 319, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 320, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 315}], "O": [{"%": 321, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 321}, {"%": 243}], "O": [{"%": 322, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 322}, {"%": 240}, {"%": 239}, {"%": 242}, {"%": 241}], "O": [{"%": 323, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}, {"%": 324, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 325, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 326, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 327, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 328, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 323}], "O": [{"%": 329, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 329}, {"%": 238}], "O": [{"%": 330, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 330}, {"%": 235}, {"%": 234}, {"%": 237}, {"%": 236}], "O": [{"%": 331, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}, {"%": 332, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 333, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 334, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 335, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 336, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 331}], "O": [{"%": 337, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 337}, {"%": 233}], "O": [{"%": 338, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 338}, {"%": 230}, {"%": 229}, {"%": 232}, {"%": 231}], "O": [{"%": 339, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}, {"%": 340, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 341, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 342, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 343, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 344, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 339}], "O": [{"%": 345, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 345}, {"%": 228}], "O": [{"%": 346, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 346}, {"%": 225}, {"%": 224}, {"%": 227}, {"%": 226}], "O": [{"%": 347, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}, {"%": 348, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 349, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 350, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 351, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 352, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 347}], "O": [{"%": 353, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 354, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [{"%": 300}, {"%": 313}, {"%": 321}, {"%": 329}, {"%": 337}, {"%": 345}, {"%": 353}], "O": [{"%": 355, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [{"%": 355}, {"%": 354}], "O": [{"%": 356, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 896, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 356}, {"%": 223}], "O": [{"%": 357, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_6/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 357}, {"%": 220}, {"%": 219}, {"%": 222}, {"%": 221}], "O": [{"%": 358, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}}, {"%": 359, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 360, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 361, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 362, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 363, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ConvBNAct_6/ReLU/"}, "N": "struct_name"}], "I": [{"%": 358}], "O": [{"%": 364, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 365, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 364}, {"%": 365}], "O": [{"%": 366, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 366}, {"%": 218}], "O": [{"%": 367, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 368, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 217}, {"%": 368}], "O": [{"%": 369, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 256, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 367}, {"%": 369}], "O": [{"%": 370, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.sig<PERSON><PERSON>", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ESEModule/Sigmoid/"}, "N": "struct_name"}], "I": [{"%": 370}], "O": [{"%": 371, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage/Sequential/HG_Block/ESEModule/"}, "N": "struct_name"}], "I": [{"%": 364}, {"%": 371}], "O": [{"%": 372, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 256}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 372}, {"%": 216}], "O": [{"%": 373, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 373}, {"%": 213}, {"%": 212}, {"%": 215}, {"%": 214}], "O": [{"%": 374, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}}, {"%": 375, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 376, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 377, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 378, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 379, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 374}, {"%": 211}], "O": [{"%": 380, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 380}, {"%": 208}, {"%": 207}, {"%": 210}, {"%": 209}], "O": [{"%": 381, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}, {"%": 382, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 383, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 384, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 385, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 386, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 381}], "O": [{"%": 387, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 387}, {"%": 206}], "O": [{"%": 388, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 388}, {"%": 203}, {"%": 202}, {"%": 205}, {"%": 204}], "O": [{"%": 389, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}, {"%": 390, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 391, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 392, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 393, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 394, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 389}], "O": [{"%": 395, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 395}, {"%": 201}], "O": [{"%": 396, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 396}, {"%": 198}, {"%": 197}, {"%": 200}, {"%": 199}], "O": [{"%": 397, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}, {"%": 398, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 399, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 400, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 401, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 402, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 397}], "O": [{"%": 403, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 403}, {"%": 196}], "O": [{"%": 404, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 404}, {"%": 193}, {"%": 192}, {"%": 195}, {"%": 194}], "O": [{"%": 405, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}, {"%": 406, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 407, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 408, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 409, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 410, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 405}], "O": [{"%": 411, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 411}, {"%": 191}], "O": [{"%": 412, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 412}, {"%": 188}, {"%": 187}, {"%": 190}, {"%": 189}], "O": [{"%": 413, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}, {"%": 414, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 415, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 416, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 417, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 418, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 413}], "O": [{"%": 419, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 419}, {"%": 186}], "O": [{"%": 420, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 420}, {"%": 183}, {"%": 182}, {"%": 185}, {"%": 184}], "O": [{"%": 421, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}, {"%": 422, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 423, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 424, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 425, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [160], "NCHW", [], 0]}}, {"%": 426, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 421}], "O": [{"%": 427, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 428, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [{"%": 374}, {"%": 387}, {"%": 395}, {"%": 403}, {"%": 411}, {"%": 419}, {"%": 427}], "O": [{"%": 429, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 160, 12, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [{"%": 429}, {"%": 428}], "O": [{"%": 430, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1216, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 430}, {"%": 181}], "O": [{"%": 431, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_6/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 431}, {"%": 178}, {"%": 177}, {"%": 180}, {"%": 179}], "O": [{"%": 432, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 12, -1], "NCHW", [], 0]}}, {"%": 433, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 434, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 435, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 436, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 437, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ConvBNAct_6/ReLU/"}, "N": "struct_name"}], "I": [{"%": 432}], "O": [{"%": 438, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 439, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 438}, {"%": 439}], "O": [{"%": 440, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 440}, {"%": 176}], "O": [{"%": 441, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 442, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 175}, {"%": 442}], "O": [{"%": 443, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 512, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 441}, {"%": 443}], "O": [{"%": 444, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.sig<PERSON><PERSON>", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ESEModule/Sigmoid/"}, "N": "struct_name"}], "I": [{"%": 444}], "O": [{"%": 445, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_1/Sequential/HG_Block/ESEModule/"}, "N": "struct_name"}], "I": [{"%": 438}, {"%": 445}], "O": [{"%": 446, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 512}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 446}, {"%": 174}], "O": [{"%": 447, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 447}, {"%": 171}, {"%": 170}, {"%": 173}, {"%": 172}], "O": [{"%": 448, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}}, {"%": 449, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 450, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 451, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 452, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 453, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 448}, {"%": 169}], "O": [{"%": 454, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 454}, {"%": 166}, {"%": 165}, {"%": 168}, {"%": 167}], "O": [{"%": 455, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 456, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 457, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 458, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 459, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 460, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 455}], "O": [{"%": 461, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 461}, {"%": 164}], "O": [{"%": 462, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 462}, {"%": 161}, {"%": 160}, {"%": 163}, {"%": 162}], "O": [{"%": 463, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 464, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 465, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 466, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 467, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 468, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 463}], "O": [{"%": 469, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 469}, {"%": 159}], "O": [{"%": 470, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 470}, {"%": 156}, {"%": 155}, {"%": 158}, {"%": 157}], "O": [{"%": 471, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 472, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 473, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 474, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 475, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 476, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 471}], "O": [{"%": 477, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 477}, {"%": 154}], "O": [{"%": 478, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 478}, {"%": 151}, {"%": 150}, {"%": 153}, {"%": 152}], "O": [{"%": 479, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 480, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 481, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 482, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 483, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 484, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 479}], "O": [{"%": 485, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 485}, {"%": 149}], "O": [{"%": 486, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 486}, {"%": 146}, {"%": 145}, {"%": 148}, {"%": 147}], "O": [{"%": 487, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 488, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 489, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 490, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 491, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 492, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 487}], "O": [{"%": 493, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 493}, {"%": 144}], "O": [{"%": 494, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 494}, {"%": 141}, {"%": 140}, {"%": 143}, {"%": 142}], "O": [{"%": 495, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 496, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 497, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 498, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 499, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 500, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 495}], "O": [{"%": 501, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 502, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [{"%": 448}, {"%": 461}, {"%": 469}, {"%": 477}, {"%": 485}, {"%": 493}, {"%": 501}], "O": [{"%": 503, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [{"%": 503}, {"%": 502}], "O": [{"%": 504, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1664, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 504}, {"%": 139}], "O": [{"%": 505, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_6/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 505}, {"%": 136}, {"%": 135}, {"%": 138}, {"%": 137}], "O": [{"%": 506, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}}, {"%": 507, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 508, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 509, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 510, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 511, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ConvBNAct_6/ReLU/"}, "N": "struct_name"}], "I": [{"%": 506}], "O": [{"%": 512, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 513, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 512}, {"%": 513}], "O": [{"%": 514, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 514}, {"%": 134}], "O": [{"%": 515, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 516, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 133}, {"%": 516}], "O": [{"%": 517, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 515}, {"%": 517}], "O": [{"%": 518, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.sig<PERSON><PERSON>", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ESEModule/Sigmoid/"}, "N": "struct_name"}], "I": [{"%": 518}], "O": [{"%": 519, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block/ESEModule/"}, "N": "struct_name"}], "I": [{"%": 512}, {"%": 519}], "O": [{"%": 520, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 520}, {"%": 132}], "O": [{"%": 521, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 521}, {"%": 129}, {"%": 128}, {"%": 131}, {"%": 130}], "O": [{"%": 522, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 523, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 524, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 525, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 526, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 527, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 522}], "O": [{"%": 528, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 528}, {"%": 127}], "O": [{"%": 529, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 529}, {"%": 124}, {"%": 123}, {"%": 126}, {"%": 125}], "O": [{"%": 530, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 531, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 532, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 533, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 534, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 535, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 530}], "O": [{"%": 536, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 536}, {"%": 122}], "O": [{"%": 537, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 537}, {"%": 119}, {"%": 118}, {"%": 121}, {"%": 120}], "O": [{"%": 538, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 539, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 540, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 541, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 542, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 543, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 538}], "O": [{"%": 544, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 544}, {"%": 117}], "O": [{"%": 545, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 545}, {"%": 114}, {"%": 113}, {"%": 116}, {"%": 115}], "O": [{"%": 546, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 547, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 548, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 549, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 550, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 551, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 546}], "O": [{"%": 552, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 552}, {"%": 112}], "O": [{"%": 553, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 553}, {"%": 109}, {"%": 108}, {"%": 111}, {"%": 110}], "O": [{"%": 554, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 555, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 556, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 557, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 558, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 559, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 554}], "O": [{"%": 560, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 560}, {"%": 107}], "O": [{"%": 561, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 561}, {"%": 104}, {"%": 103}, {"%": 106}, {"%": 105}], "O": [{"%": 562, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}, {"%": 563, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 564, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 565, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 566, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 567, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 562}], "O": [{"%": 568, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 569, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/"}, "N": "struct_name"}], "I": [{"%": 520}, {"%": 528}, {"%": 536}, {"%": 544}, {"%": 552}, {"%": 560}, {"%": 568}], "O": [{"%": 570, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, 6, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/"}, "N": "struct_name"}], "I": [{"%": 570}, {"%": 569}], "O": [{"%": 571, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1920, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 571}, {"%": 102}], "O": [{"%": 572, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_6/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 572}, {"%": 99}, {"%": 98}, {"%": 101}, {"%": 100}], "O": [{"%": 573, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}}, {"%": 574, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 575, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 576, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 577, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 578, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ConvBNAct_6/ReLU/"}, "N": "struct_name"}], "I": [{"%": 573}], "O": [{"%": 579, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 580, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 579}, {"%": 580}], "O": [{"%": 581, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 581}, {"%": 97}], "O": [{"%": 582, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 583, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 96}, {"%": 583}], "O": [{"%": 584, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 582}, {"%": 584}], "O": [{"%": 585, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.sig<PERSON><PERSON>", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ESEModule/Sigmoid/"}, "N": "struct_name"}], "I": [{"%": 585}], "O": [{"%": 586, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/ESEModule/"}, "N": "struct_name"}], "I": [{"%": 579}, {"%": 586}], "O": [{"%": 587, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_2/Sequential/HG_Block_1/"}, "N": "struct_name"}], "I": [{"%": 587}, {"%": 520}], "O": [{"%": 588, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 768}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 588}, {"%": 95}], "O": [{"%": 589, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 589}, {"%": 92}, {"%": 91}, {"%": 94}, {"%": 93}], "O": [{"%": 590, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 3, -1], "NCHW", [], 0]}}, {"%": 591, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 592, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 593, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 594, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [768], "NCHW", [], 0]}}, {"%": 595, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 590}, {"%": 90}], "O": [{"%": 596, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 596}, {"%": 87}, {"%": 86}, {"%": 89}, {"%": 88}], "O": [{"%": 597, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}, {"%": 598, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 599, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 600, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 601, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 602, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 597}], "O": [{"%": 603, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 85}], "O": [{"%": 604, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 604}, {"%": 82}, {"%": 81}, {"%": 84}, {"%": 83}], "O": [{"%": 605, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}, {"%": 606, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 607, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 608, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 609, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 610, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 605}], "O": [{"%": 611, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 611}, {"%": 80}], "O": [{"%": 612, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 612}, {"%": 77}, {"%": 76}, {"%": 79}, {"%": 78}], "O": [{"%": 613, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}, {"%": 614, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 615, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 616, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 617, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 618, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 613}], "O": [{"%": 619, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 619}, {"%": 75}], "O": [{"%": 620, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 620}, {"%": 72}, {"%": 71}, {"%": 74}, {"%": 73}], "O": [{"%": 621, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}, {"%": 622, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 623, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 624, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 625, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 626, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 621}], "O": [{"%": 627, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 627}, {"%": 70}], "O": [{"%": 628, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 628}, {"%": 67}, {"%": 66}, {"%": 69}, {"%": 68}], "O": [{"%": 629, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}, {"%": 630, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 631, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 632, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 633, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 634, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 629}], "O": [{"%": 635, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 635}, {"%": 65}], "O": [{"%": 636, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 636}, {"%": 62}, {"%": 61}, {"%": 64}, {"%": 63}], "O": [{"%": 637, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}, {"%": 638, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 639, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 640, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 641, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [224], "NCHW", [], 0]}}, {"%": 642, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 637}], "O": [{"%": 643, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 644, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [{"%": 590}, {"%": 603}, {"%": 611}, {"%": 619}, {"%": 627}, {"%": 635}, {"%": 643}], "O": [{"%": 645, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 224, 3, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/"}, "N": "struct_name"}], "I": [{"%": 645}, {"%": 644}], "O": [{"%": 646, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2112, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 646}, {"%": 60}], "O": [{"%": 647, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_6/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 647}, {"%": 57}, {"%": 56}, {"%": 59}, {"%": 58}], "O": [{"%": 648, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}}, {"%": 649, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 650, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 651, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 652, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 653, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ConvBNAct_6/ReLU/"}, "N": "struct_name"}], "I": [{"%": 648}], "O": [{"%": 654, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 655, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ESEModule/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 654}, {"%": 655}], "O": [{"%": 656, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 656}, {"%": 55}], "O": [{"%": 657, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 658, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 54}, {"%": 658}], "O": [{"%": 659, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 1024, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ESEModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 657}, {"%": 659}], "O": [{"%": 660, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.sig<PERSON><PERSON>", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ESEModule/Sigmoid/"}, "N": "struct_name"}], "I": [{"%": 660}], "O": [{"%": 661, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNet/HG_Stage_3/Sequential/HG_Block/ESEModule/"}, "N": "struct_name"}], "I": [{"%": 654}, {"%": 661}], "O": [{"%": 662, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/"}, "N": "struct_name"}], "I": [], "O": [{"%": 663, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPHGNet/"}, "N": "struct_name"}], "I": [{"%": 662}, {"%": 663}], "O": [{"%": 664, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.assign", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 664}], "O": [{"%": 665, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 665}, {"%": 53}], "O": [{"%": 666, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 666}, {"%": 50}, {"%": 49}, {"%": 52}, {"%": 51}], "O": [{"%": 667, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, -1], "NCHW", [], 0]}}, {"%": 668, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 669, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 670, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 671, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 672, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer/Swish/"}, "N": "struct_name"}], "I": [{"%": 667}], "O": [{"%": 673, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 673}, {"%": 48}], "O": [{"%": 674, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 674}, {"%": 45}, {"%": 44}, {"%": 47}, {"%": 46}], "O": [{"%": 675, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}, {"%": 676, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 677, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 678, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 679, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 680, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_1/Swish/"}, "N": "struct_name"}], "I": [{"%": 675}], "O": [{"%": 681, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.shape64", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 681}], "O": [{"%": 682, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 683, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 4}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 684, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 682}, {"%": 683}, {"%": 684}], "O": [{"%": 685, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.flatten", "A": [{"AT": {"#": "0.a_i32", "D": 2}, "N": "start_axis"}, {"AT": {"#": "0.a_i32", "D": 3}, "N": "stop_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 681}], "O": [{"%": 686, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 686}], "O": [{"%": 687, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/LayerNorm/"}, "N": "struct_name"}], "I": [{"%": 687}, {"%": 43}, {"%": 42}], "O": [{"%": 688, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 689, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 690, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 688}, {"%": 41}], "O": [{"%": 691, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 691}, {"%": 40}], "O": [{"%": 692, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 8}, {"#": "0.a_i64", "D": 15}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 693, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [5], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 692}, {"%": 693}], "O": [{"%": 694, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 3, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 4}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 694}], "O": [{"%": 695, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [3, -1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 696, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 697, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 695}, {"%": 696}, {"%": 697}], "O": [{"%": 698, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.25819888710975647}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 699, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.scale", "A": [{"AT": {"#": "0.a_f32", "D": 0.0}, "N": "bias"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "bias_after_scale"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 698}, {"%": 699}], "O": [{"%": 700, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 701, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 702, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 695}, {"%": 701}, {"%": 702}], "O": [{"%": 703, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 704, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 705, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 695}, {"%": 704}, {"%": 705}], "O": [{"%": 706, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 2}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 703}], "O": [{"%": 707, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 15, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 700}, {"%": 707}], "O": [{"%": 708, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": -1}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 708}], "O": [{"%": 709, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 710, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout/"}, "N": "struct_name"}], "I": [{"%": 709}, {"%": 0}, {"%": 710}], "O": [{"%": 711, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}, {"%": 712, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 711}, {"%": 706}], "O": [{"%": 713, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 713}], "O": [{"%": 714, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 120}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 715, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [3], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 714}, {"%": 715}], "O": [{"%": 716, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 716}, {"%": 39}], "O": [{"%": 717, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 717}, {"%": 38}], "O": [{"%": 718, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 719, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 718}, {"%": 0}, {"%": 719}], "O": [{"%": 720, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 721, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/"}, "N": "struct_name"}], "I": [{"%": 687}, {"%": 720}], "O": [{"%": 722, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/LayerNorm_1/"}, "N": "struct_name"}], "I": [{"%": 722}, {"%": 37}, {"%": 36}], "O": [{"%": 723, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 724, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 725, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 723}, {"%": 35}], "O": [{"%": 726, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 726}, {"%": 34}], "O": [{"%": 727, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Swish/"}, "N": "struct_name"}], "I": [{"%": 727}], "O": [{"%": 728, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 729, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout/"}, "N": "struct_name"}], "I": [{"%": 728}, {"%": 0}, {"%": 729}], "O": [{"%": 730, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}, {"%": 731, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 730}, {"%": 33}], "O": [{"%": 732, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 732}, {"%": 32}], "O": [{"%": 733, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 734, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 733}, {"%": 0}, {"%": 734}], "O": [{"%": 735, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 736, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/"}, "N": "struct_name"}], "I": [{"%": 722}, {"%": 735}], "O": [{"%": 737, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/LayerNorm/"}, "N": "struct_name"}], "I": [{"%": 737}, {"%": 31}, {"%": 30}], "O": [{"%": 738, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 739, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 740, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 738}, {"%": 29}], "O": [{"%": 741, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 741}, {"%": 28}], "O": [{"%": 742, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 8}, {"#": "0.a_i64", "D": 15}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 743, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [5], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 742}, {"%": 743}], "O": [{"%": 744, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 3, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 4}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 744}], "O": [{"%": 745, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [3, -1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 746, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 747, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 745}, {"%": 746}, {"%": 747}], "O": [{"%": 748, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.25819888710975647}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 749, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.scale", "A": [{"AT": {"#": "0.a_f32", "D": 0.0}, "N": "bias"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "bias_after_scale"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 748}, {"%": 749}], "O": [{"%": 750, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 751, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 752, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 745}, {"%": 751}, {"%": 752}], "O": [{"%": 753, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 754, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 755, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 745}, {"%": 754}, {"%": 755}], "O": [{"%": 756, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 2}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 753}], "O": [{"%": 757, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 15, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 750}, {"%": 757}], "O": [{"%": 758, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": -1}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 758}], "O": [{"%": 759, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 760, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout/"}, "N": "struct_name"}], "I": [{"%": 759}, {"%": 0}, {"%": 760}], "O": [{"%": 761, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}, {"%": 762, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 761}, {"%": 756}], "O": [{"%": 763, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 763}], "O": [{"%": 764, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 120}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 765, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [3], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 764}, {"%": 765}], "O": [{"%": 766, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 766}, {"%": 27}], "O": [{"%": 767, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 767}, {"%": 26}], "O": [{"%": 768, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 769, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 768}, {"%": 0}, {"%": 769}], "O": [{"%": 770, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 771, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/"}, "N": "struct_name"}], "I": [{"%": 737}, {"%": 770}], "O": [{"%": 772, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/LayerNorm_1/"}, "N": "struct_name"}], "I": [{"%": 772}, {"%": 25}, {"%": 24}], "O": [{"%": 773, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 774, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 775, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 773}, {"%": 23}], "O": [{"%": 776, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 776}, {"%": 22}], "O": [{"%": 777, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Swish/"}, "N": "struct_name"}], "I": [{"%": 777}], "O": [{"%": 778, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 779, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout/"}, "N": "struct_name"}], "I": [{"%": 778}, {"%": 0}, {"%": 779}], "O": [{"%": 780, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}, {"%": 781, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 780}, {"%": 21}], "O": [{"%": 782, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 782}, {"%": 20}], "O": [{"%": 783, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 784, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 783}, {"%": 0}, {"%": 784}], "O": [{"%": 785, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 786, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/"}, "N": "struct_name"}], "I": [{"%": 772}, {"%": 785}], "O": [{"%": 787, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999974752427e-07}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/LayerNorm/"}, "N": "struct_name"}], "I": [{"%": 787}, {"%": 19}, {"%": 18}], "O": [{"%": 788, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 789, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 790, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": []}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 791, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": []}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 792, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": []}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 120.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 793, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 791}, {"%": 792}, {"%": 685}, {"%": 793}], "O": [{"%": 794, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.stack", "A": [{"AT": {"#": "0.a_i32", "D": 0}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 794}], "O": [{"%": 795, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 788}, {"%": 795}], "O": [{"%": 796, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 2}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 796}], "O": [{"%": 797, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 797}, {"%": 17}], "O": [{"%": 798, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 798}, {"%": 14}, {"%": 13}, {"%": 16}, {"%": 15}], "O": [{"%": 799, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, -1], "NCHW", [], 0]}}, {"%": 800, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 801, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 802, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 803, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 804, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_2/Swish/"}, "N": "struct_name"}], "I": [{"%": 799}], "O": [{"%": 805, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 806, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 665}, {"%": 805}], "O": [{"%": 807, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, 1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 807}, {"%": 806}], "O": [{"%": 808, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 808}, {"%": 12}], "O": [{"%": 809, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 809}, {"%": 9}, {"%": 8}, {"%": 11}, {"%": 10}], "O": [{"%": 810, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, -1], "NCHW", [], 0]}}, {"%": 811, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 812, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 813, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 814, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 815, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_3/Swish/"}, "N": "struct_name"}], "I": [{"%": 810}], "O": [{"%": 816, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 816}, {"%": 7}], "O": [{"%": 817, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 817}, {"%": 4}, {"%": 3}, {"%": 6}, {"%": 5}], "O": [{"%": 818, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}, {"%": 819, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 820, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 821, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 822, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 823, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_4/Swish/"}, "N": "struct_name"}], "I": [{"%": 818}], "O": [{"%": 824, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/Im2Seq/"}, "N": "struct_name"}], "I": [], "O": [{"%": 825, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.squeeze", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/Im2Seq/"}, "N": "struct_name"}], "I": [{"%": 824}, {"%": 825}], "O": [{"%": 826, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/Im2Seq/"}, "N": "struct_name"}], "I": [{"%": 826}], "O": [{"%": 827, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/CTCHead/Linear/"}, "N": "struct_name"}], "I": [{"%": 827}, {"%": 2}], "O": [{"%": 828, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 15631], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/CTCHead/Linear/"}, "N": "struct_name"}], "I": [{"%": 828}, {"%": 1}], "O": [{"%": 829, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 15631], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": 2}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/CTCHead/"}, "N": "struct_name"}], "I": [{"%": 829}], "O": [{"%": 830, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 15631], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.fetch", "A": [{"AT": {"#": "0.a_str", "D": "fetch_name_0"}, "N": "name"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "col"}], "I": [{"%": 830}], "O": [{"%": 831, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 15631], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "persistable"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}]}]}]}}
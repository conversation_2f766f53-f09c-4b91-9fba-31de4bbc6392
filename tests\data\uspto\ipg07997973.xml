<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE us-patent-grant SYSTEM "us-patent-grant-v42-2006-08-23.dtd" [ ]>
<us-patent-grant lang="EN" dtd-version="v4.2 2006-08-23" file="US07997973-20110816.XML" status="PRODUCTION" id="us-patent-grant" country="US" date-produced="20110803" date-publ="20110816">
<us-bibliographic-data-grant>
<publication-reference>
<document-id>
<country>US</country>
<doc-number>07997973</doc-number>
<kind>B2</kind>
<date>20110816</date>
</document-id>
</publication-reference>
<application-reference appl-type="utility">
<document-id>
<country>US</country>
<doc-number>12512730</doc-number>
<date>20090730</date>
</document-id>
</application-reference>
<us-application-series-code>12</us-application-series-code>
<us-term-of-grant>
<disclaimer>
<text>This patent is subject to a terminal disclaimer.</text>
</disclaimer>
</us-term-of-grant>
<classifications-ipcr>
<classification-ipcr>
<ipc-version-indicator><date>20060101</date></ipc-version-indicator>
<classification-level>A</classification-level>
<section>G</section>
<class>06</class>
<subclass>F</subclass>
<main-group>17</main-group>
<subgroup>00</subgroup>
<symbol-position>F</symbol-position>
<classification-value>I</classification-value>
<action-date><date>20110816</date></action-date>
<generating-office><country>US</country></generating-office>
<classification-status>B</classification-status>
<classification-data-source>H</classification-data-source>
</classification-ipcr>
</classifications-ipcr>
<classification-national>
<country>US</country>
<main-classification>463 16</main-classification>
</classification-national>
<invention-title id="d2e51">Amusement device for secondary games</invention-title>
<references-cited>
<citation>
<patcit num="00001">
<document-id>
<country>US</country>
<doc-number>4446424</doc-number>
<kind>A</kind>
<name>Chatanier et al.</name>
<date>19840500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00002">
<document-id>
<country>US</country>
<doc-number>4531187</doc-number>
<kind>A</kind>
<name>Uhland</name>
<date>19850700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00003">
<document-id>
<country>US</country>
<doc-number>4540174</doc-number>
<kind>A</kind>
<name>Coppock</name>
<date>19850900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00004">
<document-id>
<country>US</country>
<doc-number>4861041</doc-number>
<kind>A</kind>
<name>Jones et al.</name>
<date>19890800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00005">
<document-id>
<country>US</country>
<doc-number>5098107</doc-number>
<kind>A</kind>
<name>Boylan et al.</name>
<date>19920300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00006">
<document-id>
<country>US</country>
<doc-number>5314194</doc-number>
<kind>A</kind>
<name>Wolf</name>
<date>19940500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00007">
<document-id>
<country>US</country>
<doc-number>5350175</doc-number>
<kind>A</kind>
<name>DiLullo et al.</name>
<date>19940900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00008">
<document-id>
<country>US</country>
<doc-number>5374061</doc-number>
<kind>A</kind>
<name>Albrecht</name>
<date>19941200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00009">
<document-id>
<country>US</country>
<doc-number>5390934</doc-number>
<kind>A</kind>
<name>Grassa</name>
<date>19950200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00010">
<document-id>
<country>US</country>
<doc-number>5397128</doc-number>
<kind>A</kind>
<name>Hesse et al.</name>
<date>19950300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00011">
<document-id>
<country>US</country>
<doc-number>5494296</doc-number>
<kind>A</kind>
<name>Grassa</name>
<date>19960200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00012">
<document-id>
<country>US</country>
<doc-number>5615888</doc-number>
<kind>A</kind>
<name>Lofink et al.</name>
<date>19970400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00013">
<document-id>
<country>US</country>
<doc-number>5636843</doc-number>
<kind>A</kind>
<name>Roberts</name>
<date>19970600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00014">
<document-id>
<country>US</country>
<doc-number>5673917</doc-number>
<kind>A</kind>
<name>Vancura</name>
<date>19971000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00015">
<document-id>
<country>US</country>
<doc-number>5713793</doc-number>
<kind>A</kind>
<name>Holte</name>
<date>19980200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00016">
<document-id>
<country>US</country>
<doc-number>5722893</doc-number>
<kind>A</kind>
<name>Hill et al.</name>
<date>19980300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00017">
<document-id>
<country>US</country>
<doc-number>5728002</doc-number>
<kind>A</kind>
<name>Hobert</name>
<date>19980300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00018">
<document-id>
<country>US</country>
<doc-number>5738583</doc-number>
<kind>A</kind>
<name>Comas et al.</name>
<date>19980400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00019">
<document-id>
<country>US</country>
<doc-number>5762552</doc-number>
<kind>A</kind>
<name>Vuong et al.</name>
<date>19980600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00020">
<document-id>
<country>US</country>
<doc-number>5769714</doc-number>
<kind>A</kind>
<name>Wiener et al.</name>
<date>19980600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00021">
<document-id>
<country>US</country>
<doc-number>5785321</doc-number>
<kind>A</kind>
<name>Van Patten et al.</name>
<date>19980700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00022">
<document-id>
<country>US</country>
<doc-number>5788574</doc-number>
<kind>A</kind>
<name>Ornstein et al.</name>
<date>19980800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00023">
<document-id>
<country>US</country>
<doc-number>5800268</doc-number>
<kind>A</kind>
<name>Molnick</name>
<date>19980900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00024">
<document-id>
<country>US</country>
<doc-number>5806846</doc-number>
<kind>A</kind>
<name>Lofink et al.</name>
<date>19980900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00025">
<document-id>
<country>US</country>
<doc-number>5810360</doc-number>
<kind>A</kind>
<name>Srichayaporn</name>
<date>19980900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00026">
<document-id>
<country>US</country>
<doc-number>5826976</doc-number>
<kind>A</kind>
<name>Skratulia</name>
<date>19981000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00027">
<document-id>
<country>US</country>
<doc-number>5863041</doc-number>
<kind>A</kind>
<name>Boylan et al.</name>
<date>19990100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00028">
<document-id>
<country>US</country>
<doc-number>5868392</doc-number>
<kind>A</kind>
<name>Kraft</name>
<date>19990200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00029">
<document-id>
<country>US</country>
<doc-number>5999808</doc-number>
<kind>A</kind>
<name>LaDue</name>
<date>19991200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00030">
<document-id>
<country>US</country>
<doc-number>6045129</doc-number>
<kind>A</kind>
<name>Cooper et al.</name>
<date>20000400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00031">
<document-id>
<country>US</country>
<doc-number>6062565</doc-number>
<kind>A</kind>
<name>Chadband et al.</name>
<date>20000500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00032">
<document-id>
<country>US</country>
<doc-number>6068552</doc-number>
<kind>A</kind>
<name>Walker et al.</name>
<date>20000500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00033">
<document-id>
<country>US</country>
<doc-number>6070878</doc-number>
<kind>A</kind>
<name>Jones et al.</name>
<date>20000600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00034">
<document-id>
<country>US</country>
<doc-number>6120031</doc-number>
<kind>A</kind>
<name>Adams</name>
<date>20000900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00035">
<document-id>
<country>US</country>
<doc-number>6126166</doc-number>
<kind>A</kind>
<name>Lorson et al.</name>
<date>20001000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00036">
<document-id>
<country>US</country>
<doc-number>6135453</doc-number>
<kind>A</kind>
<name>Srichayaporn</name>
<date>20001000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00037">
<document-id>
<country>US</country>
<doc-number>6146272</doc-number>
<kind>A</kind>
<name>Walker et al.</name>
<date>20001100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00038">
<document-id>
<country>US</country>
<doc-number>6158741</doc-number>
<kind>A</kind>
<name>Koelling</name>
<date>20001200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00039">
<document-id>
<country>US</country>
<doc-number>6165069</doc-number>
<kind>A</kind>
<name>Sines et al.</name>
<date>20001200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00040">
<document-id>
<country>US</country>
<doc-number>6177905</doc-number>
<kind>B1</kind>
<name>Welch</name>
<date>20010100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00041">
<document-id>
<country>US</country>
<doc-number>6206373</doc-number>
<kind>B1</kind>
<name>Garrod</name>
<date>20010300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00042">
<document-id>
<country>US</country>
<doc-number>6217447</doc-number>
<kind>B1</kind>
<name>Lofink et al.</name>
<date>20010400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00043">
<document-id>
<country>US</country>
<doc-number>6227969</doc-number>
<kind>B1</kind>
<name>Yoseloff</name>
<date>20010500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00044">
<document-id>
<country>US</country>
<doc-number>6270404</doc-number>
<kind>B2</kind>
<name>Sines et al.</name>
<date>20010800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00045">
<document-id>
<country>US</country>
<doc-number>6285987</doc-number>
<kind>B1</kind>
<name>Roth et al.</name>
<date>20010900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00046">
<document-id>
<country>US</country>
<doc-number>6325716</doc-number>
<kind>B1</kind>
<name>Walker et al.</name>
<date>20011200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00047">
<document-id>
<country>US</country>
<doc-number>6341778</doc-number>
<kind>B1</kind>
<name>Lee</name>
<date>20020100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00048">
<document-id>
<country>US</country>
<doc-number>6503145</doc-number>
<kind>B1</kind>
<name>Webb</name>
<date>20030100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00049">
<document-id>
<country>US</country>
<doc-number>6508709</doc-number>
<kind>B1</kind>
<name>Kannarkar</name>
<date>20030100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00050">
<document-id>
<country>US</country>
<doc-number>6508710</doc-number>
<kind>B1</kind>
<name>Paravia et al.</name>
<date>20030100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00051">
<document-id>
<country>US</country>
<doc-number>6517073</doc-number>
<kind>B1</kind>
<name>Vancura</name>
<date>20030200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00052">
<document-id>
<country>US</country>
<doc-number>6520856</doc-number>
<kind>B1</kind>
<name>Walker et al.</name>
<date>20030200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00053">
<document-id>
<country>US</country>
<doc-number>6523829</doc-number>
<kind>B1</kind>
<name>Walker et al.</name>
<date>20030200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00054">
<document-id>
<country>US</country>
<doc-number>6530835</doc-number>
<kind>B1</kind>
<name>Walker et al.</name>
<date>20030300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00055">
<document-id>
<country>US</country>
<doc-number>6533662</doc-number>
<kind>B2</kind>
<name>Soltys et al.</name>
<date>20030300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00056">
<document-id>
<country>US</country>
<doc-number>6536767</doc-number>
<kind>B1</kind>
<name>Keller</name>
<date>20030300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00057">
<document-id>
<country>US</country>
<doc-number>6540230</doc-number>
<kind>B1</kind>
<name>Walker et al.</name>
<date>20030400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00058">
<document-id>
<country>US</country>
<doc-number>6540609</doc-number>
<kind>B1</kind>
<name>Paige</name>
<date>20030400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00059">
<document-id>
<country>US</country>
<doc-number>6569015</doc-number>
<kind>B1</kind>
<name>Baerlocher et al.</name>
<date>20030500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00060">
<document-id>
<country>US</country>
<doc-number>6575465</doc-number>
<kind>B2</kind>
<name>Lo</name>
<date>20030600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00061">
<document-id>
<country>US</country>
<doc-number>6575834</doc-number>
<kind>B1</kind>
<name>Lindo</name>
<date>20030600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00062">
<document-id>
<country>US</country>
<doc-number>6575843</doc-number>
<kind>B2</kind>
<name>McCabe</name>
<date>20030600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00063">
<document-id>
<country>US</country>
<doc-number>6616142</doc-number>
<kind>B2</kind>
<name>Adams</name>
<date>20030900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00064">
<document-id>
<country>US</country>
<doc-number>6625578</doc-number>
<kind>B2</kind>
<name>Spaur et al.</name>
<date>20030900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00065">
<document-id>
<country>US</country>
<doc-number>6628939</doc-number>
<kind>B2</kind>
<name>Paulsen</name>
<date>20030900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00066">
<document-id>
<country>US</country>
<doc-number>6679497</doc-number>
<kind>B2</kind>
<name>Walker et al.</name>
<date>20040100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00067">
<document-id>
<country>US</country>
<doc-number>6692003</doc-number>
<kind>B2</kind>
<name>Potter et al.</name>
<date>20040200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00068">
<document-id>
<country>US</country>
<doc-number>6692360</doc-number>
<kind>B2</kind>
<name>Kusuda et al.</name>
<date>20040200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00069">
<document-id>
<country>US</country>
<doc-number>6695700</doc-number>
<kind>B2</kind>
<name>Walker et al.</name>
<date>20040200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00070">
<document-id>
<country>US</country>
<doc-number>6712702</doc-number>
<kind>B2</kind>
<name>Goldberg et al.</name>
<date>20040300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00071">
<document-id>
<country>US</country>
<doc-number>6733387</doc-number>
<kind>B2</kind>
<name>Walker et al.</name>
<date>20040500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00072">
<document-id>
<country>US</country>
<doc-number>6769986</doc-number>
<kind>B2</kind>
<name>Vancura</name>
<date>20040800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00073">
<document-id>
<country>US</country>
<doc-number>6789800</doc-number>
<kind>B2</kind>
<name>Webb</name>
<date>20040900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00074">
<document-id>
<country>US</country>
<doc-number>6790141</doc-number>
<kind>B2</kind>
<name>Muir et al.</name>
<date>20040900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00075">
<document-id>
<country>US</country>
<doc-number>6790142</doc-number>
<kind>B2</kind>
<name>Okada et al.</name>
<date>20040900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00076">
<document-id>
<country>US</country>
<doc-number>6808173</doc-number>
<kind>B2</kind>
<name>Snow</name>
<date>20041000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00077">
<document-id>
<country>US</country>
<doc-number>6811488</doc-number>
<kind>B2</kind>
<name>Paravia et al.</name>
<date>20041100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00078">
<document-id>
<country>US</country>
<doc-number>6845981</doc-number>
<kind>B1</kind>
<name>Ko</name>
<date>20050100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00079">
<document-id>
<country>US</country>
<doc-number>6846238</doc-number>
<kind>B2</kind>
<name>Wells</name>
<date>20050100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00080">
<document-id>
<country>US</country>
<doc-number>6857957</doc-number>
<kind>B2</kind>
<name>Marks et al.</name>
<date>20050200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00081">
<document-id>
<country>US</country>
<doc-number>6863274</doc-number>
<kind>B2</kind>
<name>Webb</name>
<date>20050300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00082">
<document-id>
<country>US</country>
<doc-number>6877745</doc-number>
<kind>B1</kind>
<name>Walker et al.</name>
<date>20050400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00083">
<document-id>
<country>US</country>
<doc-number>6896618</doc-number>
<kind>B2</kind>
<name>Benoy et al.</name>
<date>20050500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00084">
<document-id>
<country>US</country>
<doc-number>6902167</doc-number>
<kind>B2</kind>
<name>Webb</name>
<date>20050600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00085">
<document-id>
<country>US</country>
<doc-number>6912398</doc-number>
<kind>B1</kind>
<name>Domnitz</name>
<date>20050600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00086">
<document-id>
<country>US</country>
<doc-number>6921331</doc-number>
<kind>B2</kind>
<name>Gatto et al.</name>
<date>20050700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00087">
<document-id>
<country>US</country>
<doc-number>6923446</doc-number>
<kind>B2</kind>
<name>Snow</name>
<date>20050800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00088">
<document-id>
<country>US</country>
<doc-number>6929264</doc-number>
<kind>B2</kind>
<name>Huard et al.</name>
<date>20050800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00089">
<document-id>
<country>US</country>
<doc-number>7000921</doc-number>
<kind>B2</kind>
<name>Schultz</name>
<date>20060200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00090">
<document-id>
<country>US</country>
<doc-number>7029009</doc-number>
<kind>B2</kind>
<name>Grauzer et al.</name>
<date>20060400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00091">
<document-id>
<country>US</country>
<doc-number>7055822</doc-number>
<kind>B2</kind>
<name>Lo</name>
<date>20060600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00092">
<document-id>
<country>US</country>
<doc-number>7066465</doc-number>
<kind>B2</kind>
<name>Daines</name>
<date>20060600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00093">
<document-id>
<country>US</country>
<doc-number>7229354</doc-number>
<kind>B2</kind>
<name>McNutt et al.</name>
<date>20070600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00094">
<document-id>
<country>US</country>
<doc-number>7255351</doc-number>
<kind>B2</kind>
<name>Yoseloff et al.</name>
<date>20070800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00095">
<document-id>
<country>US</country>
<doc-number>7264546</doc-number>
<kind>B2</kind>
<name>Marshall et al.</name>
<date>20070900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00096">
<document-id>
<country>US</country>
<doc-number>7300348</doc-number>
<kind>B2</kind>
<name>Kaminkow et al.</name>
<date>20071100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00097">
<document-id>
<country>US</country>
<doc-number>7311605</doc-number>
<kind>B2</kind>
<name>Moser</name>
<date>20071200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00098">
<document-id>
<country>US</country>
<doc-number>7316916</doc-number>
<kind>B2</kind>
<name>Takenaka</name>
<date>20080100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00099">
<document-id>
<country>US</country>
<doc-number>7344136</doc-number>
<kind>B2</kind>
<name>Schultz</name>
<date>20080300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00100">
<document-id>
<country>US</country>
<doc-number>7379886</doc-number>
<kind>B1</kind>
<name>Zaring et al.</name>
<date>20080500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00101">
<document-id>
<country>US</country>
<doc-number>7394405</doc-number>
<kind>B2</kind>
<name>Godden</name>
<date>20080700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00102">
<document-id>
<country>US</country>
<doc-number>7585217</doc-number>
<kind>B2</kind>
<name>Lutnick et al.</name>
<date>20090900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00103">
<document-id>
<country>US</country>
<doc-number>7833101</doc-number>
<kind>B2</kind>
<name>Lutnick et al.</name>
<date>20101100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00104">
<document-id>
<country>US</country>
<doc-number>2001/0007828</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20010700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00105">
<document-id>
<country>US</country>
<doc-number>2001/0014619</doc-number>
<kind>A1</kind>
<name>Kusuda</name>
<date>20010800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00106">
<document-id>
<country>US</country>
<doc-number>2001/0019965</doc-number>
<kind>A1</kind>
<name>Ochi</name>
<date>20010900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00107">
<document-id>
<country>US</country>
<doc-number>2001/0024970</doc-number>
<kind>A1</kind>
<name>McKee et al.</name>
<date>20010900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00108">
<document-id>
<country>US</country>
<doc-number>2001/0041609</doc-number>
<kind>A1</kind>
<name>Oranges et al.</name>
<date>20011100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00109">
<document-id>
<country>US</country>
<doc-number>2002/0010023</doc-number>
<kind>A1</kind>
<name>Kusuda et al.</name>
<date>20020100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00110">
<document-id>
<country>US</country>
<doc-number>2002/0013174</doc-number>
<kind>A1</kind>
<name>Murata</name>
<date>20020100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00111">
<document-id>
<country>US</country>
<doc-number>2002/0019253</doc-number>
<kind>A1</kind>
<name>Reitzen et al.</name>
<date>20020200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00112">
<document-id>
<country>US</country>
<doc-number>2002/0032049</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20020300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00113">
<document-id>
<country>US</country>
<doc-number>2002/0125639</doc-number>
<kind>A1</kind>
<name>Wells</name>
<date>20020900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00114">
<document-id>
<country>US</country>
<doc-number>2002/0147042</doc-number>
<kind>A1</kind>
<name>Vuong et al.</name>
<date>20021000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00115">
<document-id>
<country>US</country>
<doc-number>2002/0169019</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20021100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00116">
<document-id>
<country>US</country>
<doc-number>2002/0196342</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20021200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00117">
<document-id>
<country>US</country>
<doc-number>2002/0198044</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20021200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00118">
<document-id>
<country>US</country>
<doc-number>2002/0198052</doc-number>
<kind>A1</kind>
<name>Soltys et al.</name>
<date>20021200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00119">
<document-id>
<country>US</country>
<doc-number>2003/0003988</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20030100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00120">
<document-id>
<country>US</country>
<doc-number>2003/0006931</doc-number>
<kind>A1</kind>
<name>Mages</name>
<date>20030100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00121">
<document-id>
<country>US</country>
<doc-number>2003/0008662</doc-number>
<kind>A1</kind>
<name>Stern et al.</name>
<date>20030100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00122">
<document-id>
<country>US</country>
<doc-number>2003/0047871</doc-number>
<kind>A1</kind>
<name>Vancura</name>
<date>20030300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00123">
<document-id>
<country>US</country>
<doc-number>2003/0050106</doc-number>
<kind>A1</kind>
<name>Lyfoung</name>
<date>20030300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00124">
<document-id>
<country>US</country>
<doc-number>2003/0060276</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20030300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00125">
<document-id>
<country>US</country>
<doc-number>2003/0069058</doc-number>
<kind>A1</kind>
<name>Byrne</name>
<date>20030400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00126">
<document-id>
<country>US</country>
<doc-number>2003/0090063</doc-number>
<kind>A1</kind>
<name>Jarvis et al.</name>
<date>20030500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00127">
<document-id>
<country>US</country>
<doc-number>2003/0114217</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20030600</date>
</document-id>
</patcit>
<category>cited by examiner</category>
<classification-national><country>US</country><main-classification>463 20</main-classification></classification-national>
</citation>
<citation>
<patcit num="00128">
<document-id>
<country>US</country>
<doc-number>2003/0119579</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20030600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00129">
<document-id>
<country>US</country>
<doc-number>2003/0148812</doc-number>
<kind>A1</kind>
<name>Paulsen et al.</name>
<date>20030800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00130">
<document-id>
<country>US</country>
<doc-number>2003/0157977</doc-number>
<kind>A1</kind>
<name>Thomas et al.</name>
<date>20030800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00131">
<document-id>
<country>US</country>
<doc-number>2003/0187736</doc-number>
<kind>A1</kind>
<name>Teague et al.</name>
<date>20031000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00132">
<document-id>
<country>US</country>
<doc-number>2003/0190941</doc-number>
<kind>A1</kind>
<name>Byrne</name>
<date>20031000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00133">
<document-id>
<country>US</country>
<doc-number>2003/0216170</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20031100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00134">
<document-id>
<country>US</country>
<doc-number>2003/0224852</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20031200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00135">
<document-id>
<country>US</country>
<doc-number>2004/0005918</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20040100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00136">
<document-id>
<country>US</country>
<doc-number>2004/0015429</doc-number>
<kind>A1</kind>
<name>Tighe et al.</name>
<date>20040100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00137">
<document-id>
<country>US</country>
<doc-number>2004/0043807</doc-number>
<kind>A1</kind>
<name>Pennington</name>
<date>20040300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00138">
<document-id>
<country>US</country>
<doc-number>2004/0044567</doc-number>
<kind>A1</kind>
<name>Willis</name>
<date>20040300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00139">
<document-id>
<country>US</country>
<doc-number>2004/0053664</doc-number>
<kind>A1</kind>
<name>Byrne</name>
<date>20040300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00140">
<document-id>
<country>US</country>
<doc-number>2004/0068439</doc-number>
<kind>A1</kind>
<name>Elgrably</name>
<date>20040400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00141">
<document-id>
<country>US</country>
<doc-number>2004/0106454</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20040600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00142">
<document-id>
<country>US</country>
<doc-number>2004/0147308</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20040700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00143">
<document-id>
<country>US</country>
<doc-number>2004/0176162</doc-number>
<kind>A1</kind>
<name>Rothschild</name>
<date>20040900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00144">
<document-id>
<country>US</country>
<doc-number>2004/0204026</doc-number>
<kind>A1</kind>
<name>Steer et al.</name>
<date>20041000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00145">
<document-id>
<country>US</country>
<doc-number>2004/0204247</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20041000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00146">
<document-id>
<country>US</country>
<doc-number>2004/0210507</doc-number>
<kind>A1</kind>
<name>Asher et al.</name>
<date>20041000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00147">
<document-id>
<country>US</country>
<doc-number>2004/0219969</doc-number>
<kind>A1</kind>
<name>Casey et al.</name>
<date>20041100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00148">
<document-id>
<country>US</country>
<doc-number>2004/0229671</doc-number>
<kind>A1</kind>
<name>Stronach et al.</name>
<date>20041100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00149">
<document-id>
<country>US</country>
<doc-number>2004/0243519</doc-number>
<kind>A1</kind>
<name>Perttila et al.</name>
<date>20041200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00150">
<document-id>
<country>US</country>
<doc-number>2004/0259621</doc-number>
<kind>A1</kind>
<name>Pfeiffer et al.</name>
<date>20041200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00151">
<document-id>
<country>US</country>
<doc-number>2004/0264916</doc-number>
<kind>A1</kind>
<name>Van De Sluis et al.</name>
<date>20041200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00152">
<document-id>
<country>US</country>
<doc-number>2005/0003878</doc-number>
<kind>A1</kind>
<name>Updike</name>
<date>20050100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00153">
<document-id>
<country>US</country>
<doc-number>2005/0003886</doc-number>
<kind>A1</kind>
<name>Englman et al.</name>
<date>20050100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00154">
<document-id>
<country>US</country>
<doc-number>2005/0003888</doc-number>
<kind>A1</kind>
<name>Asher et al.</name>
<date>20050100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00155">
<document-id>
<country>US</country>
<doc-number>2005/0003893</doc-number>
<kind>A1</kind>
<name>Hogwood et al.</name>
<date>20050100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00156">
<document-id>
<country>US</country>
<doc-number>2005/0023758</doc-number>
<kind>A1</kind>
<name>Noyes</name>
<date>20050200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00157">
<document-id>
<country>US</country>
<doc-number>2005/0064926</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20050300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00158">
<document-id>
<country>US</country>
<doc-number>2005/0073102</doc-number>
<kind>A1</kind>
<name>Yoseloff et al.</name>
<date>20050400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00159">
<document-id>
<country>US</country>
<doc-number>2005/0075164</doc-number>
<kind>A1</kind>
<name>Krynicky</name>
<date>20050400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00160">
<document-id>
<country>US</country>
<doc-number>2005/0082756</doc-number>
<kind>A1</kind>
<name>Duncan</name>
<date>20050400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00161">
<document-id>
<country>US</country>
<doc-number>2005/0113161</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20050500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00162">
<document-id>
<country>US</country>
<doc-number>2005/0151319</doc-number>
<kind>A1</kind>
<name>Berman et al.</name>
<date>20050700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00163">
<document-id>
<country>US</country>
<doc-number>2005/0159212</doc-number>
<kind>A1</kind>
<name>Romney et al.</name>
<date>20050700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00164">
<document-id>
<country>US</country>
<doc-number>2005/0170876</doc-number>
<kind>A1</kind>
<name>Masci et al.</name>
<date>20050800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00165">
<document-id>
<country>US</country>
<doc-number>2005/0173863</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20050800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00166">
<document-id>
<country>US</country>
<doc-number>2005/0194742</doc-number>
<kind>A1</kind>
<name>Donaldson</name>
<date>20050900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00167">
<document-id>
<country>US</country>
<doc-number>2005/0233803</doc-number>
<kind>A1</kind>
<name>Yang</name>
<date>20051000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00168">
<document-id>
<country>US</country>
<doc-number>2005/0253334</doc-number>
<kind>A1</kind>
<name>Friedman</name>
<date>20051100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00169">
<document-id>
<country>US</country>
<doc-number>2005/0253338</doc-number>
<kind>A1</kind>
<name>Daines</name>
<date>20051100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00170">
<document-id>
<country>US</country>
<doc-number>2005/0275166</doc-number>
<kind>A1</kind>
<name>Wirth</name>
<date>20051200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00171">
<document-id>
<country>US</country>
<doc-number>2005/0282614</doc-number>
<kind>A1</kind>
<name>Gauselmann</name>
<date>20051200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00172">
<document-id>
<country>US</country>
<doc-number>2006/0009283</doc-number>
<kind>A1</kind>
<name>Englman et al.</name>
<date>20060100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00173">
<document-id>
<country>US</country>
<doc-number>2006/0019745</doc-number>
<kind>A1</kind>
<name>Benbrahim</name>
<date>20060100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00174">
<document-id>
<country>US</country>
<doc-number>2006/0025192</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20060200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00175">
<document-id>
<country>US</country>
<doc-number>2006/0025206</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20060200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00176">
<document-id>
<country>US</country>
<doc-number>2006/0025208</doc-number>
<kind>A1</kind>
<name>Ramsey</name>
<date>20060200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00177">
<document-id>
<country>US</country>
<doc-number>2006/0035707</doc-number>
<kind>A1</kind>
<name>Nguyen et al.</name>
<date>20060200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00178">
<document-id>
<country>US</country>
<doc-number>2006/0035708</doc-number>
<kind>A1</kind>
<name>Nguyen et al.</name>
<date>20060200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00179">
<document-id>
<country>US</country>
<doc-number>2006/0036495</doc-number>
<kind>A1</kind>
<name>Aufricht et al.</name>
<date>20060200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00180">
<document-id>
<country>US</country>
<doc-number>2006/0046816</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20060300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00181">
<document-id>
<country>US</country>
<doc-number>2006/0046853</doc-number>
<kind>A1</kind>
<name>Black</name>
<date>20060300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00182">
<document-id>
<country>US</country>
<doc-number>2006/0052148</doc-number>
<kind>A1</kind>
<name>Blair et al.</name>
<date>20060300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00183">
<document-id>
<country>US</country>
<doc-number>2006/0063580</doc-number>
<kind>A1</kind>
<name>Nguyen et al.</name>
<date>20060300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00184">
<document-id>
<country>US</country>
<doc-number>2006/0063587</doc-number>
<kind>A1</kind>
<name>Manzo</name>
<date>20060300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00185">
<document-id>
<country>US</country>
<doc-number>2006/0073882</doc-number>
<kind>A1</kind>
<name>Rozkin et al.</name>
<date>20060400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00186">
<document-id>
<country>US</country>
<doc-number>2006/0111178</doc-number>
<kind>A1</kind>
<name>Gallaway et al.</name>
<date>20060500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00187">
<document-id>
<country>US</country>
<doc-number>2006/0131809</doc-number>
<kind>A1</kind>
<name>Lancaster et al.</name>
<date>20060600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00188">
<document-id>
<country>US</country>
<doc-number>2006/0148551</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20060700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00189">
<document-id>
<country>US</country>
<doc-number>2006/0160614</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20060700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00190">
<document-id>
<country>US</country>
<doc-number>2006/0189382</doc-number>
<kind>A1</kind>
<name>Muir et al.</name>
<date>20060800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00191">
<document-id>
<country>US</country>
<doc-number>2006/0247037</doc-number>
<kind>A1</kind>
<name>Park</name>
<date>20061100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00192">
<document-id>
<country>US</country>
<doc-number>2006/0247039</doc-number>
<kind>A1</kind>
<name>Lerner et al.</name>
<date>20061100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00193">
<document-id>
<country>US</country>
<doc-number>2006/0252515</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20061100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00194">
<document-id>
<country>US</country>
<doc-number>2006/0252530</doc-number>
<kind>A1</kind>
<name>Oberberger et al.</name>
<date>20061100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00195">
<document-id>
<country>US</country>
<doc-number>2007/0014252</doc-number>
<kind>A1</kind>
<name>Chung et al.</name>
<date>20070100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00196">
<document-id>
<country>US</country>
<doc-number>2007/0015571</doc-number>
<kind>A1</kind>
<name>Walker et al.</name>
<date>20070100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00197">
<document-id>
<country>US</country>
<doc-number>2007/0021181</doc-number>
<kind>A1</kind>
<name>Nelson et al.</name>
<date>20070100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00198">
<document-id>
<country>US</country>
<doc-number>2007/0054739</doc-number>
<kind>A1</kind>
<name>Amaitis et al.</name>
<date>20070300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00199">
<document-id>
<country>US</country>
<doc-number>2007/0060099</doc-number>
<kind>A1</kind>
<name>Ramer et al.</name>
<date>20070300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00200">
<document-id>
<country>US</country>
<doc-number>2007/0060305</doc-number>
<kind>A1</kind>
<name>Amaitis et al.</name>
<date>20070300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00201">
<document-id>
<country>US</country>
<doc-number>2007/0060306</doc-number>
<kind>A1</kind>
<name>Amaitis et al.</name>
<date>20070300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00202">
<document-id>
<country>US</country>
<doc-number>2007/0060355</doc-number>
<kind>A1</kind>
<name>Amaitis et al.</name>
<date>20070300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00203">
<document-id>
<country>US</country>
<doc-number>2007/0060358</doc-number>
<kind>A1</kind>
<name>Amaitis et al.</name>
<date>20070300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00204">
<document-id>
<country>US</country>
<doc-number>2007/0077981</doc-number>
<kind>A1</kind>
<name>Hungate et al.</name>
<date>20070400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00205">
<document-id>
<country>US</country>
<doc-number>2007/0087834</doc-number>
<kind>A1</kind>
<name>Moser et al.</name>
<date>20070400</date>
</document-id>
</patcit>
<category>cited by examiner</category>
<classification-national><country>US</country><main-classification>463 42</main-classification></classification-national>
</citation>
<citation>
<patcit num="00206">
<document-id>
<country>US</country>
<doc-number>2007/0093296</doc-number>
<kind>A1</kind>
<name>Asher et al.</name>
<date>20070400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00207">
<document-id>
<country>US</country>
<doc-number>2007/0105613</doc-number>
<kind>A1</kind>
<name>Adams et al.</name>
<date>20070500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00208">
<document-id>
<country>US</country>
<doc-number>2007/0190494</doc-number>
<kind>A1</kind>
<name>Rosenberg</name>
<date>20070800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00209">
<document-id>
<country>US</country>
<doc-number>2007/0191090</doc-number>
<kind>A1</kind>
<name>O'Halloran et al.</name>
<date>20070800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00210">
<document-id>
<country>US</country>
<doc-number>2007/0259709</doc-number>
<kind>A1</kind>
<name>Kelly et al.</name>
<date>20071100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00211">
<document-id>
<country>US</country>
<doc-number>2007/0270224</doc-number>
<kind>A1</kind>
<name>Abbott</name>
<date>20071100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00212">
<document-id>
<country>US</country>
<doc-number>2008/0051171</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00213">
<document-id>
<country>US</country>
<doc-number>2008/0058048</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00214">
<document-id>
<country>US</country>
<doc-number>2008/0058049</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00215">
<document-id>
<country>US</country>
<doc-number>2008/0065481</doc-number>
<kind>A1</kind>
<name>Immorlica et al.</name>
<date>20080300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00216">
<document-id>
<country>US</country>
<doc-number>2008/0070667</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00217">
<document-id>
<country>US</country>
<doc-number>2008/0076512</doc-number>
<kind>A1</kind>
<name>Aida</name>
<date>20080300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00218">
<document-id>
<country>US</country>
<doc-number>2008/0076572</doc-number>
<kind>A1</kind>
<name>Nguyen et al.</name>
<date>20080300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00219">
<document-id>
<country>US</country>
<doc-number>2008/0085769</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00220">
<document-id>
<country>US</country>
<doc-number>2008/0096628</doc-number>
<kind>A1</kind>
<name>Czyzewski et al.</name>
<date>20080400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00221">
<document-id>
<country>US</country>
<doc-number>2008/0102956</doc-number>
<kind>A1</kind>
<name>Burman et al.</name>
<date>20080500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00222">
<document-id>
<country>US</country>
<doc-number>2008/0102957</doc-number>
<kind>A1</kind>
<name>Burman et al.</name>
<date>20080500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00223">
<document-id>
<country>US</country>
<doc-number>2008/0113765</doc-number>
<kind>A1</kind>
<name>DeWaal</name>
<date>20080500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00224">
<document-id>
<country>US</country>
<doc-number>2008/0139306</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080600</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00225">
<document-id>
<country>US</country>
<doc-number>2008/0161101</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00226">
<document-id>
<country>US</country>
<doc-number>2008/0167106</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080700</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00227">
<document-id>
<country>US</country>
<doc-number>2008/0191418</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00228">
<document-id>
<country>US</country>
<doc-number>2008/0200251</doc-number>
<kind>A1</kind>
<name>Alderucci et al.</name>
<date>20080800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00229">
<document-id>
<country>US</country>
<doc-number>2008/0214286</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20080900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00230">
<document-id>
<country>US</country>
<doc-number>2008/0248849</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20081000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00231">
<document-id>
<country>US</country>
<doc-number>2008/0254881</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20081000</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00232">
<document-id>
<country>US</country>
<doc-number>2009/0061974</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20090300</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00233">
<document-id>
<country>US</country>
<doc-number>2009/0093300</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20090400</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00234">
<document-id>
<country>US</country>
<doc-number>2009/0131151</doc-number>
<kind>A1</kind>
<name>Harris et al.</name>
<date>20090500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00235">
<document-id>
<country>US</country>
<doc-number>2010/0048302</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20100200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00236">
<document-id>
<country>US</country>
<doc-number>2010/0124960</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20100500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00237">
<document-id>
<country>US</country>
<doc-number>2010/0124967</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20100500</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00238">
<document-id>
<country>US</country>
<doc-number>2010/0211431</doc-number>
<kind>A1</kind>
<name>Lutnick et al.</name>
<date>20100800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00239">
<document-id>
<country>AU</country>
<doc-number>2004202895</doc-number>
<date>20050100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00240">
<document-id>
<country>CA</country>
<doc-number>2472735</doc-number>
<date>20050100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00241">
<document-id>
<country>GB</country>
<doc-number>2403429</doc-number>
<date>20050100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00242">
<document-id>
<country>WO</country>
<doc-number>WO 97/44105</doc-number>
<date>19971100</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00243">
<document-id>
<country>WO</country>
<doc-number>WO 99/48308</doc-number>
<date>19990900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00244">
<document-id>
<country>WO</country>
<doc-number>WO 00/79467</doc-number>
<kind>A2</kind>
<date>20001200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00245">
<document-id>
<country>WO</country>
<doc-number>WO 02/060546</doc-number>
<date>20020800</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00246">
<document-id>
<country>WO</country>
<doc-number>WO 2004/076011</doc-number>
<date>20040900</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<patcit num="00247">
<document-id>
<country>WO</country>
<doc-number>WO 2006/020413</doc-number>
<date>20060200</date>
</document-id>
</patcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00248">
<othercit>&#x201c;Hotel Online Special Report&#x2014;New Side Bet May Assist Gaming Industry in Search of a Booster Shot for Table Games&#x201d;, Sep. 21, 1998, (http://hotelonline.com/News/PressReleases1998<sub>&#x2014;</sub>3rd/Sept98<sub>&#x2014;</sub>Streak.html), download date: Aug. 28, 2006.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00249">
<othercit>&#x201c;Bill Gates' Goldrush&#x2014;Rowanlea Report&#x201d;, (http://www.rowanlea.com/report/2/historyofbgates.html), download date: Aug. 28, 2006.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00250">
<othercit>&#x201c;The Don't Pass Bet&#x201d;, Craps-Info.Net, (http://www.craps-info.net/the<sub>&#x2014;</sub>dont<sub>&#x2014;</sub>pass<sub>&#x2014;</sub>bet.html), download date: Aug. 28, 2006.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00251">
<othercit>Smith, Rod, &#x201c;Ironically, Lawsuit Filed Against Some Nevada Casinos for Card Counting; Computerized Card Counting System Boosts the House's Odds of Winning at Blackjack&#x201d;, Hotel Online, Oct. 19, 2004.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00252">
<othercit>&#x201c;Card Counting&#x201d;, (http://www.homepokergames.com/cardcounting.php), download date: Sep. 7, 2006.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00253">
<othercit>U.S. Appl. No. 11/467,078, filed Aug. 24, 2006, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00254">
<othercit>U.S. Appl. No. 11/468,809, filed Aug. 31, 2006, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00255">
<othercit>U.S. Appl. No. 11/470,250, filed Sep. 5, 2006, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00256">
<othercit>U.S. Appl. No. 11/533,300, filed Sep. 19, 2006, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00257">
<othercit>U.S. Appl. No. 11/539,518, filed Oct. 6, 2006, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00258">
<othercit>U.S. Appl. No. 11/618,426, filed Dec. 29, 2006, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00259">
<othercit>U.S. Appl. No. 11/674,232, filed Feb. 13, 2007, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00260">
<othercit>&#x201c;The Vegas Guy&#x2014;Dodge City Saloon&#x201d; (http: www.joebobbriggs.com/vegasguy/vg20020910.html), download date: Sep. 7, 2006.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00261">
<othercit>&#x201c;Who's Holding the Aces Now?&#x201d;, (http://www.wired.com/news/games/0,2101,60049,00.html), Sep. 7, 2006.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00262">
<othercit>&#x201c;Top Rated Online Casinos&#x2014;Find the Best Casinos on the Net&#x2014;Gambling-Win.com,&#x201d; (http://www.gambling-win.com/most-popular-casinos.html), download date: Dec. 4, 2007.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00263">
<othercit>&#x201c;My Multimonitor Setup: Three Screens For One Computer,&#x201d; (http://daggle.com/060223-231233.html), download date: Dec. 4, 2007.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00264">
<othercit>&#x201c;E Ink Corporation&#x2014;Technology&#x2014;Electronic Paper Displays,&#x201d; (http://www.eink.com/technology/), download date: Dec. 4, 2007.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00265">
<othercit>U.S. Appl. No. 11/680,764, filed Mar. 1, 2007, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00266">
<othercit>U.S. Appl. No. 11/697,024, filed Apr. 5, 2007, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00267">
<othercit>U.S. Appl. No. 11/733,902, filed Apr. 11, 2007, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00268">
<othercit>U.S. Appl. No. 11/846,696, filed Aug. 29, 2007, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00269">
<othercit>U.S. Appl. No. 11/868,013, filed Oct. 5, 2007, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00270">
<othercit>U.S. Appl. No. 12/194,593, filed Aug. 20, 2008, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00271">
<othercit>U.S. Appl. No. 11/553,130, filed Oct. 26, 2006, Burman et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00272">
<othercit>U.S. Appl. No. 11/553,142, filed Oct. 26, 2006, Burman et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00273">
<othercit>U.S. Appl. No. 12/197,809, filed Aug. 25, 2008, Amaitis et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00274">
<othercit>Notification of Transmittal of the International Search Report and the Written Opinion of the International Searching Authority for International Application No. PCT/US07/86661; 12 pages; May 12, 2008.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00275">
<othercit>Webpage: &#x201c;Dynamic In-Game Advertising&#x201d;, (http://www.wheii.com/2005<sub>&#x2014;</sub>05<sub>&#x2014;</sub>01<sub>&#x2014;</sub>archive.php), download date: Dec. 11, 2006.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00276">
<othercit>Webpage: &#x201c;Meet Steven Spielberg, hardboiled cynic&#x201d;, (http://diLsalon.com/story/entimovies/review12002/06121Iminority<sub>&#x2014;</sub>report/index. html), download date: Dec. 11, 2006.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00277">
<othercit>USPTO Office Action for U.S. Appl. No. 11/618,426, Jul. 24, 2008 (6 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00278">
<othercit>Susan Chaityn Lebovits, &#x201c;Free-Play Site Draws Card Players After Crackdown,&#x201d; Boston Globe, Boston, MA, p. E1, Nov. 5, 2007.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00279">
<othercit>U.S. Appl. No. 12/247,623, filed Oct. 8, 2008, Amaitis et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00280">
<othercit>U.S. Appl. No. 11/199,831, filed Aug. 9, 2005, Amaitis et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00281">
<othercit>U.S. Appl. No. 11/199,835, filed Aug. 9, 2005, Amaitis et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00282">
<othercit>U.S. Appl. No. 11/201,812, filed Aug. 10, 2005, Amaitis et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00283">
<othercit>U.S. Appl. No. 11/210,482, filed Aug. 24, 2005, Amaitis et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00284">
<othercit>U.S. Appl. No. 11/199,964, filed Aug. 9, 2005, Amaitis et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00285">
<othercit>U.S. Appl. No. 11/256,568, filed Oct. 21, 2005, Asher et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00286">
<othercit>U.S. Appl. No. 11/567,322, filed Dec. 6, 2006, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00287">
<othercit>&#x201c;William Hill Steps Up Mobile Betting for Closer Targeting,&#x201d; Precision Marketing, London, p. 6, Dec. 19, 2003.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00288">
<othercit>&#x201c;Ladbrokes Uses Mobile Ads to Push Grand National Betting,&#x201d; (mobile advertising) (brief article), New Media Age, p. 4, Apr. 3, 2008.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00289">
<othercit>&#x201c;Mobile Lotteries an Odds-On Favourite,&#x201d; Precision Marketing, London, p. 12, Jan. 9, 2004.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00290">
<othercit>&#x201c;Gambling Revolution Held Back by Red Tape,&#x201d; Precision Marketing, London, p. 11, Sep. 19, 2003.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00291">
<othercit>USPTO Office Action for U.S. Appl. No. 11/470,250, Dec. 5, 2008 (5 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00292">
<othercit>U.S. Appl. No. 11/621,369, filed Jan. 9, 2007, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00293">
<othercit>U.S. Appl. No. 12/147,005, filed Jun. 26, 2008, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00294">
<othercit>USPTO Notice of Allowance for U.S. Appl. No. 11/470,250, Jul. 24, 2009 (4 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00295">
<othercit>USPTO Office Action for U.S. Appl. No. 11/618,426, Apr. 14, 2009 (8 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00296">
<othercit>Notification Concerning Transmittal of International Preliminary Report on Patentability and Written Opinion for International Application No. PCT/US07/76298; 7 pages; Sep. 17, 2008.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00297">
<othercit>Notification of Transmittal of the International Search Report and the Written Opinion of the International Searching Authority for International Application No. PCT/US07/77021; 10 pages; Apr. 14, 2008.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00298">
<othercit>Notification of Transmittal of the International Search Report and the Written Opinion of the International Searching Authority for International Application No. PCT/US08/74220; 14 pages; Nov. 17, 2008.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00299">
<othercit>USPTO Office Action for U.S. Appl. No. 11/680,764, Aug. 31, 2009 (10 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00300">
<othercit>USPTO Office Action for U.S. Appl. No. 11/567,322, Sep. 16, 2009 (9 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00301">
<othercit>USPTO Office Action for U.S. Appl. No. 11/621,369, Sep. 29, 2009 (11 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00302">
<othercit>USPTO Office Action for U.S. Appl. No. 11/621,369, Nov. 10, 2010 (16 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00303">
<othercit>USPTO Office Action for U.S. Appl. No. 11/674,232, Oct. 28, 2010 (6 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00304">
<othercit>USPTO Office Action for U.S. Appl. No. 11/680,764, Dec. 7, 2010 (22 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00305">
<othercit>USPTO Pre-Brief Appeal Conference Decision for U.S. Appl. No. 11/675,182, Nov. 10, 2010 (2 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00306">
<othercit>U.S. Appl. No. 12/962,828, filed Dec. 8, 2010, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00307">
<othercit>USPTO Office Action for U.S. Appl. No. 11/733,902, May 6, 2010 (6 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00308">
<othercit>USPTO Office Action for U.S. Appl. No. 11/567,322, Apr. 30, 2009 (7 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00309">
<othercit>USPTO Office Action for U.S. Appl. No. 11/567,322, Mar. 29, 2010 (14 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00310">
<othercit>USPTO Examiner Interview Summary for U.S. Appl. No. 11/567,322, Jul. 23, 2010 (3 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00311">
<othercit>USPTO Office Action for U.S. Appl. No. 11/621,369, Apr. 27, 2010 (6 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00312">
<othercit>USPTO Office Action for U.S. Appl. No. 11/680,764, Mar. 24, 2010 (19 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00313">
<othercit>U.S. Appl. No. 12/693,668, filed Jan. 26, 2010, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00314">
<othercit>U.S. Appl. No. 12/693,524, filed Jan. 26, 2010, Lutnick et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00315">
<othercit>Australian Examiner's Report for Application No. **********, dated Aug. 21, 2009 (5 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00316">
<othercit>Australian Examiner's Report for Application No. **********, dated Jul. 12, 2010 (3 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00317">
<othercit>Australian Examiner's Report for Application No. **********, dated Jul. 15, 2010 (2 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00318">
<othercit>Australian Examiner's Report for Application No. **********, dated Jul. 22, 2010 (3 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00319">
<othercit>Australian Examiner's Report for Application No. **********, dated Jul. 23, 2010 (2 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00320">
<othercit>Players Rating System, II Dado at: http://web.archive.org/web/20040228122341/http://www.ildado.com/players<sub>&#x2014;</sub>rating<sub>&#x2014;</sub>system.html, dated: Feb. 28, 2004 (2 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00321">
<othercit>U.S. Appl. No. 12/759,757, filed Apr. 14, 2010, Inventors: Howard W. Lutnick, et al. for &#x201c;Game Of Chance Systems And Methods&#x201d; (443 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00322">
<othercit>International Preliminary Report on Patentability for International Application No. PCT/US07/86661, dated Jun. 10, 2009 (9 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00323">
<othercit>Notification of Transmittal or Search Report and Written Opinion of the ISA, or the Declaration for International Application No. PCT/US08/55209, dated Jul. 31, 2008 (8 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00324">
<othercit>International Preliminary Report on Patentability for International Application No. PCT/US08/55209, dated Sep. 1, 2009 (6 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00325">
<othercit>Notification of Transmittal or Search Report and Written Opinion of the ISA, or the Declaration for International Application No. PCT/US07/76298, dated Sep. 17, 2008 (8 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00326">
<othercit>International Preliminary Report on Patentability for International Application No. PCT/US07/76298, dated Feb. 24, 2009 (6 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00327">
<othercit>International Preliminary Report on Patentability for International Application No. PCT/US08/74220, dated Mar. 2, 2010 (10 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00328">
<othercit>USPTO Office Action for U.S. Appl. No. 11/468,809, Aug. 17, 2010 (7 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00329">
<othercit>USPTO Office Action for U.S. Appl. No. 11/567,322, Aug. 12, 2010 (13 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00330">
<othercit>USPTO Office Action for U.S. Appl. No. 11/539,518, Aug. 20, 2010 (8 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00331">
<othercit>USPTO Office Action for U.S. Appl. No. 11/533,300, Aug. 20, 2010 (7 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00332">
<othercit>PCT Search Report and Written Opinion for International Application No. PCT/US08/54128, Sep. 10, 2008 (12 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00333">
<othercit>USPTO Office Action for U.S. Appl. No. 11/675,182, May 21, 2009 (6 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00334">
<othercit>USPTO Office Action for U.S. Appl. No. 11/675,182, Sep. 4, 2009 (8 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00335">
<othercit>USPTO Office Action for U.S. Appl. No. 11/675,182, Apr. 13, 2010 (12 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00336">
<othercit>International Preliminary Report on Patentability for International Application No. PCT/US08/54128, Aug. 19, 2009 (6 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00337">
<othercit>Australian Examination Report for Application No. **********, Jun. 28, 2010 (2 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00338">
<othercit>U.S. Appl. No. 11/675,182, filed Feb. 15, 2007, Alderucci et al.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00339">
<othercit>Michael Friedman, Bet on Poker? Bodog takes bets on the 2005 WSOP, dated Jun. 24, 2005, http://www.pokernews.com/news/2005/06/bet-on-poker-bodog-wsop.htm.</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00340">
<othercit>USPTO Pre-Brief Appeal Conference Decision for U.S. Appl. No. 11/680,764, Sep. 27, 2010 (2 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00341">
<othercit>USPTO Office Action for U.S. Appl. No. 11/733,902, Oct. 6, 2010 (13 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
<citation>
<nplcit num="00342">
<othercit>U.S. Appl. No. 12/897,954, filed Oct. 5, 2010, Inventor: Howard W. Lutnick for &#x201c;Secondary Game&#x201d; (126 pages).</othercit>
</nplcit>
<category>cited by other</category>
</citation>
</references-cited>
<number-of-claims>28</number-of-claims>
<us-exemplary-claim>1</us-exemplary-claim>
<us-field-of-classification-search>
<classification-national>
<country>US</country>
<main-classification>463 16- 25</main-classification>
<additional-info>unstructured</additional-info>
</classification-national>
<classification-national>
<country>US</country>
<main-classification>273274</main-classification>
</classification-national>
<classification-national>
<country>US</country>
<main-classification>273292</main-classification>
</classification-national>
</us-field-of-classification-search>
<figures>
<number-of-drawing-sheets>9</number-of-drawing-sheets>
<number-of-figures>9</number-of-figures>
</figures>
<us-related-documents>
<continuation>
<relation>
<parent-doc>
<document-id>
<country>US</country>
<doc-number>11470250</doc-number>
<date>20060905</date>
</document-id>
<parent-grant-document>
<document-id>
<country>US</country>
<doc-number>7585217</doc-number>
</document-id>
</parent-grant-document>
</parent-doc>
<child-doc>
<document-id>
<country>US</country>
<doc-number>12512730</doc-number>
</document-id>
</child-doc>
</relation>
</continuation>
<related-publication>
<document-id>
<country>US</country>
<doc-number>20090291732</doc-number>
<kind>A1</kind>
<date>20091126</date>
</document-id>
</related-publication>
</us-related-documents>
<parties>
<applicants>
<applicant sequence="001" app-type="applicant-inventor" designation="us-only">
<addressbook>
<last-name>Lutnick</last-name>
<first-name>Howard W.</first-name>
<address>
<city>New York</city>
<state>NY</state>
<country>US</country>
</address>
</addressbook>
<nationality>
<country>omitted</country>
</nationality>
<residence>
<country>US</country>
</residence>
</applicant>
<applicant sequence="002" app-type="applicant-inventor" designation="us-only">
<addressbook>
<last-name>Alderucci</last-name>
<first-name>Dean P.</first-name>
<address>
<city>Westpoint</city>
<state>CT</state>
<country>US</country>
</address>
</addressbook>
<nationality>
<country>omitted</country>
</nationality>
<residence>
<country>US</country>
</residence>
</applicant>
<applicant sequence="003" app-type="applicant-inventor" designation="us-only">
<addressbook>
<last-name>Gelman</last-name>
<first-name>Geoffrey M.</first-name>
<address>
<city>Brooklyn</city>
<state>NY</state>
<country>US</country>
</address>
</addressbook>
<nationality>
<country>omitted</country>
</nationality>
<residence>
<country>US</country>
</residence>
</applicant>
</applicants>
<agents>
<agent sequence="01" rep-type="attorney">
<addressbook>
<last-name>Miller</last-name>
<first-name>Mark A.</first-name>
<address>
<country>unknown</country>
</address>
</addressbook>
</agent>
</agents>
</parties>
<assignees>
<assignee>
<addressbook>
<orgname>CFPH, LLC</orgname>
<role>02</role>
<address>
<city>New York</city>
<state>NY</state>
<country>US</country>
</address>
</addressbook>
</assignee>
</assignees>
<examiners>
<primary-examiner>
<last-name>Laneau</last-name>
<first-name>Ronald</first-name>
<department>3714</department>
</primary-examiner>
</examiners>
</us-bibliographic-data-grant>
<abstract id="abstract">
<p id="p-0001" num="0000">Various embodiments of amusement devices and methods for various games are described. In some embodiments, a secondary player may engage in a game started by a first player. Various additional methods and apparatus are described.</p>
</abstract>
<drawings id="DRAWINGS">
<figure id="Fig-EMI-D00000" num="00000">
<img id="EMI-D00000" he="137.67mm" wi="140.38mm" file="US07997973-20110816-D00000.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00001" num="00001">
<img id="EMI-D00001" he="159.34mm" wi="150.96mm" file="US07997973-20110816-D00001.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00002" num="00002">
<img id="EMI-D00002" he="189.65mm" wi="149.01mm" file="US07997973-20110816-D00002.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00003" num="00003">
<img id="EMI-D00003" he="185.42mm" wi="141.14mm" file="US07997973-20110816-D00003.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00004" num="00004">
<img id="EMI-D00004" he="186.77mm" wi="148.51mm" file="US07997973-20110816-D00004.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00005" num="00005">
<img id="EMI-D00005" he="99.31mm" wi="119.21mm" file="US07997973-20110816-D00005.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00006" num="00006">
<img id="EMI-D00006" he="188.81mm" wi="155.45mm" file="US07997973-20110816-D00006.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00007" num="00007">
<img id="EMI-D00007" he="157.40mm" wi="147.15mm" file="US07997973-20110816-D00007.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00008" num="00008">
<img id="EMI-D00008" he="201.08mm" wi="153.42mm" file="US07997973-20110816-D00008.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
<figure id="Fig-EMI-D00009" num="00009">
<img id="EMI-D00009" he="200.83mm" wi="150.11mm" file="US07997973-20110816-D00009.TIF" alt="embedded image" img-content="drawing" img-format="tif"/>
</figure>
</drawings>
<description id="description">
<?RELAPP description="Other Patent Relations" end="lead"?>
<heading id="h-0001" level="1">RELATED APPLICATION</heading>
<p id="p-0002" num="0001">This application is a continuation of U.S. patent application Ser. No. 11/470,250, filed Sep. 5, 2006, now U.S. Pat. No. 7,585,217 which is incorporated herein by reference.</p>
<?RELAPP description="Other Patent Relations" end="tail"?>
<?BRFSUM description="Brief Summary" end="lead"?>
<heading id="h-0002" level="1">BRIEF DESCRIPTION OF THE DRAWINGS</heading>
<p id="p-0003" num="0002"><figref idref="DRAWINGS">FIG. 1</figref> shows a system according to some embodiments.</p>
<p id="p-0004" num="0003"><figref idref="DRAWINGS">FIG. 2</figref> shows a casino server according to some embodiments.</p>
<p id="p-0005" num="0004"><figref idref="DRAWINGS">FIG. 3</figref> shows a terminal for use by a secondary player, according to some embodiments.</p>
<p id="p-0006" num="0005"><figref idref="DRAWINGS">FIG. 4</figref> shows a gaming device according to some embodiments.</p>
<p id="p-0007" num="0006"><figref idref="DRAWINGS">FIG. 5</figref> shows a monitoring device (e.g., camera, card reader) according to some embodiments.</p>
<p id="p-0008" num="0007"><figref idref="DRAWINGS">FIG. 6</figref> shows a database entry including various information about a game (e.g., date, time, outcome, player, bet amount)</p>
<p id="p-0009" num="0008"><figref idref="DRAWINGS">FIG. 7</figref> shows a database entry including various games played by a player.</p>
<p id="p-0010" num="0009"><figref idref="DRAWINGS">FIG. 8</figref> shows a touch screen display for entering betting information and tracking the progress of a game, according to some embodiments.</p>
<p id="p-0011" num="0010"><figref idref="DRAWINGS">FIG. 9</figref> shows a touch screen display for entering betting information and tracking the progress of a game, according to some embodiments.</p>
<?BRFSUM description="Brief Summary" end="tail"?>
<?DETDESC description="Detailed Description" end="lead"?>
<heading id="h-0003" level="1">DETAILED DESCRIPTION</heading>
<p id="p-0012" num="0011">The following sections I-IX provide a guide to interpreting the present application.</p>
<heading id="h-0004" level="1">I. Terms</heading>
<p id="p-0013" num="0012">The term &#x201c;product&#x201d; means any machine, manufacture and/or composition of matter, unless expressly specified otherwise.</p>
<p id="p-0014" num="0013">The term &#x201c;process&#x201d; means any process, algorithm, method or the like, unless expressly specified otherwise.</p>
<p id="p-0015" num="0014">Each process (whether called a method, algorithm or otherwise) inherently includes one or more steps, and therefore all references to a &#x201c;step&#x201d; or &#x201c;steps&#x201d; of a process have an inherent antecedent basis in the mere recitation of the term &#x2018;process&#x2019; or a like term. Accordingly, any reference in a claim to a &#x2018;step&#x2019; or &#x2018;steps&#x2019; of a process has sufficient antecedent basis.</p>
<p id="p-0016" num="0015">The term &#x201c;invention&#x201d; and the like mean &#x201c;the one or more inventions disclosed in this application&#x201d;, unless expressly specified otherwise.</p>
<p id="p-0017" num="0016">The terms &#x201c;an embodiment&#x201d;, &#x201c;embodiment&#x201d;, &#x201c;embodiments&#x201d;, &#x201c;the embodiment&#x201d;, &#x201c;the embodiments&#x201d;, &#x201c;one or more embodiments&#x201d;, &#x201c;some embodiments&#x201d;, &#x201c;certain embodiments&#x201d;, &#x201c;one embodiment&#x201d;, &#x201c;another embodiment&#x201d; and the like mean &#x201c;one or more (but not all) embodiments of the disclosed invention(s)&#x201d;, unless expressly specified otherwise.</p>
<p id="p-0018" num="0017">The term &#x201c;variation&#x201d; of an invention means an embodiment of the invention, unless expressly specified otherwise.</p>
<p id="p-0019" num="0018">A reference to &#x201c;another embodiment&#x201d; in describing an embodiment does not imply that the referenced embodiment is mutually exclusive with another embodiment (e.g., an embodiment described before the referenced embodiment), unless expressly specified otherwise.</p>
<p id="p-0020" num="0019">The terms &#x201c;including&#x201d;, &#x201c;comprising&#x201d; and variations thereof mean &#x201c;including but not limited to&#x201d;, unless expressly specified otherwise.</p>
<p id="p-0021" num="0020">The terms &#x201c;a&#x201d;, &#x201c;an&#x201d; and &#x201c;the&#x201d; mean &#x201c;one or more&#x201d;, unless expressly specified otherwise.</p>
<p id="p-0022" num="0021">The term &#x201c;plurality&#x201d; means &#x201c;two or more&#x201d;, unless expressly specified otherwise.</p>
<p id="p-0023" num="0022">The term &#x201c;herein&#x201d; means &#x201c;in the present application, including anything which may be incorporated by reference&#x201d;, unless expressly specified otherwise.</p>
<p id="p-0024" num="0023">The phrase &#x201c;at least one of&#x201d;, when such phrase modifies a plurality of things (such as an enumerated list of things), means any combination of one or more of those things, unless expressly specified otherwise. For example, the phrase &#x201c;at least one of a widget, a car and a wheel&#x201d; means either (i) a widget, (ii) a car, (iii) a wheel, (iv) a widget and a car, (v) a widget and a wheel, (vi) a car and a wheel, or (vii) a widget, a car and a wheel. The phrase &#x201c;at least one of&#x201d;, when such phrase modifies a plurality of things, does not mean &#x201c;one of each of&#x201d; the plurality of things.</p>
<p id="p-0025" num="0024">Numerical terms such as &#x201c;one&#x201d;, &#x201c;two&#x201d;, etc. when used as cardinal numbers to indicate quantity of something (e.g., one widget, two widgets), mean the quantity indicated by that numerical term, but do not mean at least the quantity indicated by that numerical term. For example, the phrase &#x201c;one widget&#x201d; does not mean &#x201c;at least one widget&#x201d;, and therefore the phrase &#x201c;one widget&#x201d; does not cover, e.g., two widgets.</p>
<p id="p-0026" num="0025">The phrase &#x201c;based on&#x201d; does not mean &#x201c;based only on&#x201d;, unless expressly specified otherwise. In other words, the phrase &#x201c;based on&#x201d; describes both &#x201c;based only on&#x201d; and &#x201c;based at least on&#x201d;. The phrase &#x201c;based at least on&#x201d; is equivalent to the phrase &#x201c;based at least in part on&#x201d;.</p>
<p id="p-0027" num="0026">The term &#x201c;represent&#x201d; and like terms are not exclusive, unless expressly specified otherwise. For example, the term &#x201c;represents&#x201d; do not mean &#x201c;represents only&#x201d;, unless expressly specified otherwise. In other words, the phrase &#x201c;the data represents a credit card number&#x201d; describes both &#x201c;the data represents only a credit card number&#x201d; and &#x201c;the data represents a credit card number and the data also represents something else&#x201d;.</p>
<p id="p-0028" num="0027">The term &#x201c;whereby&#x201d; is used herein only to precede a clause or other set of words that express only the intended result, objective or consequence of something that is previously and explicitly recited. Thus, when the term &#x201c;whereby&#x201d; is used in a claim, the clause or other words that the term &#x201c;whereby&#x201d; modifies do not establish specific further limitations of the claim or otherwise restricts the meaning or scope of the claim.</p>
<p id="p-0029" num="0028">The term &#x201c;e.g.&#x201d; and like terms mean &#x201c;for example&#x201d;, and thus does not limit the term or phrase it explains. For example, in the sentence &#x201c;the computer sends data (e.g., instructions, a data structure) over the Internet&#x201d;, the term &#x201c;e.g.&#x201d; explains that &#x201c;instructions&#x201d; are an example of &#x201c;data&#x201d; that the computer may send over the Internet, and also explains that &#x201c;a data structure&#x201d; is an example of &#x201c;data&#x201d; that the computer may send over the Internet. However, both &#x201c;instructions&#x201d; and &#x201c;a data structure&#x201d; are merely examples of &#x201c;data&#x201d;, and other things besides &#x201c;instructions&#x201d; and &#x201c;a data structure&#x201d; can be &#x201c;data&#x201d;.</p>
<p id="p-0030" num="0029">The term &#x201c;i.e.&#x201d; and like terms mean &#x201c;that is&#x201d;, and thus limits the term or phrase it explains. For example, in the sentence &#x201c;the computer sends data (i.e., instructions) over the Internet&#x201d;, the term &#x201c;i.e.&#x201d; explains that &#x201c;instructions&#x201d; are the &#x201c;data&#x201d; that the computer sends over the Internet.</p>
<p id="p-0031" num="0030">Any given numerical range shall include whole and fractions of numbers within the range. For example, the range &#x201c;1 to 10&#x201d; shall be interpreted to specifically include whole numbers between 1 and 10 (e.g., 1, 2, 3, 4, . . . 9) and non-whole numbers (e.g., 1.1, 1.2, . . . 1.9).</p>
<heading id="h-0005" level="1">II. Determining</heading>
<p id="p-0032" num="0031">The term &#x201c;determining&#x201d; and grammatical variants thereof (e.g., to determine a price, determining a value, determine an object which meets a certain criterion) is used in an extremely broad sense. The term &#x201c;determining&#x201d; encompasses a wide variety of actions and therefore &#x201c;determining&#x201d; can include calculating, computing, processing, deriving, investigating, looking up (e.g., looking up in a table, a database or another data structure), ascertaining and the like. Also, &#x201c;determining&#x201d; can include receiving (e.g., receiving information), accessing (e.g., accessing data in a memory) and the like. Also, &#x201c;determining&#x201d; can include resolving, selecting, choosing, establishing, and the like.</p>
<p id="p-0033" num="0032">The term &#x201c;determining&#x201d; does not imply certainty or absolute precision, and therefore &#x201c;determining&#x201d; can include estimating, extrapolating, predicting, guessing and the like.</p>
<p id="p-0034" num="0033">The term &#x201c;determining&#x201d; does not imply that mathematical processing must be performed, and does not imply that numerical methods must be used, and does not imply that an algorithm or process is used.</p>
<p id="p-0035" num="0034">The term &#x201c;determining&#x201d; does not imply that any particular device must be used. For example, a computer need not necessarily perform the determining.</p>
<heading id="h-0006" level="1">III. Indication</heading>
<p id="p-0036" num="0035">The term &#x201c;indication&#x201d; is used in an extremely broad sense. The term &#x201c;indication&#x201d; may, among other things, encompass a sign, symptom, or token of something else.</p>
<p id="p-0037" num="0036">The term &#x201c;indication&#x201d; may be used to refer to any indicia and/or other information indicative of or associated with a subject, item, entity, and/or other object and/or idea.</p>
<p id="p-0038" num="0037">As used herein, the phrases &#x201c;information indicative of&#x201d; and &#x201c;indicia&#x201d; may be used to refer to any information that represents, describes, and/or is otherwise associated with a related entity, subject, or object.</p>
<p id="p-0039" num="0038">Indicia of information may include, for example, a code, a reference, a link, a signal, an identifier, and/or any combination thereof and/or any other informative representation associated with the information.</p>
<p id="p-0040" num="0039">In some embodiments, indicia of information (or indicative of the information) may be or include the information itself and/or any portion or component of the information. In some embodiments, an indication may include a request, a solicitation, a broadcast, and/or any other form of information gathering and/or dissemination.</p>
<heading id="h-0007" level="1">IV. Forms of Sentences</heading>
<p id="p-0041" num="0040">Where a limitation of a first claim would cover one of a feature as well as more than one of a feature (e.g., a limitation such as &#x201c;at least one widget&#x201d; covers one widget as well as more than one widget), and where in a second claim that depends on the first claim, the second claim uses a definite article &#x201c;the&#x201d; to refer to the limitation (e.g., &#x201c;the widget&#x201d;), this does not imply that the first claim covers only one of the feature, and this does not imply that the second claim covers only one of the feature (e.g., &#x201c;the widget&#x201d; can cover both one widget and more than one widget).</p>
<p id="p-0042" num="0041">When an ordinal number (such as &#x201c;first&#x201d;, &#x201c;second&#x201d;, &#x201c;third&#x201d; and so on) is used as an adjective before a term, that ordinal number is used (unless expressly specified otherwise) merely to indicate a particular feature, such as to distinguish that particular feature from another feature that is described by the same term or by a similar term. For example, a &#x201c;first widget&#x201d; may be so named merely to distinguish it from, e.g., a &#x201c;second widget&#x201d;. Thus, the mere usage of the ordinal numbers &#x201c;first&#x201d; and &#x201c;second&#x201d; before the term &#x201c;widget&#x201d; does not indicate any other relationship between the two widgets, and likewise does not indicate any other characteristics of either or both widgets. For example, the mere usage of the ordinal numbers &#x201c;first&#x201d; and &#x201c;second&#x201d; before the term &#x201c;widget&#x201d; (1) does not indicate that either widget comes before or after any other in order or location; (2) does not indicate that either widget occurs or acts before or after any other in time; and (3) does not indicate that either widget ranks above or below any other, as in importance or quality. In addition, the mere usage of ordinal numbers does not define a numerical limit to the features identified with the ordinal numbers. For example, the mere usage of the ordinal numbers &#x201c;first&#x201d; and &#x201c;second&#x201d; before the term &#x201c;widget&#x201d; does not indicate that there must be no more than two widgets.</p>
<p id="p-0043" num="0042">When a single device or article is described herein, more than one device/article (whether or not they cooperate) may alternatively be used in place of the single device/article that is described. Accordingly, the functionality that is described as being possessed by a device may alternatively be possessed by more than one device/article (whether or not they cooperate).</p>
<p id="p-0044" num="0043">Similarly, where more than one device or article is described herein (whether or not they cooperate), a single device/article may alternatively be used in place of the more than one device or article that is described. For example, a plurality of computer-based devices may be substituted with a single computer-based device. Accordingly, the various functionality that is described as being possessed by more than one device or article may alternatively be possessed by a single device/article.</p>
<p id="p-0045" num="0044">The functionality and/or the features of a single device that is described may be alternatively embodied by one or more other devices which are described but are not explicitly described as having such functionality/features. Thus, other embodiments need not include the described device itself, but rather can include the one or more other devices which would, in those other embodiments, have such functionality/features.</p>
<heading id="h-0008" level="1">V. Disclosed Examples and Terminology are Not Limiting</heading>
<p id="p-0046" num="0045">Neither the Title (set forth at the beginning of the first page of the present application) nor the Abstract (set forth at the end of the present application) is to be taken as limiting in any way as the scope of the disclosed invention(s). An Abstract has been included in this application merely because an Abstract of not more than 150 words is required under 37 C.F.R. &#xa7;1.72(b).</p>
<p id="p-0047" num="0046">The title of the present application and headings of sections provided in the present application are for convenience only, and are not to be taken as limiting the disclosure in any way.</p>
<p id="p-0048" num="0047">Numerous embodiments are described in the present application, and are presented for illustrative purposes only. The described embodiments are not, and are not intended to be, limiting in any sense. The presently disclosed invention(s) are widely applicable to numerous embodiments, as is readily apparent from the disclosure. One of ordinary skill in the art will recognize that the disclosed invention(s) may be practiced with various modifications and alterations, such as structural, logical, software, and electrical modifications. Although particular features of the disclosed invention(s) may be described with reference to one or more particular embodiments and/or drawings, it should be understood that such features are not limited to usage in the one or more particular embodiments or drawings with reference to which they are described, unless expressly specified otherwise.</p>
<p id="p-0049" num="0048">The present disclosure is not a literal description of all embodiments of the invention(s). Also, the present disclosure is not a listing of features of the invention(s) which must be present in all embodiments.</p>
<p id="p-0050" num="0049">Devices that are described as in communication with each other need not be in continuous communication with each other, unless expressly specified otherwise. On the contrary, such devices need only transmit to each other as necessary or desirable, and may actually refrain from exchanging data most of the time. For example, a machine in communication with another machine via the Internet may not transmit data to the other machine for long period of time (e.g., weeks at a time). In addition, devices that are in communication with each other may communicate directly or indirectly through one or more intermediaries.</p>
<p id="p-0051" num="0050">A description of an embodiment with several components or features does not imply that all or even any of such components/features are required. On the contrary, a variety of optional components are described to illustrate the wide variety of possible embodiments of the present invention(s). Unless otherwise specified explicitly, no component/feature is essential or required.</p>
<p id="p-0052" num="0051">Although process steps, algorithms or the like may be described in a particular sequential order, such processes may be configured to work in different orders. In other words, any sequence or order of steps that may be explicitly described does not necessarily indicate a requirement that the steps be performed in that order. The steps of processes described herein may be performed in any order practical. Further, some steps may be performed simultaneously despite being described or implied as occurring non-simultaneously (e.g., because one step is described after the other step). Moreover, the illustration of a process by its depiction in a drawing does not imply that the illustrated process is exclusive of other variations and modifications thereto, does not imply that the illustrated process or any of its steps are necessary to the invention(s), and does not imply that the illustrated process is preferred.</p>
<p id="p-0053" num="0052">Although a process may be described as including a plurality of steps, that does not imply that all or any of the steps are preferred, essential or required. Various other embodiments within the scope of the described invention(s) include other processes that omit some or all of the described steps. Unless otherwise specified explicitly, no step is essential or required.</p>
<p id="p-0054" num="0053">Although a process may be described singly or without reference to other products or methods, in an embodiment the process may interact with other products or methods. For example, such interaction may include linking one business model to another business model. Such interaction may be provided to enhance the flexibility or desirability of the process.</p>
<p id="p-0055" num="0054">Although a product may be described as including a plurality of components, aspects, qualities, characteristics and/or features, that does not indicate that any or all of the plurality are preferred, essential or required. Various other embodiments within the scope of the described invention(s) include other products that omit some or all of the described plurality.</p>
<p id="p-0056" num="0055">An enumerated list of items (which may or may not be numbered) does not imply that any or all of the items are mutually exclusive, unless expressly specified otherwise. Likewise, an enumerated list of items (which may or may not be numbered) does not imply that any or all of the items are comprehensive of any category, unless expressly specified otherwise. For example, the enumerated list &#x201c;a computer, a laptop, a PDA&#x201d; does not imply that any or all of the three items of that list are mutually exclusive and does not imply that any or all of the three items of that list are comprehensive of any category.</p>
<p id="p-0057" num="0056">An enumerated list of items (which may or may not be numbered) does not imply that any or all of the items are equivalent to each other or readily substituted for each other.</p>
<p id="p-0058" num="0057">All embodiments are illustrative, and do not imply that the invention or any embodiments were made or performed, as the case may be.</p>
<heading id="h-0009" level="1">VI. Computing</heading>
<p id="p-0059" num="0058">It will be readily apparent to one of ordinary skill in the art that the various processes described herein may be implemented by, e.g., appropriately programmed general purpose computers, special purpose computers and computing devices. Typically a processor (e.g., one or more microprocessors, one or more microcontrollers, one or more digital signal processors) will receive instructions (e.g., from a memory or like device), and execute those instructions, thereby performing one or more processes defined by those instructions.</p>
<p id="p-0060" num="0059">A &#x201c;processor&#x201d; means one or more microprocessors, central processing units (CPUs), computing devices, microcontrollers, digital signal processors, or like devices or any combination thereof.</p>
<p id="p-0061" num="0060">Thus a description of a process is likewise a description of an apparatus for performing the process. The apparatus that performs the process can include, e.g., a processor and those input devices and output devices that are appropriate to perform the process.</p>
<p id="p-0062" num="0061">Further, programs that implement such methods (as well as other types of data) may be stored and transmitted using a variety of media (e.g., computer readable media) in a number of manners. In some embodiments, hard-wired circuitry or custom hardware may be used in place of, or in combination with, some or all of the software instructions that can implement the processes of various embodiments. Thus, various combinations of hardware and software may be used instead of software only.</p>
<p id="p-0063" num="0062">The term &#x201c;computer-readable medium&#x201d; refers to any medium, a plurality of the same, or a combination of different media, that participate in providing data (e.g., instructions, data structures) which may be read by a computer, a processor or a like device. Such a medium may take many forms, including but not limited to, non-volatile media, volatile media, and transmission media. Non-volatile media include, for example, optical or magnetic disks and other persistent memory. Volatile media include dynamic random access memory (DRAM), which typically constitutes the main memory. Transmission media include coaxial cables, copper wire and fiber optics, including the wires that comprise a system bus coupled to the processor. Transmission media may include or convey acoustic waves, light waves and electromagnetic emissions, such as those generated during radio frequency (RF) and infrared (IR) data communications. Common forms of computer-readable media include, for example, a floppy disk, a flexible disk, hard disk, magnetic tape, any other magnetic medium, a CD-ROM, DVD, any other optical medium, punch cards, paper tape, any other physical medium with patterns of holes, a RAM, a PROM, an EPROM, a FLASH-EEPROM, any other memory chip or cartridge, a carrier wave as described hereinafter, or any other medium from which a computer can read.</p>
<p id="p-0064" num="0063">Various forms of computer readable media may be involved in carrying data (e.g. sequences of instructions) to a processor. For example, data may be (i) delivered from RAM to a processor; (ii) carried over a wireless transmission medium; (iii) formatted and/or transmitted according to numerous formats, standards or protocols, such as Ethernet (or IEEE 802.3), SAP, ATP, Bluetooth&#x2122;, and TCP/IP, TDMA, CDMA, and 3G; and/or (iv) encrypted to ensure privacy or prevent fraud in any of a variety of ways well known in the art.</p>
<p id="p-0065" num="0064">Thus a description of a process is likewise a description of a computer-readable medium storing a program for performing the process. The computer-readable medium can store (in any appropriate format) those program elements which are appropriate to perform the method.</p>
<p id="p-0066" num="0065">Just as the description of various steps in a process does not indicate that all the described steps are required, embodiments of an apparatus include a computer/computing device operable to perform some (but not necessarily all) of the described process.</p>
<p id="p-0067" num="0066">Likewise, just as the description of various steps in a process does not indicate that all the described steps are required, embodiments of a computer-readable medium storing a program or data structure include a computer-readable medium storing a program that, when executed, can cause a processor to perform some (but not necessarily all) of the described process.</p>
<p id="p-0068" num="0067">Where databases are described, it will be understood by one of ordinary skill in the art that (i) alternative database structures to those described may be readily employed, and (ii) other memory structures besides databases may be readily employed. Any illustrations or descriptions of any sample databases presented herein are illustrative arrangements for stored representations of information. Any number of other arrangements may be employed besides those suggested by, e.g., tables illustrated in drawings or elsewhere. Similarly, any illustrated entries of the databases represent exemplary information only; one of ordinary skill in the art will understand that the number and content of the entries can be different from those described herein. Further, despite any depiction of the databases as tables, other formats (including relational databases, object-based models and/or distributed databases) could be used to store and manipulate the data types described herein. Likewise, object methods or behaviors of a database can be used to implement various processes, such as the described herein. In addition, the databases may, in a known manner, be stored locally or remotely from a device which accesses data in such a database.</p>
<p id="p-0069" num="0068">Various embodiments can be configured to work in a network environment including a computer that is in communication (e.g., via a communications network) with one or more devices. The computer may communicate with the devices directly or indirectly, via any wired or wireless medium (e.g. the Internet, LAN, WAN or Ethernet, Token Ring, a telephone line, a cable line, a radio channel, an optical communications line, commercial on-line service providers, bulletin board systems, a satellite communications link, a combination of any of the above). Each of the devices may themselves comprise computers or other computing devices, such as those based on the Intel&#xae; Pentium&#xae; or Centrino&#x2122; processor, that are adapted to communicate with the computer. Any number and type of devices may be in communication with the computer.</p>
<p id="p-0070" num="0069">In an embodiment, a server computer or centralized authority may not be necessary or desirable. For example, the present invention may, in an embodiment, be practiced on one or more devices without a central authority. In such an embodiment, any functions described herein as performed by the server computer or data described as stored on the server computer may instead be performed by or stored on one or more such devices.</p>
<p id="p-0071" num="0070">Where a process is described, in an embodiment the process may operate without any user intervention. In another embodiment, the process includes some human intervention (e.g., a step is performed by or with the assistance of a human).</p>
<heading id="h-0010" level="1">VII. Continuing Applications</heading>
<p id="p-0072" num="0071">The present disclosure provides, to one of ordinary skill in the art, an enabling description of several embodiments and/or inventions. Some of these embodiments and/or inventions may not be claimed in the present application, but may nevertheless be claimed in one or more continuing applications that claim the benefit of priority of the present application. Applicants intend to file additional applications to pursue patents for subject matter that has been disclosed and enabled but not claimed in the present application.</p>
<heading id="h-0011" level="1">VIII. 35 U.S.C. &#xa7;112, Paragraph 6</heading>
<p id="p-0073" num="0072">In a claim, a limitation of the claim which includes the phrase &#x201c;means for&#x201d; or the phrase &#x201c;step for&#x201d; means that 35 U.S.C. &#xa7;112, paragraph 6, applies to that limitation.</p>
<p id="p-0074" num="0073">In a claim, a limitation of the claim which does not include the phrase &#x201c;means for&#x201d; or the phrase &#x201c;step for&#x201d; means that 35 U.S.C. &#xa7;112, paragraph 6 does not apply to that limitation, regardless of whether that limitation recites a function without recitation of structure, material or acts for performing that function. For example, in a claim, the mere use of the phrase &#x201c;step of&#x201d; or the phrase &#x201c;steps of&#x201d; in referring to one or more steps of the claim or of another claim does not mean that 35 U.S.C. &#xa7;112, paragraph 6, applies to that step(s).</p>
<p id="p-0075" num="0074">With respect to a means or a step for performing a specified function in accordance with 35 U.S.C. &#xa7;112, paragraph 6, the corresponding structure, material or acts described in the specification, and equivalents thereof, may perform additional functions as well as the specified function.</p>
<p id="p-0076" num="0075">Computers, processors, computing devices and like products are structures that can perform a wide variety of functions. Such products can be operable to perform a specified function by executing one or more programs, such as a program stored in a memory device of that product or in a memory device which that product accesses. Unless expressly specified otherwise, such a program need not be based on any particular algorithm, such as any particular algorithm that might be disclosed in the present application. It is well known to one of ordinary skill in the art that a specified function may be implemented via different algorithms, and any of a number of different algorithms would be a mere design choice for carrying out the specified function.</p>
<p id="p-0077" num="0076">Therefore, with respect to a means or a step for performing a specified function in accordance with 35 U.S.C. &#xa7;112, paragraph 6, structure corresponding to a specified function includes any product programmed to perform the specified function. Such structure includes programmed products which perform the function, regardless of whether such product is programmed with (i) a disclosed algorithm for performing the function, (ii) an algorithm that is similar to a disclosed algorithm, or (iii) a different algorithm for performing the function.</p>
<heading id="h-0012" level="1">IX. Prosecution History</heading>
<p id="p-0078" num="0077">In interpreting the present application (which includes the claims), one of ordinary skill in the art shall refer to the prosecution history of the present application, but not to the prosecution history of any other patent or patent application, regardless of whether there are other patent applications that are considered related to the present application.</p>
<heading id="h-0013" level="1">X. Embodiments of the Invention</heading>
<heading id="h-0014" level="1">Terms</heading>
<p id="p-0079" num="0078">As used herein, the term &#x201c;viewing window&#x201d; includes an area of a gaming device at which symbols or outcomes are visible. The area may, for instance, include a pane of glass or other transparent material situated over reels of the gaming device. Thus, only the portion of the reels under the transparent material may be visible to the player. A viewing window may include a display screen, in some embodiments. The symbols or outcomes visible in the viewing window may include the symbols or outcomes that determine the player's winnings.</p>
<p id="p-0080" num="0079"><figref idref="DRAWINGS">FIG. 1</figref> shows a system according to some embodiments. According to some embodiments, Casino A and Casino B may represent facilities where participation in games of chance or in other contests is permitted. In various embodiments, in Casinos A and B, players may place bets on games or contests, and/or may win or lose money based on games or contests. The system of <figref idref="DRAWINGS">FIG. 1</figref> may permit secondary players in Casino A and secondary players in Casino B to participate in the games of primary players who are at Casino A. Further, the system of <figref idref="DRAWINGS">FIG. 1</figref> may permit a secondary player outside of Casinos A or B to participate in games of primary players at casino A. Further, the system of <figref idref="DRAWINGS">FIG. 1</figref> may permit regulators to track various data related to the games of primary players played at Casino A, to the participation in games by secondary players who are at Casino A, to the participation in games by secondary players who are at Casino B, and to the participation in games by secondary players who are at neither Casino A nor Casino B. According to some embodiments, Casino A may include a server <b>110</b>. The server may be in communication with a gaming device <b>130</b>, a monitoring device <b>160</b>, and a terminal of secondary player X <b>140</b>, each of which may lie within the premises of Casino A. Server <b>110</b> may further be in communication with server <b>120</b> of Casino B, with a server of a regulator <b>170</b>, and with a device of a secondary player Z <b>190</b>, where the secondary player device <b>190</b> is not located on the premises of Casino A nor Casino B. Communication between server <b>110</b> and the device <b>190</b> may occur through an external network <b>180</b>, e.g., through the Internet. Casino B may include a server <b>120</b> which is in communication with server <b>110</b>, with the server of a regulator <b>170</b>, and with a terminal of secondary player Y <b>150</b>, which may lie within the premises of Casino B.</p>
<p id="p-0081" num="0080">In some embodiments, the server of Casino A <b>110</b> may receive data about a game from gaming device <b>130</b> or from monitoring device <b>160</b>. A monitoring device may include a device such as a camera or microphone which may monitor a game at Casino A and transmit data about the game to the server of Casino A. The server of Casino A may transmit data received from gaming device <b>130</b> or monitoring device <b>160</b> to the terminal of a secondary player X <b>140</b> so as to allow the terminal <b>140</b> to recreate the game, to accept bets from secondary player X on the game, and to pay winnings to secondary player X based on the game.</p>
<p id="p-0082" num="0081">The server of Casino A <b>110</b> may further transmit received data about a game to the server of Casino B <b>120</b>. The server of Casino B may, in turn, transmit such data to the terminal of a secondary player Y <b>150</b> so as to allow the terminal <b>150</b> to recreate the game, to accept bets from secondary player Y on the game, and to pay winnings to secondary player Y based on the game.</p>
<p id="p-0083" num="0082">The server of Casino A <b>110</b> may further transmit received data about a game to the device of secondary player Z <b>190</b>, e.g., through the Internet. The device of secondary player Z <b>190</b> may, in turn, recreate the game for secondary player Z, receive bets on the game from secondary player Z, and/or credit winnings to secondary player Z based on the game.</p>
<p id="p-0084" num="0083">The server of Casino A <b>110</b> may further transmit received data about a game to the server of the regulator <b>170</b>. Such data may allow the regulator to monitor the fairness of games, to watch for illegal gaming, to track taxable income of the casino, or to perform any other desired function.</p>
<p id="p-0085" num="0084">In various embodiments, the terminal of secondary player X <b>140</b> may transmit to the server of Casino A <b>110</b> data about the activities of secondary player X at the terminal. Further, the terminal of secondary player Y <b>150</b> may transmit to the server of Casino B <b>120</b> data about the activities of secondary player Y at the terminal. The server of Casino B <b>120</b> may transmit such data to the server of Casino A <b>110</b>. Further, the device of secondary player Z <b>150</b> may transmit to the server of Casino A <b>110</b> data about the activities of secondary player Z at the device. Data received by the server of Casino A <b>110</b> from terminals <b>140</b> and <b>150</b>, and from device <b>190</b> may allow the server of Casino A to tracking winnings and losses of secondary players X, Y, and Z; to determine which data (e.g., data about which games) to transmit to the terminals or device; to determine an amount owed to Casino A by Casino B for use of data from Casino A; and so on. Further, data received by the server of Casino A <b>110</b> from terminals <b>140</b> and <b>150</b>, and from device <b>190</b> may be forwarded to the server of the regulator <b>170</b>. The regulator may use such data to track the bets of secondary players, to check for illegal gambling, to monitor the fairness of games, etc.</p>
<p id="p-0086" num="0085">It should be appreciated that the system of <figref idref="DRAWINGS">FIG. 1</figref> represents a system according to some embodiments, and that other servers, devices, terminals, networks, and communication links may be present in various embodiments.</p>
<p id="p-0087" num="0086"><figref idref="DRAWINGS">FIG. 2</figref> shows the Casino A server according to some embodiments. In various embodiments a similar server may constitute the Casino B server, or the server of any other casino. The storage device <b>230</b> may store program data. The program data may be used to direct the processor <b>210</b> to execute algorithms in accordance with various embodiments. The storage device <b>230</b> may store other types of data. Such data may include data received from the play of games; data that can be used to recreate games; data describing bets, wins, and loss of primary and secondary players; data describing the current locations or activities of primary or secondary players; data describing amounts owed to a casino; and so on. Communication port <b>220</b> may be used to transmit and/or to receive data. Communication port <b>220</b> may include an antenna, a wireless transmitter, a signal generator, a router, or any other communication device. Any data transmitted or received may be stored, at least at some point, in storage device <b>230</b>.</p>
<p id="p-0088" num="0087"><figref idref="DRAWINGS">FIG. 3</figref> shows a gaming device <b>130</b> according to some embodiments. The storage device <b>330</b> may store program data. The program data may be used to direct the processor <b>310</b> to execute algorithms in accordance with various embodiments. Program data may include data used to generate graphics, to determine game outcomes, to compute winnings, and so on. The storage device <b>330</b> may store other types of data. Such data may include data describing bets, wins, and losses by a primary player at gaming device <b>130</b>. Input device <b>340</b> may include sensors, buttons, touch screens, microphones, bill validators, coin acceptors, card readers, and any other means by which a primary player or other party may interact with gaming device <b>130</b>. For example, the input device <b>340</b> may include a &#x201c;bet&#x201d; button.</p>
<p id="p-0089" num="0088">The output device <b>350</b> may include display screens, microphones, lights, coin dispensers, buzzers, and any other means by which a gaming device may provide a signal to the secondary player. The communication port <b>320</b> may be used to transmit and/or to receive data.</p>
<p id="p-0090" num="0089"><figref idref="DRAWINGS">FIG. 4</figref> shows a terminal <b>140</b> for use by a secondary player, according to some embodiments. The storage device <b>430</b> may store program data. The program data may be used to direct the processor <b>410</b> to execute algorithms in accordance with various embodiments. Program data may include data used to a recreate games or depictions of games based on data received about original games. Program data may include data used to generate graphics, to display game outcomes, to compute winnings, and so on. The storage device <b>430</b> may store other types of data. Such data may include data describing bets, wins, and losses by a secondary player at terminal <b>140</b>. Input device <b>340</b> may include sensors, buttons, touch screens, microphones, bill validators, coin acceptors, card readers, and any other means by which a secondary player or other party may interact with terminal <b>130</b>. For example, the input device <b>340</b> may include a &#x201c;bet&#x201d; button.</p>
<p id="p-0091" num="0090">The output device <b>350</b> may include display screens, microphones, lights, coin dispensers, buzzers, and any other means by which terminal <b>140</b> may provide a signal to the secondary player. The communication port <b>320</b> may be used to transmit and/or to receive data.</p>
<p id="p-0092" num="0091"><figref idref="DRAWINGS">FIG. 5</figref> shows a monitoring device <b>160</b> according to some embodiments. The monitoring device may receive data about a game via input device <b>530</b>. The input device <b>530</b> may include a camera, microphone, pressure sensor, bar code scanner, sensor, button, and so on. For example, an input device may include a camera that is pointed at a table where a game of blackjack is being played. For example, an input device may include a camera that is pointed at the viewing window of a slot machine. Communication port <b>520</b> may be used to transmit data received by the input device to e.g., a casino server. In various embodiments, the monitoring device may serve multiple purposes, some of which may not involve receiving data about a game. For example, a monitoring device may include a camera which also serves security purposes at casinos.</p>
<p id="p-0093" num="0092"><figref idref="DRAWINGS">FIG. 6</figref> shows a database entry <b>600</b> including various information about a game. The database entry may store various aspects of a game played by primary player (e.g., by Jane Smith). Such data may later be used to allow a secondary player to participate in the game.</p>
<p id="p-0094" num="0093"><figref idref="DRAWINGS">FIG. 7</figref> shows a database entry <b>700</b> including various games played by a player. The player may be a primary player. The data in database entry <b>700</b> may allow a secondary player to examine historical data about the games of a primary player (e.g., about the games of Sam Hunter), including statistics about the games (e.g., the profits made in the last 100 games).</p>
<p id="p-0095" num="0094"><figref idref="DRAWINGS">FIG. 8</figref> shows a display screen for entering betting information and tracking the progress of a game, according to some embodiments. The display screen may be sensitive and/or responsive to touch and may thereby function as a touch screen, in some embodiments. One area of the display screen lists the favored primary players of the secondary player currently viewing the display. Presumably, the secondary player has logged in or otherwise identified himself to the terminal or device to which the display belongs. The secondary player may have previously indicated his favored primary players. The casino may thus track the whereabouts of the favored primary players and alert the secondary player when a favored primary player begins play.</p>
<p id="p-0096" num="0095">Another area of the display screen includes an announcements area. The casino may make announcements to the secondary player. Such announcements may include promotional announcements. For example, such announcements may include announcements of discounts at casino or other restaurants, announcements of discounts on shows, announcements about upcoming concerts or boxing matches, announcements about discounts on hotel rooms, and so on. Announcements may include promotions for other products, such as automobiles, toothpaste, or plane flights to the Caribbean. Announcements may further include announcements about primary players in which the secondary player may be interested. For example, an announcement may indicate that a favored primary player of the secondary player has just begun play.</p>
<p id="p-0097" num="0096">Another area of the display screen includes a list of primary players that are available in the sense that the secondary player may participate in the games of these primary players. This display area may identify the primary player, either by real name or by an alias, such as &#x201c;TeeBone&#x201d;. The alias may allow a primary player to maintain some anonymity or privacy. This display area may further indicate a game which the primary player is playing (and thus the game the secondary player would be participating in), a minimum bet required of the secondary player to participate in the game, and one or more statistics related to the primary players. For example, statistics may indicate a number of consecutive games won by the primary players. This display area may further include areas where a secondary player can touch in order to begin participating in the games of a primary player. For example, by touching an area labeled &#x201c;select&#x201d; next to primary player Robert Clements, the secondary player may begin participating in the games of Robert Clemens.</p>
<p id="p-0098" num="0097">Another area of the display screen includes windows where a secondary player may track the progress of games in which he is participating. <figref idref="DRAWINGS">FIG. 8</figref> depicts a first window where the secondary player can follow the game of primary player &#x201c;TeeBone&#x201d;, in whose game the secondary player is participating. The game is blackjack, and the secondary player has a bet of $5 riding on the game. The game is currently in progress. <figref idref="DRAWINGS">FIG. 8</figref> depicts a second window where the secondary player can follow the game of primary player Sue Baker. The game is a slot machine game. The game has just finished with an outcome of &#x201c;cherry-bar-cherry&#x201d;. The secondary player has just won $6 on the game. Now, the secondary player has the opportunity to place bets on the next game, as indicated by the status &#x201c;open for bets&#x201d;.</p>
<p id="p-0099" num="0098">Another area of the display screen includes a display of the credit balance of the secondary player. These credits may be used to bet on games in which the secondary player is participating. Each credit may correspond, for example, to $0.25 in value. The secondary player may place bets using the betting areas of the display screen, including a &#x201c;Bet 25&#xa2;&#x201d; area, a &#x201c;Bet $1&#x201d; area, a &#x201c;Bet $5&#x201d; area, a &#x201c;Repeat Last Bet&#x201d; area, and an &#x201c;Auto Bet&#x201d; area. When touched, such areas may apply to only the game which has a status of &#x201c;Open for Bets&#x201d;. For example, touching the &#x201c;Bet 1&#x201d; may cause a bet of $1 to be placed on the game of Sue Baker, since it is that game which has the status of &#x201c;Open for Bets&#x201d;. In this way, there need not be a separate set of betting buttons for every game in which the secondary player is participating. The &#x201c;Repeat Last Bet&#x201d; area may allow the secondary player to easily repeat a prior bet that may take extra effort to enter using the other betting areas. For example, rather than touching the &#x201c;Bet $1&#x201d; area 4 times to enter a $4 bet, the secondary player might simply touch the &#x201c;Repeat Last Bet&#x201d; area to repeat a prior bet of $4. The &#x201c;Auto Bet&#x201d; area may allow the secondary player to continue making the same bet on each new game, for example, without having to always enter a bet. In some embodiments, the secondary player may program in a particular betting strategy and then touch the &#x201c;Auto Bet&#x201d; area to have the strategy executed automatically by the terminal of the secondary player. The &#x201c;Lock Game&#x201d; area may allow the secondary player to prevent access to the terminal by other secondary players while he steps away for a break. The &#x201c;Order Drinks&#x201d; area may allow the secondary player to order drinks or other items and have them delivered to his terminal without ever leaving.</p>
<p id="p-0100" num="0099">As will be appreciated, the various areas of the touch screen that allow touch interaction may also be implemented using ordinary buttons or any other interactive technology.</p>
<p id="p-0101" num="0100">It should be appreciated that the figures do not necessarily show everything that might be included in a system, object, machine, device, etc. For example, although not shown in <figref idref="DRAWINGS">FIG. 3</figref>, gaming device <b>130</b> may include a coin hopper.
<ul id="ul0001" list-style="none">
    <li id="ul0001-0001" num="0101">1. One player bets on the outcome of a game of another player. For example, one player bets on whether a winning outcome will be achieved in the game of another player. For example, one player bets on whether another player will win. In various embodiments, one player may place a bet and either win or lose money based on the results of a game played by another player. As used herein, &#x201c;primary player&#x201d;, &#x201c;primary players&#x201d;, and the like, may refer to a player or players who most directly participate in a game, such as a casino game. A primary player may, for example, be physically located at a slot machine and may participate in a game at the slot machine by inserting a coin, indicating a bet amount, and pulling a handle of the slot machine. A primary player may also be physically located at a table game, such as a game of blackjack with a live dealer. In various embodiments, a primary player directly initiates a game in which he participates, e.g., by pulling the handle of slot machine or physically placing a bet at a table game and motioning to a dealer that he is interested in playing. In various embodiments, a particular game would not occur but for the actions of the primary player.</li>
    <li id="ul0001-0002" num="0102">&#x2003;As used herein, &#x201c;secondary player&#x201d;, &#x201c;secondary players&#x201d;, and the like, may refer to a player or players who participate or may come to participate in games played by primary players or by other secondary players. For example, a secondary player places a bet on a game in which a primary player is involved. The secondary player wins if the primary player wins, and the secondary player loses if the primary player loses. In another example, a secondary player places a bet for a game that has already occurred. When placing the bet, the secondary player does not know the outcome of the game. Once the secondary player has placed the bet, the outcome of the game may be revealed to the secondary player, and the secondary player may be paid if the outcome is a winning outcome. In another embodiment, secondary player A places a $10 bet on secondary player B, betting that secondary player B will win a game on which secondary player B has placed a $20 bet. If secondary player B wins the $20 bet, then secondary player A will win the $10 bet. In various embodiments, the secondary player does not initiate the game in which he participates. In various embodiments, a game in which the secondary player participates would occur whether or not the secondary player chose to bet on the game. The game in which a secondary player participates may be initiated by a primary player or may be initiated automatically, e.g., by a computer program.</li>
    <li id="ul0001-0003" num="0103">&#x2003;Where ever data is used herein, it should be understood that such data may be stored, such as in a database or in any other suitable medium, format, or data structure. Data may be stored in either a fixed location or throughout distributed locations. Data may be stored either in a single location or in multiple locations (e.g., in multiple redundant locations). The data may be retrieved as needed from its storage location. When data is generated but not immediately needed, such data may be stored for later retrieval. Data may be accessible by reference to any part of the data, including any tag or label associated with the data. For example, if some data elements of a set of data elements are known, the remaining data elements from the set of data elements may be retrieved based on the known data elements. For example, the known data elements may serve as a search key for finding the remaining data elements in the set of data elements.</li>
    <li id="ul0001-0004" num="0104">&#x2003;In all applicable embodiments described herein, any data generated, transmitted, stored, retrieved, or used may also be stored for auditing purposes. Such data may be made available to regulators to casinos (e.g., to casinos generating the data; e.g., to casinos using the data), or to any other relevant party. Data that may be stored may include data describing the size of a bet made by a primary player on a game, the type of bet made by a primary player on a game, intermediate events that occurred during a game (e.g., rolls prior to the final roll in a game of craps), the date of a game, the decision options that were available in a game (e.g., hit, stand in blackjack), the decisions that were made in a game, the outcome of a game, the amount paid to the winner of a game, and so on.</li>
    <li id="ul0001-0005" num="0105">&#x2003;In various embodiments, data may be collected and stored relating to any searches of game related data. For example, suppose a secondary player searches for all games in which a payout of more than 100 coins was won. Accordingly, data indicating the search criteria may be stored so that it may be possible to determine in the future that a secondary player searched for all games in which a payout of more than 100 coins was won. Further data describing the results of a search may be stored. For example, if the search by the secondary player yielded 1218 games, then this fact may be stored. Further identifiers for each game identified by the search may be stored.
    <ul id="ul0002" list-style="none">
        <li id="ul0002-0001" num="0106">1.1. One player places bets on a game in which another player participates. In various embodiments, a secondary player may place a bet on the outcome of a game itself. For example, a secondary player may place a bet on the outcome of a slot machine game. If the outcome &#x201c;bar-bar-bar&#x201d; occurs in the game, then the secondary player may receive ten times his bet. The secondary player need not, in various embodiments, place the same type of bet as does the primary player. For example, the primary player may initiate a craps game with a &#x201c;pass&#x201d; bet. The secondary player may bet on the same craps game, but may place a &#x201c;don't pass&#x201d; bet. Thus, though the secondary player and the primary player have placed bets on the same game, the primary player may lose and the secondary player may win.</li>
        <li id="ul0002-0002" num="0107">1.2. One player places bets on how another player will do. In various embodiments, a secondary player may place a bet on what will happen to a primary player in a game. The secondary player does not, in various embodiments, bet on the outcome of the game itself, but only on how the outcome of the game effects the primary player given the primary player's bet on the game. For example, the secondary player may bet that the primary player will win the game. If the primary player wins, then the secondary player's bet may be a winning bet and the secondary player may receive a payment. If, however, the primary player loses, then the secondary player may lose.</li>
        <li id="ul0002-0003" num="0108">&#x2003;In various embodiments, the secondary player may bet that the primary player will lose. The secondary player may thus receive a payment for a winning bet if the primary player loses, but the secondary player may lose his bet if the primary player wins.</li>
        <li id="ul0002-0004" num="0109">&#x2003;It should be noted that often, a bet placed by a primary player will provide the house or casino with an advantage. This is how the house may make money, on average. Thus, if a secondary player is permitted to place a bet against a primary player, then the secondary player may enjoy the same advantage as the house. In various embodiments, the secondary player may be charged a fee for betting against the primary player. The fee may provide the house with an advantage in a bet that might otherwise favor the secondary player. The fee may be a flat fee. The fee may be a percentage of the secondary player's bet. The fee may be taken only from payments of winnings received by the secondary player. For example, if the secondary player wins a payment of $10 based on a $10 bet placed, 50 cents may be deducted from the payment and kept by the house.</li>
        <li id="ul0002-0005" num="0110">&#x2003;In various embodiments a fee charged to the secondary player may be set at an amount which provides to the house the same advantage as the house had against the primary player. As used herein, a &#x201c;house advantage&#x201d; or &#x201c;house edge&#x201d; may be defined as a ratio of the expected amount won by a casino to the initial amount bet by a player. Suppose that a house advantage on a game is 1.41%. Thus, a primary player who bets $1 could expect to receive $0.98.59 back, on average. Further, suppose that a primary player initially bets $1 and may receive back $0 (for a net loss of $1) or may receive back $2 (for a net gain of $1). An exemplary such bet would be a $1 pass bet in the game of craps. The secondary player, in this example, may bet $1 against the primary player. The secondary player would then expect to receive back $1.01.41, on average. In order to give the house the same advantage against the secondary player that it had against the primary player, the secondary player may be charged a fee of $0.02.82. This fee may be rounded to $0.03, or may be varied over a large number of secondary player bets so as to average out to $0.02.82. With the fee taken into account, the secondary player might expect to receive $0.98.59 back per dollar bet, providing the house with the same advantage against the secondary player as it had against the primary player.</li>
        <li id="ul0002-0006" num="0111">&#x2003;In various embodiments, the secondary player may not be allowed to take exactly the opposite position as does the primary (e.g., where all wins for the primary player are losses for the secondary player, and vice versa). In various embodiments, an outcome that causes the primary player to lose may not result in a win for the secondary player, even though the secondary player has bet against the primary player. For example, an outcome of &#x201c;plum-orange-cherry&#x201d; may cause the primary player to lose, but may also cause the secondary player to lose. In various embodiments, an outcome that caused the primary player to lose may result in a push or tie for the secondary player. In this way, the house may maintain an edge against the secondary player even if the house also had an edge against the primary player. In various embodiments, the outcomes which are losing for the primary player and not winning for the secondary player may be chosen in such a way that the house is given the same advantage over the secondary player that it had over the primary player. For example, suppose that a particular game provides the primary player with the potential to either win $1 net, or lose $1 net. Suppose further that the game has a 2% house edge. Suppose further that outcomes X and Y in the game are both losing outcomes for the primary player. Outcome X occurs with probability 0.03, and outcome Y occurs with probability 0.01. With a bet of $1 against the primary player, the secondary player would ordinarily expect to win $1.02, for an average net profit of $0.02. However, in various embodiments, outcomes X and Y may also be counted as ties for the secondary player. The secondary player's expected payment is then reduced by the probability of X times the amount that would have been won (beyond the bet amount) upon the occurrence of X, plus the probability of Y times the amount that would have been won (beyond the bet amount) upon the occurrence of Y. This reduction is equal to 0.03&#xd7;$1+0.01&#xd7;$1=$0.04. The secondary player's expected winnings have thus been brought down from $1.02 to $0.98. This reduction provides the house with the same 2% edge against the secondary player as it had in the original game against the primary player.</li>
        <li id="ul0002-0007" num="0112">&#x2003;In various embodiments, the secondary player may bet against an outcome that would ordinarily be winning in a game. For example, in a game of blackjack, the secondary player may bet that the dealer will win. In various embodiments, the house may then alter the probabilities of various outcomes in the game so as to return an edge to the house. For example, if a secondary player bets on the dealer in a game of blackjack, the house may remove cards with low point values from the deck. This may reduce the probability of a dealer win, and thus may reduce the probability that the secondary player may win when betting on the dealer. In various embodiments, a game where the secondary player bets on the house may not be a game that was actually played by a primary player. Rather, the game may be a game that is or was simulated by the house with probabilities of various outcomes altered from the standard probabilities of the game.</li>
        <li id="ul0002-0008" num="0113">&#x2003;In various embodiments, a secondary player may take the house's position, or approximately the house's position, and bet against a primary player. The secondary player may thereby lose whatever the primary player wins, and win whatever the primary player loses. For example, if the primary player loses his bet of $1, then the secondary player may win $1. However, if the primary player wins $10, the secondary loses $10. In order that the house may be sure of collecting $10 from the secondary player in the event that the primary player wins $10, the house may require the secondary player to place a sufficient deposit with the house to cover possible losses of the secondary player. The deposit might come in the form of a credit balance that the secondary player has accumulated (e.g., as a result of inserting bills, or as a result of winning bets), in the form of a financial account that the house is free to charge in order to collect on the secondary player's obligations (e.g., the secondary player may provide a credit card number), in the form of a check that the secondary player has provided to the house, or in any other suitable form. In various embodiments, the house may require a deposit or other commitment from the secondary player equal to the maximum possible payout that may be received by the primary player. For example, suppose the primary player participates in a game in which the primary player may win up to $100. If the secondary player bets against the primary player, then the secondary player may risk losing up to $100 in a game. The house may thus require the secondary player to have a credit balance of as much as $100 in order to bet against the primary player. In various embodiments, the house may require the secondary player to confirm (e.g., by pressing a button) that the secondary player is aware he has the potential to lose up to X amount, where X is the maximum the secondary player might lose from participating in a game.</li>
        <li id="ul0002-0009" num="0114">&#x2003;In various embodiments, a secondary player may bet against a primary player while not mirroring the payouts of the primary player. For example, the secondary player may bet $1 on a game in which the secondary player bets that the primary player will lose. If the primary player does lose the game, the secondary player may receive $1.25, for a net profit of $0.25. If, the primary player wins, the secondary player may lose his bet of $1, for a net loss of $1. The secondary player may lose $1 regardless of the amount that the primary player wins. For example, the secondary player may lose $1 whether the primary player wins $1 or whether the primary player wins $100.</li>
        <li id="ul0002-0010" num="0115">&#x2003;In various embodiments, the secondary player may bet that a primary player will win a certain multiple of the primary player's bet in a given game. For example, the secondary player may bet $5 that the primary player will win at least triple the primary player's bet of $2 in a game. The secondary player may win $20 if the primary player wins at least $6. Otherwise, the secondary player may lose his bet of $5.</li>
        <li id="ul0002-0011" num="0116">&#x2003;In various embodiments, the secondary player may be paid according to a table or function that maps every possible result of a primary player to a payment for the secondary player. For example, the secondary player may receive $3 if the primary player wins $0, $5 if the primary player wins $1, $0 if the primary player wins $2, $0 if the primary player wins $3, $1 if the primary player wins $4, and so on. As will be understood, the function need not perform a linear or continuous mapping.</li>
        <li id="ul0002-0012" num="0117">&#x2003;In various embodiments, a secondary player may be forbidden and/or prevented from placing a bet that would provide the secondary player with an edge. For example, a secondary player may be prevented from betting against a primary player, where the house had an edge versus the primary player.</li>
        <li id="ul0002-0013" num="0118">1.3. A player places bets for games from the past. In various embodiments, a secondary player may place a bet on a game that has occurred in the past. With respect to the game, at least one of the following may have occurred in the past (e.g., before the secondary player placed a bet on the game): (a) the game's start; (b) the game's conclusion; (c) collection of a bet from the primary player who played the game; and (d) payment of winnings to the primary player who played the game.</li>
        <li id="ul0002-0014" num="0119">&#x2003;When a game is originally played, a record of the game may be created. The record may include data sufficient to recreate all or part of the game. Such data may include: (a) one or more seeds or random numbers used to generate outcomes for the game; (b) one or more outcomes of the game (e.g., &#x201c;cherry-bell-lemon&#x201d;; e.g., a sequence of five cards, such as cards constituting a poker hand; e.g., a set of hands of cards, such as a player hand and dealer hand, or such as a player hand and hands of the player's opponent; e.g., the number or numbers showing on one or more dice, such as in a game of craps; e.g., a sequence of numbers showing on a sequence of dice rolls; e.g., a set of numbers in a game of keno; e.g., the payouts achieved in a bonus round; e.g., the level achieved in a bonus round); (c) one or more symbols comprising an outcome of the game; (d) one or more cards; (e) reel positions for one or more reels of a slot machine; (f) a number of decks used; (g) a decision made by a primary player of the game; (h) one or more algorithms used to generate an outcome of the game; (i) an identifier for the gaming device used in the game; (j) a pay table used for the game; (k) a make, model, or year for the gaming device used in the game; (l) a date or time when the game was played; (m) a location where the game was played; (n) a dealer involved in the game; (o) a position of the primary player at a table used in playing the game; (p) an identifier (e.g., a name) for the primary player who played the game; (q) an identifier of another player in the game (e.g., another player at a blackjack table where the game was played); (r) a bet made by a primary player of the game; (s) winnings received by the primary player in the game; (t) video footage of the game; (u) audio footage of the game; and (v) an order of cards dealt from a deck of cards. Video footage of the game may include video footage from various perspectives. In some embodiments, video footage may show or focus on cards, dice, or reels, or other items which determine and/or reveal the outcome of a game. Video footage may include footage of actions in a game, such as footage of a player making bets, making decision, and/or collecting winnings. Such video footage may focus on a player's hands, for example. In some embodiments, video footage may show or focus on a dealer or other casino representative in charge of a game. In some embodiments, video footage may show or focus on a player's face or body. For example, video footage may show a player's facial expressions or body language during a game. In some embodiments, video footage may focus on spectators. In some embodiments, video footage is recorded from a live game. In some embodiments, video footage is generated. Video footage may be generated based on stored data about a game.</li>
        <li id="ul0002-0015" num="0120">&#x2003;Video footage may be generated in a number of ways. In some embodiments, video footage may be generated by assembling stock video clips. For example, one stock video clip may show a primary player (e.g., an actor acting as a primary player) making a bet. Another stock video clip may show a primary player rolling the dice. There may be stock video clips of every possible outcome in a game. For example, there may be a stock video clip showing the every possible roll of two dice. To assemble video footage of a complete game, the casino may e.g., put together a video clip of a bet being made, a video clip of an outcome being rolled corresponding to the outcome that actually occurred in the original game the secondary player is betting on, and a video clip of a player collecting his winnings. In some embodiments, stock video footage may include video footage of entire games. Should a similar game later occur, the same video footage may be used for the similar game when the secondary player is participating in the similar game.</li>
        <li id="ul0002-0016" num="0121">&#x2003;In some embodiments, video footage is generated using computer algorithms. For example, computer algorithms may generate footage showing a simulated primary player placing a bet and rolling dice, the dice bouncing and landing, a simulated croupier paying winnings, and so on. In various embodiments, video may be generated so as to be true, as much as practicable, to the data of the game. For example, video may be generated to show a video or animated depiction of an outcome that actually occurred in a game of a primary player.</li>
        <li id="ul0002-0017" num="0122">&#x2003;In various embodiments, video may be generated based on data about a game. Data indicating the bet amount of a primary player may be used to generate video of a primary player (e.g., a simulated primary player) making a bet of the same bet amount. Data indicating an outcome of a game may be used to generate video showing the same outcome being generated. Data indicating intermediate symbols or indicia that appear during a game may be used to generate video showing those same intermediate symbols or indicia. For example, data indicating that a particular position at a blackjack table was dealt the seven of hearts may be used to generate video showing the simulated dealing of the seven of hearts on a simulated blackjack table. Data indicating the identity of a primary player may be used to generate video. For example, based on a stored photo of a primary player, the casino may generate cartoon caricatures of the primary player playing a game. Data indicating the age or other demographic of a primary player may be used to generate video. For example, if the primary player is a 60 year-old female, the casino may generate a cartoon caricature of a 60 year-old female playing a game. In some embodiments, demographic data about a player may be used to retrieve stock footage of a player with similar characteristics. For example, stock footage of a 60 year-old female player may be retrieved.</li>
        <li id="ul0002-0018" num="0123">&#x2003;The record of the game may be stored by a gaming device, casino server, third party server, or other device. Subsequently, a secondary player may place a bet on the game, or on some aspect of the game. Once the secondary player has placed a bet, data stored in the record may be used to recreate the game, or to recreate some aspect of the game. For example, video footage of the game may be shown to the secondary player. In some embodiments, the outcome of the game may simply be displayed for the secondary player.</li>
        <li id="ul0002-0019" num="0124">&#x2003;Based on the outcome of the game, and based on the bet placed by the secondary player, the secondary player may lose his bet, lose a portion of his bet, break even, or be paid winnings. For example, if the outcome of the game is a winning outcome, then the secondary player may be paid based on the standard rules of the game. For example, if the secondary player bets $10 on a game of blackjack, and the primary player in the game received 20 points to the dealer's 19, then the secondary player may win $10 in addition to keeping his bet.</li>
        <li id="ul0002-0020" num="0125">&#x2003;If the secondary player has placed a bet on what would happen to the primary player, then the winnings and/or losses of the primary player may be revealed to the secondary player. For example, if the secondary player bet against the primary player, and the primary player lost, the secondary player may win. If the secondary player made a bet whereby the secondary player receives twice the winnings of the primary player, and the primary player wins $20, then the secondary player may receive $40</li>
        <li id="ul0002-0021" num="0126">1.4. A primary player on which a secondary player was betting is no longer available. In various embodiments, a secondary player may participate in one or more games played by a primary player. For example, the secondary player may place bets on the games played by the primary player. The primary player may, at some point, terminate his playing session. The secondary player may, on the other hand, wish to continue his participation in the games of the primary player, and may thus find himself deprived of opportunities to make bets on the games of the primary player.
        <ul id="ul0003" list-style="none">
            <li id="ul0003-0001" num="0127">1.4.1. A primary player is asked to stay. In various embodiments, the primary player may signal his intention to terminate a playing session. For example, the primary player may stand up, cash out, refrain from placing a bet even though he is at a table game, and so on. The secondary player may signal his desire to continue participating. For example, the secondary player may press a button labeled &#x201c;continue session&#x201d; on a betting interface. The secondary player may communicate his desire verbally (e.g., to a casino representative), via text (e.g., via a text message sent to a casino representative) or in any other manner. Regardless of whether the secondary player actually signals his desire to continue participating, the primary player may be contacted. For example, a representative of the casino may contact the primary player. Such a representative may include a waitress, pit boss, dealer, etc. The primary player may be asked to stay and to continue playing. The primary player may be offered a benefit for staying, such as cash, goods or services, a free meal, show tickets, improved odds, comp points, and so on. The primary player may be informed that there is a secondary player who appreciates the results of the primary player and wishes for the primary player to remain.</li>
            <li id="ul0003-0002" num="0128">&#x2003;In some embodiments, a primary player who has signaled an intent to leave may be asked to stay only if one or more criteria are satisfied. For example, the primary player may be asked to stay only if at least three secondary players have been participating in the games of the primary player. Other criteria may include: (a) there are at least X secondary players watching the games of the primary player; (b) there are at least X secondary players who are interested in participating in the games of the primary player; (c) there has been at least X dollar amount of bets placed by secondary players on each game of the primary player; (d) there has been a total of at least X dollar amount of bets placed by secondary players on games of the primary player during a particular period of time, number of games, particular playing session, etc.; (e) the casino has made at least X dollars of profit from secondary players having participated in the games of the primary player; (f) the casino has made at least X dollars of theoretical win or profits from secondary players having participated in the games of the primary player; and so on. It will be appreciated that a casino may require any combination of the above criteria to be met in order for a primary player to be asked to stay. There may be multiple ways of meeting the above criteria, including by partially satisfying two or more of the criteria. It will further be appreciated that there may be other criteria that a casino may use based on whose satisfaction the casino may ask a primary player to continue with a playing session.</li>
            <li id="ul0003-0003" num="0129">&#x2003;In various embodiments, a casino may offer a primary player an opportunity to play a fair game (i.e., where the primary player's expected winnings accounting for the cost of betting are exactly 0), if the primary player will continue to play.</li>
            <li id="ul0003-0004" num="0130">1.4.2. The casino plays automatically. In some embodiments, when a primary player terminates a playing session, the casino or house may play in place of the primary player. For example, a dealer at a blackjack table may continue to deal a hand to the position where the primary player had been. The dealer may make decisions for the hand, such as hit or stand decisions. The decisions may be made according to optimum strategy. The decisions may also be made based on inputs from the secondary player. Another representative of the casino may also stand in for the primary player. For example, the other representative may sit at the table or slot machine where the primary player had been, and may resume play.</li>
            <li id="ul0003-0005" num="0131">&#x2003;In some embodiments, game outcomes may be generated automatically once the primary player leaves. For example, a slot machine that the primary player has left may continue to generate outcomes. The secondary player may thus continue to place bets on the outcomes.</li>
            <li id="ul0003-0006" num="0132">&#x2003;In some embodiments, a computer algorithm may make decisions in a game. The computer algorithm may substitute in for a primary player in a game so that a secondary player may participate in the game without the presence of a human primary player. In some embodiments a computer algorithm may act as a primary player even when a secondary player had not been participating in games of a prior human primary player. In other words, a computer algorithm need not necessarily substitute in for a primary player, but may serve as a simulated or artificial primary player from the get go. A computer algorithm may make decisions in a game. The computer algorithm may make decisions of how much to bet; decisions of what types of bets to make (e.g., the computer algorithm may decide whether or not to make an insurance get in a game of blackjack); decisions of whether to check, bet, raise, call, or fold (e.g., in a game of poker); decisions about whether or not to receive additional cards (e.g., in games of blackjack or video poker); and any other decisions that may be made in a game. The computer algorithm may refer to a stored set of rules for making decisions in a game. For example, the computer algorithm may refer to a table which lists one or more possible situations which might arise in a game and which lists a corresponding decision that should be made should that situation arise. The computer algorithm may also include procedures, logic, or other computational methods for computing a decision given a game state. For example, in a game of video poker, a computer algorithm may compute expected winnings given each of several possible decisions. The computer may determine which of the decisions leads to the highest expected winnings and make that decision.</li>
            <li id="ul0003-0007" num="0133">&#x2003;In various embodiments, a computer algorithm may be programmed to make decisions which yield the highest expected winnings, payouts, and/or profits in a game. In various embodiments, a computer algorithm may be programmed to approximate the play of a human player. The computer algorithm may be programmed to, at least occasionally, favor strategies with emotional or intuitive appeal over those that are optimal. For example, a computer algorithm may be programmed to pursue a high paying hand in a game of video poker even when expected winnings would be optimized by pursuing a lower paying but more certain hand. In various embodiments, computer algorithms may be programmed with different personalities. Some might be programmed to take big risks in the strategies they use. Some might be programmed to play conservatively. Some computer algorithms may be programmed to bet frequently (e.g., in games of poker). Some computer algorithms may be programmed to bet infrequently, and only with very good hands (e.g., in games of poker).</li>
            <li id="ul0003-0008" num="0134">1.4.3. An interrupted session of the primary player is resumed when primary player returns. In some embodiments, when a primary player leaves, the session of the secondary player may be put on hold. That is, for the time being, the secondary player may not have the opportunity of placing bets and participating in games played by the primary player. However, the secondary player may have the opportunity to resume playing when the primary player returns and initiates new games.
            <ul id="ul0004" list-style="none">
                <li id="ul0004-0001" num="0135">*******. An alert is given to the secondary player when primary player returns. In some embodiments, the secondary player may be sent an alert when the primary player has returned, or when the primary player is soon to return, or when the primary player is likely to return. The alert may take the form of a phone call, email, text message, verbal alert by a casino representative, and so on.</li>
            </ul>
            </li>
            <li id="ul0003-0009" num="0136">1.4.4. In some embodiments, a secondary player may indicate a primary player in whose games the secondary player may be interested in participating. The secondary player may thereby &#x201c;tag&#x201d; or &#x201c;bookmark&#x201d; the primary player as a player in whose games the secondary player may wish to participate. In various embodiments, the casino may allow the secondary player to easily determine when a bookmarked primary player is playing (e.g., is seated at a gaming device or gaming table; e.g., has inserted a player tracking card at a gaming device or gaming table; e.g., has played one or more games in the recent past). For example, a secondary player may peruse a list of bookmarked primary player. The secondary player may select one of the primary players from the list and may then be shown whether or not the primary player is currently playing, what game the primary player is playing, where the primary player is playing, or any other information of interest. In some embodiments, the casino may alert the secondary player anytime a bookmarked primary player has begun playing. In some embodiments, the casino may keep track of various statistics related to primary players that the secondary player has bookmarked. The casino may report such statistics to the secondary player when the secondary player makes contact with the casino (e.g., sits at terminal from which the secondary player may participate in games of the primary player), or at any other time. Statistics may include statistics about recent games played, recent wins, recent losses, recent large payouts, recent profits, and so on. Statistics need not necessarily be recent, but may be recent if the secondary player has previously learned of older statistics about the primary player. In various embodiments, if a secondary player is ready to begin participating in the games of a primary player, the secondary player may be offered (e.g., by default) the opportunity to participate in games of a bookmarked primary player. The secondary player may be offered the opportunity to participate in the games of a first bookmarked primary player (e.g., a primary player that is first on the secondary player's list of favorite primary players). If the secondary player declines, the secondary player may be offered the opportunity to participate in games of a second bookmarked primary player (e.g., a primary player that is second on the secondary player's list of favorite primary players), and so on. In various embodiments, secondary players may share tags or bookmarks of primary players amongst themselves. For example, a secondary player may publish a list of whom he thinks are &#x201c;lucky&#x201d; primary players. Other secondary players may view the list and decide to participate in the games of the listed primary players.</li>
            <li id="ul0003-0010" num="0137">1.4.5. An expected value is paid to the secondary player. In various embodiments, a secondary player may have placed a bet on results of a primary player spanning more than one game. For example, the secondary player may have bet that a primary player would be ahead monetarily after one hour of play. If, however, the primary player leaves prior to completing one hour of play, there is the potential that the secondary player's bet remains unresolved. In various embodiments, the secondary player's bet is settled for the expected value (EV) of the secondary player's winnings. For example, if, based on the current time, the current winnings of the primary player, and the odds of the game that the primary player has been playing, the expected winnings of the secondary player are $8, then the secondary player may be paid $8 when the primary player terminates his session. The bet may also be settled for various functions of the EV, such as for the EV less a processing fee, 50% of the EV, and so on.</li>
            <li id="ul0003-0011" num="0138">1.4.6. Bets are returned to the secondary player. In some embodiments, when the primary player terminates a session, a bet made be the secondary player that was dependent on the primary player finishing the session may be returned to the secondary player.</li>
            <li id="ul0003-0012" num="0139">1.4.7. Options to participate in the games of other primary players are shown to the secondary player. In some embodiments, when the primary player terminates a session, the secondary player may be presented with other primary players on whom or on whose games the secondary player might bet. By selecting one or more of the new primary players, the secondary player may continue participating in games. For the purposes of a bet that required the completion of the session by the original primary player, the new primary player may be treated as if he was continuing where the original primary player left off. For example, the new primary player may be treated as if he has lost $6 during the past half hour, as the original primary player actually did. If the new primary player subsequently wins $10 in the next half hour, a bet made by the secondary player that the original primary player would be ahead after an hour of play would be a winning bet.</li>
            <li id="ul0003-0013" num="0140">&#x2003;When a selection of new primary players is presented to the secondary player, primary players presented may be chosen by the casino based on similarities to the original primary player. For example, suppose the original primary player was from Texas. When the original primary player terminates his session, new primary players may be presented wherein each is also from Texas. Other characteristics that the original and new primary players may share include: (a) both may play the same type game (e.g., both may play IGT's Wheel of Fortune&#xae; slot machines); (b) both may be of the same gender; (c) both may be the same age; (d) both may have the same occupation; (e) both may have the same geographic location of residence or origin; (f) both may have common interests (e.g., in music, food, sports, etc.); and (g) both may share common birthdays.</li>
            <li id="ul0003-0014" num="0141">1.4.8. The secondary player is given the opportunity to become a primary player. He's told where he can sit down and start playing. In some embodiments, when a primary player terminates his session, the secondary player is offered the chance to become a primary player. For example, the secondary player is shown the location of the slot machine or table game where the primary player had been playing. The secondary player may be offered the opportunity to take the seat and/or take the place of the primary player.</li>
            <li id="ul0003-0015" num="0142">1.4.9. Historical games of the primary player are found. In some embodiments, when the primary player terminates a session of play, the secondary player may be offered the opportunity to participate in historical games of the primary player. In various embodiments, the historical games may include games in which the secondary player has not already participated. The secondary player may thereby have the opportunity to continue benefiting from the skill, luck, or other value he associates with the primary player.</li>
        </ul>
        </li>
        <li id="ul0002-0022" num="0143">1.5. Maintenance of player privacy. In various embodiments, the identity of a primary player may be shielded from the secondary player. This may prevent a secondary player from finding out sensitive financial information about the primary player, from scolding the primary player for unfavorable outcomes, or for otherwise causing harm or discomfort to the primary player.
        <ul id="ul0005" list-style="none">
            <li id="ul0005-0001" num="0144">1.5.1. The secondary player doesn't see who he is betting on. In various embodiments, facial features or any other potentially identifying features of a primary player are hidden from the secondary player. For example, in video footage of the game of the primary player, the face is blurred, covered, or completely omitted from the field of view. Voices may be edited out or masked.</li>
            <li id="ul0005-0002" num="0145">1.5.2. The secondary player does not know the location of the person he is betting on. In various embodiments, the location of the primary player is disguised or kept hidden. Otherwise, especially for a live game, it would be conceivable that the secondary player could find the primary player by simply going to the location of the primary player. Thus, in various embodiments, video footage of the game of the primary player may omit distinguishing characteristics of the primary player's location. Such characteristics may include identifiable features of a casino, such as pictures, sculptures, fountains, names of restaurants, signs for a bathroom, signs for a poker room or other casino sector, and so on. Distinguishing features of a table game may also be disguised or omitted. For example, a unique design or color of a table may be omitted. In various embodiments, games or locations with readily identifiable and/or unique characteristics may be ineligible for participation by secondary players.</li>
            <li id="ul0005-0003" num="0146">1.5.3. Limits to how many times a secondary player can bet on one particular person. In various embodiments, there may be a limit as to the number of games of a primary player in which a secondary player may participate. This may lessen the likelihood of the secondary player developing any strong feelings towards the primary player one way or the other. In various embodiments, there is a limit to the amount of time that the secondary player is allowed to spend participating in the games of a given primary player.</li>
            <li id="ul0005-0004" num="0147">&#x2003;In various embodiments, a secondary player may be switched from participating in the games of a first primary player to participating in the games of a second primary player. The secondary player may be switched without the secondary player knowing that he has been switched. For example, the secondary player may receive data about a game that includes the symbols, indicia, and/or outcomes generated during the game. However, the secondary player may not necessarily receive identifying information about a primary player of the game. Thus, when the secondary player is switched from participating in the games of a first primary player to participating in the games of a second primary player, the secondary player may not be aware of the switch since the secondary player may have no access to identifying information for either the first or second primary players. In various embodiments, the secondary player may be switched form participating in the games of a first primary player to participating in the games of a second primary player after a predetermined number of games. For example, after participating in 25 games of a first primary player, the secondary player may be switched to participating in the games of a second primary player. In various embodiments, a switch may occur at random. For example, after every game played by a first primary player, the casino may randomly generate a number between 1 and 100. If the number is greater than 80, the casino may switch the secondary player from participating in the games of the first primary player to participating in the games of a second primary player. In some embodiments, the switch may occur after a random number of games with an upper boundary. For example, if the secondary player has not been switched after 20 games with a first primary player, the secondary player may be switched automatically. In some embodiments, a secondary player may be switched upon his own request. In various embodiments, when a secondary player is switched between the games of different primary players with reasonable frequency, the chances with which a primary player's privacy becomes compromised may be reduced. In some embodiments, a secondary player may be informed when he has been switched from the games of a first primary player to the games of a second primary player. In some embodiments, the secondary player is not informed of the switch.</li>
            <li id="ul0005-0005" num="0148">1.5.4. Introduction of a time delay so that the primary player is no longer located where he had been by the time the secondary player begins participation in the games of the primary player. In various embodiments, a secondary player is restricted to betting on games that have occurred a predetermined amount of time in the past, e.g., one day or more in the past. In this way, the secondary player is unlikely to be able to contact the primary player, as the primary player may no longer be in the vicinity. In various embodiments, the secondary player is restricted to betting on games that have been played by a primary player who has already left the location in which the games were originally played.</li>
        </ul>
        </li>
        <li id="ul0002-0023" num="0149">1.6. A secondary player or spectator is provided with knowledge about what the next cards will be, or what the primary player's opponent holds. The secondary player may watch the primary player struggle with a decision while the secondary player already knows the correct decision. In various embodiments, a secondary player may be informed of some information about a game that the primary player does not know, or at least did not know at the time the primary player was participating in the game. For example, a primary player may be engaged in a game of video poker. The secondary player may watch the progress of the game from a remote terminal. The secondary player may be informed that the next four cards in the deck are all aces. However, this information is not known to the primary player. Thus, the secondary player may experience the excitement of hoping the primary player will draw four cards.
        <ul id="ul0006" list-style="none">
            <li id="ul0006-0001" num="0150">1.6.1. The secondary player knows the next cards, the symbols that will occur on reels, the proper door to open in a bonus game, etc. In various embodiments, a secondary player may be informed of one or more of the following at a point in a game prior to when a primary player finds out (or found out): (a) an outcome of a game (e.g., &#x201c;cherry-cherry-cherry&#x201d;); (b) a payment that the primary player will receive based on the game; (c) a game result (e.g., win, lose); (d) a reel position; (e) a symbol that will appear on a reel (e.g., the secondary player may know that the third reel of a slot machine will show a symbol &#x201c;bar&#x201d; that will complete a winning outcome of &#x201c;bar-bar-bar&#x201d; prior to when the primary player finds out); (f) a card that will be received by the primary player; (g) a card that will be received by a dealer; (h) a card that is at or near the top of the deck being used in a game of cards; (i) a hand of cards that will be achieved by a primary player should the primary player make a particular decision (e.g., a hit decision in blackjack); (j) an order of cards in a deck of cards (k) a payment, result, or outcome that would result from a particular choice in a bonus game of a gaming device (e.g., the primary player would win 200 coins by choosing door number 3 in a bonus game); (l) a card that will be received by the primary player's opponent; (m) a card held by the primary player's opponent (e.g., in a poker hand); (n) a number that will appear on a die in a game (e.g., in craps); (o) a number that will come up in the game of roulette; and so on.</li>
            <li id="ul0006-0002" num="0151">1.6.2. The secondary player may make a new bet at apparently good odds if the primary player is not likely to make a decision that would win for the secondary player. In various embodiments, a secondary player may be allowed to place a bet on a game being played by the primary player after finding out information about the game. The bet may be made at odds apparently favorable to the primary player. For example, suppose that a primary player holds an initial hand of video poker comprising the Ks, Kc, 10h, 3c and 7d. Unbeknownst to the primary player, but known to the secondary player, the next four cards in the deck are the Ah, Kh, Qh, and Jh. Thus, were the primary player to discard the Ks, Kc, 3c, and 7d, the primary player would achieve a royal flush, the highest paying outcome, in various embodiments. The secondary player may be allowed to bet four coins on the game. The secondary player may win 1 coin for a pair, jacks or better, 2 coins for two-pair, 3 coins for three-of-a-kind, and 800 for a royal flush. Thus, the secondary player may bet 4 coins with an apparent potential to win 800 coins. Indeed, it is possible that the second player will win 800 coins. However, it would be very unlikely for the primary player to discard a pair of kings in order to draw four cards to the 10h. Thus, it is more likely the primary player will keep his pair of kings, draw three cards, and end up with three kings, providing the secondary player with a payout of 3 coins. Thus, in various embodiments, the strategy of a primary player may be predicted, e.g., by the casino server. The predicted strategy may be, e.g., an optimal strategy given lack of any knowledge about future results or outcomes (e.g., future cards in a deck). Based on predictions of the primary player's strategy, the casino server may provide betting opportunities for the secondary player such that the house will maintain an advantage given the predicted strategies. The same betting opportunities provided to the secondary player may have provided the house with a disadvantage if the primary player were to be able to utilize knowledge of future results or outcomes (e.g., future cards in a deck). Accordingly, a secondary player may make certain bets on a game in the hopes that the primary player will deviate from optimal or conventional strategy.</li>
            <li id="ul0006-0003" num="0152">1.6.3. The secondary player may provide hints. In various embodiments, a secondary player may have the opportunity to convey a hint to the primary player. A hint may take the form of a suggested decision. For example, a hint may indicate that the primary player should discard the first and third cards in his hand of video poker. A hint may take the form of a veto. For example, the primary player may first indicate a particular choice of strategy, such as a particular combination of cards to discard in a game of video poker. The secondary player may provide an indication that such a strategy should not be followed. The secondary player may be allowed only one veto, or may be allowed up to a predetermined number of vetoes. A hint may take the form of information about a symbol, result, or outcome of a game. For example, in the bonus round of a slot machine game, the secondary player may inform the primary player of the number of coins behind door <b>2</b>. It may happen that there are more coins behind door <b>3</b>, but the secondary player may only be allowed to give a hint about door <b>2</b>, in some embodiments.</li>
            <li id="ul0006-0004" num="0153">1.6.4. The secondary player may watch the primary player for entertainment purposes. The secondary player may watch facial expressions during good outcomes or during near-misses. In various embodiments, the secondary player may derive entertainment or other gratification from watching the experiences of the primary player. The secondary player may, for instance, watch a primary player play a game in which the primary player will win a large payout. The secondary player can watch the expression on the face of the primary player (e.g., from video footage) and see the expression change from neutral to an expression of surprise and elation. The secondary player may choose to participate in games that are likely to have or to have had an emotional impact on the primary player. The secondary player may thus choose games in which a payment above a predetermined amount was won, in which a certain outcome (e.g., a winning outcome) was achieved, in which a jackpot was achieved, in which a bonus round was played, and so on. A secondary player may also choose a game in which the primary player comes close, or apparently comes close to achieving a large payment. For example, the secondary player may choose a game in which the primary player has four cards to a royal flush in video poker, and will draw a fifth card. The secondary player may also choose a game in which two out of three reels of a slot machine line up on jackpot symbols.</li>
            <li id="ul0006-0005" num="0154">1.6.5. A search is performed to find games that include near misses of high paying outcomes, or any other characteristic. In various embodiments, a secondary player may receive information about various games that will happen, are in progress, or have happened already. Based on the information, the secondary player may choose a game in which to participate, or which to watch. The secondary player may have a preferred game he likes to play, a preferred primary player he likes to bet with (or on), a preferred dealer in whose game he wishes to participate, and so on. The secondary player may also wish to participate in games where he knows something about the outcome, results, or other information about the game. For example, the secondary player may wish to participate in games where the first two reels of a slot machine show the jackpot symbols.</li>
            <li id="ul0006-0006" num="0155">&#x2003;In various embodiments, the secondary player may indicate a desired criterion, or desired criteria about the game. Various games satisfying the criterion or criteria may then be made available for the secondary player to participate in. The secondary player may then choose one or more of the games to participate in. In various embodiments, once the secondary player has indicated a criterion or criteria, the secondary player may automatically begin participating in a game matching the criterion or criteria. Criteria indicated for a game by a secondary player may include one or more of the following: (a) the game has a particular dealer; (b) the game has a particular number of players; (c) the game is played at a particular gaming device; (d) the game is played at a particular type of gaming device; (e) the game is played by a particular primary player; (f) the game is played by a primary player with a particular characteristic (e.g., age, race, marital status, nationality, area of residence, occupation, etc.); (g) the game has a potential payout above a particular level (e.g., the game has a payout of more than 1000 times the bet); (h) the game has an expected payout above a certain level (e.g., an expected payout of more than 95% of the original bet); (i) the game has a bonus round; (j) the game is played in a certain location; (k) the game is played at a certain time or date; (l) the game is, or will be a winning game (e.g., the game will pay at least three times an initial bet of the primary player); (m) the game will feature an outcome that has almost all the required symbols necessary for a large payout (e.g., a game of video poker has four cards to a royal flush); and so on.</li>
            <li id="ul0006-0007" num="0156">1.6.6. Preventing collaboration. In various embodiments, measures may be taken to prevent collaboration between the primary player and the secondary player. Particularly if the secondary player knows information about the game, such as hidden cards in a deck, the secondary player would be able to confer an advantage to the primary player and to himself by communicating with the primary player. As discussed previously, the identity of the primary player may be shielded from the secondary player. Similarly, the identity of the secondary player may be shielded from the primary player. One or both of the primary and secondary players may be kept in an enclosure, such as a sound-proof room or Faraday cage, that reduces the possibility of communication. Signal detectors, such as antennas, may be placed near the primary or secondary players to detect possible communications between the two. Cell phones, pagers, Blackberries&#x2122; and other communication devices may be temporarily confiscated from either or both of the primary and secondary players. The secondary player may participate in the game only after one or more, including all game decisions have been made in the game.</li>
        </ul>
        </li>
        <li id="ul0002-0024" num="0157">1.7. What happens if a machine needs servicing in the middle of a role? What happens if the primary player is taking too long to finish a game? In various embodiments, the completion of a game may be delayed or prevented. For example, a gaming device may break down in the middle of a game. A primary player may get into a discussion with a friend in the middle of a video poker game, and may thus delay a decision in the game for several minutes. A secondary player participating in a delayed game may find the delay frustrating and may wish to complete the game in some other manner.
        <ul id="ul0007" list-style="none">
            <li id="ul0007-0001" num="0158">1.7.1. A game is completed automatically. In various embodiments, the game may be completed automatically, e.g., by the casino. The game that is completed automatically may, in fact, be a copy of the original game, so that the primary player can complete the original game on his own. However, the secondary player may receive a payment based on the automatically completed game. The game may be completed using a predetermined strategy, such as optimal strategy. The game may be completed using a random strategy where, for example, one of several possible strategies is selected at random.</li>
            <li id="ul0007-0002" num="0159">1.7.2. The secondary player makes the decisions in a game. In some embodiments, the secondary player may have the opportunity to complete the game by making his own decisions. For example, if the game is blackjack, the secondary player may indicate decisions such as &#x201c;hit&#x201d; or &#x201c;stand&#x201d; so as to complete the game. The secondary player may, in various embodiments, complete a copy of the original game, so that the primary player may complete the original game on his own. A copy of the original game may include a second game with one or more similar parameters or aspects to the first game. For example, in the copied version of the game, one or more of the player hand, the dealer's hand, the order of cards in a deck, the prizes available behind certain doors in a bonus game, etc., may be the same as in the original game.</li>
            <li id="ul0007-0003" num="0160">1.7.3. A bet is returned to the secondary player. In various embodiments, when a game is delayed, the bet placed by the secondary player on the game may be returned to the secondary player.</li>
            <li id="ul0007-0004" num="0161">1.7.4. The secondary player is provided with an expected value of his winnings at that point in the game. In various embodiments, when a game is delayed, the expected payment or the expected winnings to be paid the secondary player may be provided to the secondary player. In some embodiments, a function of the expected payment is provided, such as the expected payment less a fee.</li>
        </ul>
        </li>
        <li id="ul0002-0025" num="0162">1.8. Communication between the secondary player and the primary player. In some embodiments, the primary player and the secondary player may be given the opportunity to communicate. Communication may occur via text, voice, or any other means. Communication may occur through the casino server. Communication may be monitored by the casino, such as by a computer program or a casino representative. Communication may be edited or prevented if there is inappropriate or threatening language and/or if communication somehow provides either the primary player or secondary player with an unfair advantage.
        <ul id="ul0008" list-style="none">
            <li id="ul0008-0001" num="0163">1.8.1. The secondary player sends help to the primary player. For example, &#x201c;you should hit here&#x201d;. In some embodiments, the secondary player may send help to the primary player. The secondary player may help the primary player with strategy in a game such as blackjack, video poker, or live poker. In video poker, the secondary player may suggest which cards the primary player should discard. In blackjack, the secondary player may suggest whether to hit, stand, double down, split, etc. In a live game of poker, the secondary player may advise the primary player whether to check, bet, raise, fold, or call. The secondary player may also suggest an amount of a bet or raise. The secondary player may provide other suggestions or opinions, such as suggesting that another player is probably bluffing. The secondary player may provide additional information, such as the probabilities of various events occurring given a particular strategy. For example, the secondary player may indicate that the primary player would have roughly 2 to 1 odds against making a flush should he continue in a game of poker.</li>
            <li id="ul0008-0002" num="0164">1.8.2. The secondary player takes over the game. In various embodiments, a secondary player may take the place of a primary player in making decisions in a game. For example, the secondary player may transmit signals that cause game decisions to be made without additional input by the primary player. For example, the primary player may press a button on a gaming device labeled &#x201c;defer to secondary player&#x201d;. The secondary player may then select, e.g., cards to discard from a remote terminal. The remote terminal may, in turn, transmit to the gaming device indications of which cards the secondary player has chosen to discard. The chosen cards may then be removed from the primary player's hand and replaced with new cards. The primary player may win or lose, and may receive payments based on the decisions made by the secondary player.</li>
            <li id="ul0008-0003" num="0165">1.8.3. Sending a tip to the primary player. In various embodiments, the secondary player may send a tip, other consideration, or other token of gratitude to the primary player. For example, if the primary player has just won a large payment, thereby causing the secondary player also to win a large payment, the secondary player may be grateful and wish to tip the primary player. The secondary player may provide an indication that he wishes to tip the primary player, e.g., by pressing a button on a remote terminal. The casino server may then deduct the amount of the tip from an account associated with the secondary player, and add such amount to an account associated with the primary player. The casino server may also cause the amount of the tip to be paid out at the primary player's gaming device or table, e.g., in the form of a coin or cashless gaming receipt. In some embodiments, the primary player may pay to have something delivered to the primary player. For example, the secondary player may pay for a bottle of wine. A casino representative, such as a waitress, may then deliver the bottle of wine to the primary player at the location of the primary player.</li>
        </ul>
        </li>
        <li id="ul0002-0026" num="0166">1.9. Betting interfaces. A secondary player may participate in the game of a primary player using various interfaces. The interfaces may allow the secondary player to select a game in which to participate, including selecting various aspects of a game, such as the machine on which the game is played, the primary player playing the game, the time, and so on. The interface may allow the secondary player to select a bet type. For example, the secondary player can bet for a primary player to win, or for a primary player to lose. The interface may allow the secondary player to select a bet amount. The interface may allow the secondary player to insert cash or other consideration, to identify himself (e.g., for the purposes of receiving comp points), and to cash out winnings or remaining balances.
        <ul id="ul0009" list-style="none">
            <li id="ul0009-0001" num="0167">1.9.1. Internet. A secondary player may participate using a network, such as the internet or a casino intranet. The secondary player may employ a computer, such as a personal computer, for this purpose. The secondary player may view a selection of games to participate in, progress of a current game, credit balances, etc., using a computer monitor. The secondary player may input decisions using a mouse, computer keyboard, or any other computer input device. For example, the secondary player may key in a bet amount using a numeric keypad on a computer keyboard. The secondary player may also use a device such as a phone, a cell phone, personal digital assistant, or Blackberry&#x2122;. The contents of the following United States patent applications, listed with serial numbers, titles, and matter numbers in parenthesis, are incorporated by reference herein for all purposes: (a) Ser. No. 10/835,995 System and Method for Convenience Gaming (075234.0121); (b) Ser. No. 11/063,311 System and Method for Convenience Gaming (075234.0136); (c) Ser. No. 11/199,835 System and Method for Wireless Gaming System with User Profiles (075234.0173); (d) Ser. No. 11/199,831 System for Wireless Gaming System with Alerts (075234.0174); (e) Ser. No. 11/201,812 System and Method for Wireless Gaming with Location Determination (075234.0176); (f) Ser. No. 11/199,964 System and Method for Providing Wireless Gaming as a Service Application (075234.0177); (g) Ser. No. 11/256,568 System and Method for Wireless Lottery (075234.0178); (h) Ser. No. 11/210,482 System and Method for Peer-to-Peer Wireless Gaming (075234.0179); (i) Ser. No. 60/697,861 Enhanced Wireless Gaming System (075234.0183). The device used by the secondary player for participating in games may communicate with a casino server via the network, as is commonly known in the art. Messages may be exchanged back and forth between a device used by the secondary player and the casino, the messages taking the form of streams of bits represented by electronic pulses, optical pulses, or any other practical representation.</li>
            <li id="ul0009-0002" num="0168">1.9.2. Felt table with live dealer. In various embodiments a secondary player may participate in a game by sitting at a table and interacting with a casino representative. The table at which the secondary player sits may be different from the table the primary player sits at. Thus the game activities of the primary player may occur elsewhere from the location of the secondary player. However, the secondary player may store cash or chips at his table, and may indicate bets by placing chips at certain parts of the table. From this table, the secondary player may watch the action in the game of the primary player, e.g., using closed circuit television. Based on the outcome of the game played by the primary player, the secondary player may receive payments at his table. Thus, for example, the casino representative at the table of the secondary player may collect bets from the secondary player, and may pay winnings to the secondary player if the outcome of the game of the primary player is winning for the primary player. The table of the secondary player may appear similar to that of the primary player. For example, the table may have the same shape and surface markings. The secondary player may even sit at the same position with respect to his table as the primary player sits with respect to the primary player's table. The secondary player may enjoy a similar experience to that of the primary player, only, perhaps, without the cards, dice, or other game apparatus used at the table of the primary player. In various embodiments, the table of the secondary player may serve as a means for the secondary player to make bets, receive winnings, and possibly to view the game of the primary player.</li>
            <li id="ul0009-0003" num="0169">&#x2003;In some embodiments, the secondary player uses the same table or gaming device as does the primary player. For example, the secondary player may place a bet beside the hand of the primary player. The secondary player may then receive payments based on the outcome of the game of the primary player.</li>
            <li id="ul0009-0004" num="0170">1.9.3. Machine at the casino. In some embodiments, a secondary player may participate in a game using a machine or terminal configured to allow participation in a separate game. The terminal may include a coin slot, bill validator, credit card reader, and/or other means for accepting consideration. The terminal may include buttons, keys, roller balls, and/or other input devices that may be used by the secondary player for selecting a game in which to participate, for selecting bet amounts, for selecting bet types, and so on. The terminal may be in communication with the device that conducts the actual game. For example, the terminal of the secondary player may be in communication with a gaming device at which the primary player is playing. The terminal may thus receive from the device of the primary player an indication of games played by the primary player, amounts bet, outcomes received, and other pertinent information. The terminal of the secondary player may be in direct communication with the device of the primary player, or may be in communication with the casino server which, in turn, communicates with the device of the primary player. The terminal of the secondary player may also be in communication with sensors, detectors, and/or other monitoring devices at a game played by the primary player, such as at a blackjack game. For example, the terminal of the secondary player may receive feeds from cameras located at a blackjack game being played by the primary player. In various embodiments, a dealer or other casino representative may report information about a game of the primary player. For example, a dealer may input into keypad connected to the casino server that a primary player has been dealt an ace and a ten in a game of blackjack. Such information may subsequently be received at the terminal of the secondary player, and may be used in determining a payment for the secondary player. The terminal of the secondary player may be a mobile device, e.g., a mobile device as set forth in Nevada bill AB471.</li>
            <li id="ul0009-0005" num="0171">&#x2003;In some embodiments, the terminal of the secondary player may be constructed or configured to look like a gaming device. Betting interfaces at the terminal may be designed to mimic or appear similar to those at the gaming device. Graphics shown on the housing or the screen may also be similar. However, the terminal may simply recreate and redisplay games and outcomes generated by the gaming device. The terminal may not, in various embodiments, generate games or outcomes of its own, e.g., using its own processor or locally stored algorithms. In various embodiments, the terminal may comprise a kiosk.</li>
            <li id="ul0009-0006" num="0172">1.9.4. Casino desk. In various embodiments, a secondary player may visit a casino desk, casino cage, or other casino venue where bets may be placed in person. The secondary player may there select a game in which to participate. The secondary player may place a bet. The secondary player may receive some record of his bet. The record may be a paper receipt, for example. The record may include the name of the secondary player, the name of the primary player, the type of game, the time of the game, the machine or location at which the game was played, the amount of the bet, the terms of the bet (e.g., what outcomes constitute winning outcomes), and any other pertinent information. Upon resolution of the game, the secondary player may return to the desk and receive payment of any winnings.</li>
            <li id="ul0009-0007" num="0173">1.9.5. How bets are entered. In various embodiments bet amounts and bet selections may be entered using buttons, keyboards, microphones, computer mice, joysticks, or any other input devices. A secondary player may also place bets and indicate bet amounts according to rules. Rules may include instructions that may be followed by a computer algorithm, the instructions indicating rules or conditions specifying when and how much to bet. By betting according to rules, the secondary player may save himself the effort of repeatedly indicating a desire to place a bet. Rules may include the following: (a) continue betting $1 on each new game until the secondary player provides an indication to stop; (b) continue betting $1 on each new game for the next 20 games; (c) bet $1 on the game following every win, and double the prior bet following every loss; (d) continue betting until a credit balance reaches either 0 or $100; and so on. In some embodiments, rules may be entered explicitly by the secondary player. In some embodiments, different sets of rules may be predefined. A secondary player need then only select one of the predefined sets of rules to have betting done automatically on his behalf according to the selected set of rules. In some embodiments, a set of rules indicates that the prior bet should be repeated. A secondary player may simply need to confirm each new bet before it is made. For example, for a first game, a secondary player may bet 5 coins on each of 7 pay lines of a slot machine game. For a second game, the secondary player may simply press a &#x201c;repeat prior bet&#x201d; button in order to once again bet 5 coins on each of 7 pay lines. Without pressing such a button, the process of entering the bet again might be time consuming. Further, the primary player may have continued on with the next game before the secondary player had time to enter the bet a second time. In various embodiments, a secondary player may specify a bet with reference to a prior bet. For example, the secondary player may indicate a desire to bet twice his prior bet, or to make the same bet he made two games ago.
            <ul id="ul0010" list-style="none">
                <li id="ul0010-0001" num="0174">1.9.5.1. Layout of the betting screen and the graphical user interface. In various embodiments a secondary player may choose a bet type; choose a bet amount; follow the progress of a game; follow the progress of a primary player; view statistics related to a gaming device, table, dealer, primary player, casino, etc.; all using a betting interface on a display screen. The display screen may also function as a touch screen so that the secondary player may interact with the screen by touching it in certain locations. A first location of the screen may include a selection area. Shown in the selection area may be any number of attributes pertaining to a game. For example, a selection area may list a number of primary players. The secondary player may select one of the primary players to indicate that the secondary player would like to participate in the game of the selected primary player. The selection area may present a selection of: (a) primary players; (b) gaming devices; (c) times; (d) dates; (e) casinos; (f) game types (e.g., video poker, slot, etc); (g) dealers; (h) opponents; (i) game results (e.g., ranges of payouts provided by the game, such as games which paid 0-2 coins, games which paid 3-4 coins, games which paid 5-6 coins, etc); and so on. Possible selections may be presented as a menu, a list, a scroll bar, or any other presentation. The secondary player may go through various layers of selection until he has completely specified a game in which to participate. For example, the secondary player may first select a primary player, then a gaming device, then a time of a game. Each set of choices may be presented as a new menu.</li>
                <li id="ul0010-0002" num="0175">&#x2003;A second location of the screen may include a betting area. In the betting area, the secondary player may indicate an amount to bet on a game. The secondary player may specify a number of outcomes to bet on, such as a number of pay lines to bet on, or a number of hands of video poker on which to bet. The secondary player may also specify an amount to bet on each pay line or each outcome. If different types of bets may be made (e.g., a main bet and an insurance bet in blackjack, or pass line and hard eight in craps), then the secondary player may specify which of such bets he wishes to make. A secondary player may specify bets to be made on the primary player. For example, the secondary player may specify a bet that the primary player will lose or will win, or may specify a bet that the primary player will win more than a certain amount.</li>
                <li id="ul0010-0003" num="0176">&#x2003;A third location of the screen may include an area where information about a game is displayed. The area may allow the secondary player to follow the progress of the game. In this area, the secondary may watch as new symbols (e.g., cards in a card game or symbols on slot reels) arise, as new bets are made by the primary player and/or his opponent(s), as decisions are made by the primary player, as decisions are made by the dealer, as hidden symbols are revealed (e.g., as a dealer's down card is turned face up in the game of blackjack), as bets are collected (e.g., from the primary player), and as winnings are paid out (e.g., to the primary player). The third location of the screen may include live video, animations depicting a reenactment of the game, pre-recorded video of the game, pre-recorded video depicting a game similar to the game in which the secondary player is participating, or any other video depiction. The third location may include text descriptions of events in the game. For example, a text description may read, &#x201c;Joe Smith has just been dealt a pair of kings.&#x201d;</li>
                <li id="ul0010-0004" num="0177">&#x2003;A fourth location of the screen may allow a secondary player to view statistics related to a gaming device, table, dealer, primary player, casino, etc. For example, the fourth location may show the number of times a primary player has won or lost in his last 100 games, a graph depicting the bankroll of the primary player over the last two hours, the number of times a particular gaming device has paid more than 20 coins in the last day, and so on. Statistics may be presented in any conceivable form, such as using tables, graphs, bar graphs, line graphs, pie charts, and so on.</li>
                <li id="ul0010-0005" num="0178">&#x2003;A fifth location of the screen may allow a secondary player to communicate with the primary player, with a casino representative, with other secondary players, or with others. The fifth location may comprise a chat area, for example, where text conversations are tracked, and where different statements are labeled with the name of the originator of the statement.</li>
                <li id="ul0010-0006" num="0179">&#x2003;A sixth location of the screen may allow the secondary player to follow his own progress. For example, the secondary player may see his account balance and statistics about his own wins or losses.</li>
                <li id="ul0010-0007" num="0180">&#x2003;A seventh location of the screen may allow the secondary player to cash out a portion of his winnings and/or account balances.</li>
                <li id="ul0010-0008" num="0181">&#x2003;An eighth location of the screen may allow the secondary player to summon a casino representative, e.g., to order food.</li>
                <li id="ul0010-0009" num="0182">&#x2003;As will be appreciated, the locations described above may be overlapping. All locations need not have the same function at once, but may alternate. For example, at a first point in time, the screen may be occupied completely with video footage of a game. When the game finishes, the video footage may be replaced with statistics about the player. It will be further appreciated that there may be additional locations on the screen.</li>
            </ul>
            </li>
            <li id="ul0009-0008" num="0183">1.9.6. In order to participate in the games of a primary player, a secondary player may provide identifying information about himself. Identifying information may include a name, age, state of residence, nationality, driver's license number, social security number, and/or any other identifying information. The casino may use such identifying information in order to verify that the secondary player is authorized to place bets and/or to participate in games as a secondary player. For example, the casino may use identifying information to verify that a secondary player is over 21 years of age. The casino may only permit the secondary player to participate in games of the primary player if the secondary player is over 21 years of age. In various embodiments, a secondary player may be identified automatically by the casino. For example, the secondary player may seek to participate in a game while situated at a remote terminal or device. The remote terminal or device may be configured to check the identity of the secondary player prior to communicating with the casino. The terminal or device may only communicate with the casino, in some embodiments, if the secondary player is a particular player. Thus, the casino may automatically identify a secondary player by virtue of the terminal or device at which the secondary player is situated. If a terminal or device is configured only to communicate with the casino when a particular secondary player has identified himself to the terminal or device, then the casino can be assured that a particular secondary player is desirous of participating in games. The particular secondary player may be, for example, a particular secondary player that is authorized to participate in games. In some embodiments, a remote device or terminal may constitute a mobile device (e.g., a mobile device as set forth in Nevada bill AB471). The mobile device may be programmed to be used only by a particular secondary player. Therefore, if the secondary player is authorized to make bets, and the mobile device is configured to communicate with the casino only when the particular secondary player is using it, then the casino may assume that it is an authorized secondary player that is placing bets through the mobile device.</li>
        </ul>
        </li>
        <li id="ul0002-0027" num="0184">1.10. The secondary player bets on outcomes on which the primary player did not. In various embodiments, a secondary player may place bets on results or outcomes that were not bet on by the primary player. As will be appreciated, for a given game, there can be many possible outcomes, and many types of bets placed on the various outcomes. For example, in craps, many different bets can be placed in the same game, among them pass and don't pass.
        <ul id="ul0011" list-style="none">
            <li id="ul0011-0001" num="0185">1.10.1. The secondary player bets on a pay-line that the primary player did not. In various embodiments, the secondary player may bet on a pay-line of a slot machine that was not bet on by the primary player. For example, a slot machine may include three pay-lines, e.g., lines 1, 2, and 3. The primary player may bet on pay-line 1. The secondary player may bet on pay-line 2 and/or pay-line 3. The secondary player may, in various embodiments, bet on pay-line 1 as well. In some embodiments, the secondary player is only allowed to bet on pay-lines that the primary player has not already bet on. Such embodiments may help prevent a secondary player from determining a game in which the primary player has achieved a winning pay-line, and then betting on the same pay-line. In some embodiments, a secondary player may bet on pay-lines that were not available to the primary player when he played. For example, the secondary player may bet on a custom pay-line consisting of the top two symbols on a first reel, and the bottom symbol on a second reel of a slot machine. In some embodiments, the secondary player may bet on a pay-line that was not even visible to the primary player during his play of the game. For example, a slot machine may only show one symbol on each reel in a viewing window. The symbol on each reel that is one position above the viewing window may not be visible. Nevertheless, the secondary player may have the opportunity to bet on a pay-line comprising the row of symbols one position above the viewing window. Similarly, the secondary player may bet on a pay-line comprising the row of symbols one position below the viewing window. In various embodiments, any other pay-line or outcome may be constructed using visible and non-visible symbols. For example, a pay-line may be constructed using some symbols that were visible, and some symbols that were not visible to the primary player.</li>
            <li id="ul0011-0002" num="0186">1.10.2. In various embodiments, the secondary player may place bets on symbols that were never even shown to the primary player. Such symbols may have occurred, for example, well above the viewing window. In some embodiments, such symbols may be shown to the secondary player.</li>
            <li id="ul0011-0003" num="0187">1.10.3. Play a card game with unused cards. For example, in video poker, only the top 10 cards may be used during a game. The secondary player could play another game using cards from the bottom of the deck. In various embodiments, a secondary player may play a game using cards, symbols, or other indicia that were not revealed to the primary player. For example, a primary player may participate in a game of video poker. The primary player may use the top nine cards from a shuffled deck during the game (e.g., the primary player receives an initial deal of five cards, and subsequently draws four additional cards). However, in a standard 52-card deck, 43 cards would remain in the deck. The secondary player may play a new game using the 43 remaining cards. The secondary player may thus engage in a game for which no person yet knows the outcome. This may help to avoid situations where a secondary player can choose to participate in a game where he knows the outcome will be favorable to him. In various embodiments, a secondary player may participate in a new game using cards remaining after a game of blackjack, after a game of poker, after a game of casino war, or after any other game. In various embodiments, the secondary player may make his own decisions in the game, e.g., rather than relying upon decisions of the primary player. In various embodiments, a secondary player may use cards remaining in a deck for a game other than the game for which the deck was first used. For example, after a deck is used for a video poker game of the primary player, the secondary player may use the remaining cards in the deck for a game of blackjack.</li>
            <li id="ul0011-0004" num="0188">1.10.4. The secondary player bets on some function of the data from a game. In some embodiments, a secondary player may bet on some function or transformation of the outcomes, results, or other data used in a game played by a primary player. As used herein, the term &#x201c;function&#x201d; may refer to a process or procedure for relating any acceptable input to an output, such that there is only one output per unique input. The output and input may be numerical or non-numerical. As used herein, a &#x201c;function of&#x201d; an input may refer to the resultant output when the function is used to relate the input to the output. As used herein, the term &#x201c;transformation&#x201d; may refer to a process or procedure for relating any acceptable input to an output.
            <ul id="ul0012" list-style="none">
                <li id="ul0012-0001" num="0189">1.10.4.1. An outcome is generated using a function of a random number used in generating an outcome in the primary game. Suppose a random number 10232 was used to generate an outcome in a game of a primary player. The random number +1 could be used, such that the number 10233 is used. This could yield a completely different outcome. Various games played at a casino utilize random number generators. For example, a slot machine may utilize a random number generator to choose a random number for each reel of the slot machine. Each random number is then used to determine the symbol that should be revealed by the corresponding reel. In various embodiments, a game played by a secondary player may use a new set of random numbers generated based on some function of the random numbers used in a game played by the primary player. For example, the random numbers used in the game played by the secondary player may consist of the random numbers used in the game played by the primary player with one added to each. Thus, {10245, 31189, 19320} may be transformed to {10246, 31190, 19321}. The new set of random numbers may be used as inputs to an algorithm (e.g., the same algorithm used in the game played by the primary player), to generate the symbols or outcomes of the game played by the secondary player. As will be appreciated, any function of the random numbers in the primary player's game may be used to come up with random numbers in the secondary player's game. For example, one may be subtracted from each random number, the order of the random numbers may be changed (e.g., so each random number now corresponds to different one of the reels), each random number may be multiplied by a factor, and so on.</li>
                <li id="ul0012-0002" num="0190">&#x2003;In various embodiments, seed numbers may be used in the generation of random numbers. Thus, in some embodiments, a seed number used in a game played by a primary player may be transformed according to some function (e.g., one may be added) in order to generate a seed to be used in the game played by the secondary player.</li>
                <li id="ul0012-0003" num="0191">&#x2003;In various embodiments, a game played by a primary player may result in a first outcome with a first associated payout. The game may be disguised by changing the first outcome to a second outcome with the same payout. Thus, the primary player may view the first outcome while he plays the game, but the secondary player may view the second outcome when he participates in the game. Monetarily, the primary player and the secondary player may have had the same experiences. In other words, given identical bets, both the primary player and the secondary player will have had the same payouts, in various embodiments. However, the primary player and the secondary player will have seen different representations of the game. For example, suppose a slot machine game includes several possible outcomes. Among the possible outcomes are &#x201c;bar-bar-bar&#x201d; with an associated payout of 10 coins, and &#x201c;cherry-cherry-cherry&#x201d;, also with an associated payout of 10 coins. The primary player may play the game and achieve the outcome &#x201c;bar-bar-bar&#x201d;. The secondary player may also participate in the game. When the game is presented to the secondary player, the secondary player may be shown an outcome of &#x201c;cherry-cherry-cherry&#x201d;.</li>
                <li id="ul0012-0004" num="0192">&#x2003;Thus, in various embodiments, a first outcome of a game may be generated for a primary player. The casino may determine what other outcomes have the same payout as the first outcome. From among the other outcomes, the casino may select one to present to a secondary player who has participated in the game.</li>
                <li id="ul0012-0005" num="0193">&#x2003;In various embodiments the outcome presented to a secondary player may differ both in terms of the constituent symbols and in terms of the payout from the outcome that was seen by the primary player. However, over the course of two or more games, a secondary player may be presented with outcomes whose associated payouts sum to the same total as do the payouts associated with the outcomes presented to the primary player over the course of the same two or more games. For example, both a primary player and a secondary player may participate in the same two games. In the first game, the primary player may be presented with outcome A and receive an associated payout of 4 coins. For the first game, the secondary player may be presented with outcome C and receive an associated payout of 3 coins. In the second game, the primary player may be presented with outcome B and receive an associated payout of 6 coins. For the second game, the secondary player may be presented with outcome D and receive an associated payout of 7 coins. Thus, neither the primary and secondary players have been presented with different outcomes over the course of the two games. However, after two games, both have received the same total payouts, each having received 10 coins in total. In various embodiments, a secondary player may view what is essentially the same game that the primary player is playing. However, the game may be disguised by replacing symbols from the presentation to the primary player with new symbols for presentation to the secondary player. For example, a &#x201c;cherry&#x201d; when viewed by the primary player becomes a &#x201c;dog&#x201d; when viewed by the secondary player. In terms of underlying logic, however, the games may remain the same. For example, &#x201c;cherry&#x201d; may always map to &#x201c;dog&#x201d;, and likewise there may be a consistent function which maps the symbols shown to the primary player to the symbols shown to the secondary player. The pay tables on display for the primary and secondary players may exhibit a similar functional relationship. For example, suppose the primary player's pay table includes a line showing a payout of 15 for &#x201c;cherry-cherry-cherry&#x201d;. A corresponding line on the pay table for the secondary player may include a line showing a payout of 15 for &#x201c;dog-dog-dog&#x201d;. In various embodiments, other graphics may be altered. For example, a background coloration of the game viewed by the primary player may be blue, whereas the background coloration of the same game viewed by the secondary player may be green.</li>
                <li id="ul0012-0006" num="0194">&#x2003;In various embodiments, a second game presented to the secondary player may be a different type of game from that presented to the primary player.</li>
                <li id="ul0012-0007" num="0195">&#x2003;However, an outcome may be chosen for presentation to the secondary player that has the same payout as an outcome that occurred in a game played by the primary player. For example, a primary player may be involved in a game of Casino War. The secondary player may view the outcomes of the games of the primary player, but disguised as the game of craps. For example, if the primary player wins a game of Casino War (e.g., by being dealt a card with a higher rank than the card dealt to the dealer), then the secondary player may be shown an animated sequence of dice rolling a seven during the first roll of the game (i.e., a winning outcome in craps). If, however, the primary player loses the game of Casino War, then the secondary player may be shown an animated sequence of dice rolling a two on the first roll of the game (i.e., a losing outcome in craps).</li>
                <li id="ul0012-0008" num="0196">&#x2003;The various methods of disguising a game described herein may provide an advantage, in certain embodiments, of making it difficult for the secondary player to determine details about the original game in which he is participating. For example, this may make it difficult for the secondary player to vary his bets based on advanced knowledge about the outcome of the original game. The same random number may be used, but a different reel configuration. In various embodiments, a gaming device may store an internal table or function which maps random numbers to symbols or outcomes. For example, the random number 1293 may map to the symbol of &#x201c;cherry&#x201d; on reel <b>1</b> of a slot machine. In various embodiments, a game played by a secondary player may utilize the same random numbers used in a game played by a primary player. However, the game of the secondary player may include a different table or matching function between random numbers and symbols. Thus, for example, in the game played by the secondary player, the number 1293 may map to the symbol &#x201c;bell&#x201d; instead of &#x201c;cherry&#x201d;. Accordingly, using the same random numbers, the game of the secondary player may arrive at different symbols or outcomes than those that occurred in the game of the primary player. In various embodiments, a gaming device may store an internal table or function which maps random numbers to reel positions. For example, the random number 2451 may instruct a gaming device to stop reel <b>1</b> with position <b>12</b> visible in the viewing window of the gaming device. Each position on a reel may feature a symbol. For example, a reel may have ten positions, each position corresponding roughly to 36 degrees of arc of the circular reel. Thus, by instructing a gaming device to stop a reel at a certain position, a random number will also instruct the reel to display the symbol featured at the certain position. In various embodiments, the game played by the secondary player may utilize the same random numbers utilized by the game played by the primary player. However, the positions and/or ordering of one or more symbols may be changed. Thus, the same reel position in the game of the secondary player may corresponding to a different symbol than it did in the game of the primary player. Thus, using the same set of random numbers, the game of the secondary player may nevertheless result in different symbols or outcomes than does the game of the primary player.</li>
                <li id="ul0012-0009" num="0197">1.10.4.2. What if all cherries were transformed into bars? A secondary player may bet on real outcomes, but with one aspect altered into another. In some embodiments, one or more symbols obtained in a game played by a primary player may be mapped to other symbols in a game played by a secondary player. For example, any &#x201c;cherry&#x201d; symbol in a game of a primary player may be transformed into a &#x201c;bar&#x201d; symbol in a game of a secondary player. Thus, if the primary player receives the outcome of &#x201c;cherry-bell-cherry&#x201d;, the secondary player will receive the outcome of &#x201c;bar-bell-bar&#x201d;. The pay table, between the two games, may remain the same. In embodiments where the pay table remains the same, it is possible for a winning outcome to be mapped to a losing outcome, and for a losing outcome to be mapped to a winning outcome. In some embodiments, a first card in one game is transformed into a second card in another game. For example, the two of hearts becomes the king of diamonds. In some embodiments, an entire outcome in a game of the primary player may be mapped to a different outcome in a game of the secondary player. For example, the outcome of &#x201c;bell-lemon-plum&#x201d; may map to &#x201c;cherry-cherry-cherry&#x201d;. In various embodiments, when one symbol in a game played by a primary player is mapped to another symbol in a game presented to a secondary player, the same mapping may also occur in the pay table. For example, suppose the symbol &#x201c;lemon&#x201d; in a game played by the primary player is mapped to the symbol &#x201c;tree&#x201d; in a game presented to the secondary player. If there is a line in the pay table of the primary player indicating a payout of 100 associated with the outcome &#x201c;lemon-lemon-lemon&#x201d;, then there may be a corresponding line in the pay table of the secondary player indicating a payout of 100 associated with the outcome &#x201c;tree-tree-tree&#x201d;.</li>
                <li id="ul0012-0010" num="0198">1.10.4.3. A secondary player may bet on original deals of cards, but with 7s now wild. In some embodiments, symbols in a game played by the primary player can take new meaning in the game of the secondary player. For example, in a game of cards, any seven dealt in the game of the primary player may count as a wild card in the game of the secondary player. Thus, for example, the primary player may receive a final poker hand of Qs Qh Jd 3h 7s. The primary player may then be paid based on having a hand with a pair, jacks or better. The secondary player may be paid based on having a hand with three of a kind, since the 7s, as a wild card, may count as a queen.</li>
                <li id="ul0012-0011" num="0199">1.10.4.4. A secondary player may bet on a blackjack hand occurring with poker, or vice versa. In various embodiments, the secondary player may use the same symbols or outcomes obtained by the primary player, but to play a different game. For example, the primary player may be engaged in a game of blackjack. The secondary player may use the cards received by the primary player to form a poker hand. Thus, if the primary player receives the 2s 7s 3s As and 6s, yielding 19 points in the game of blackjack, the secondary player may receive a flush (all spades) in a game of poker.</li>
                <li id="ul0012-0012" num="0200">1.10.4.5. A secondary player may bet on shifted data. For instance, an outcome consists of the last two reels from one slot pull, and then the first reel of the next slot pull. Or a hand of poker consists of the last three cards from one hand and the first two cards from the next hand. In various embodiments, data, symbols, or outcomes from two or more games of a primary player may be combined to create a single game for the primary player. For example, three cards used in a first game of the primary player, and two cards used in a second game of the primary player may be combined to form a single hand of cards for a single game of the secondary player. Data used in consecutive games of the primary player may be treated as a stream of data frames, each frame including all the data from one game. For example, each frame may include the three symbols appearing on the pay-line of a slot machine. A new stream of data frames may be created by shifting the frame limits over (e.g., left or right) by some number of data points, e.g., by some number of symbols. Thus, for example, each frame in the new stream of data frames may include symbols from reels two and three followed by a symbol from reel one. In other words, new games have been created by using the last two symbols in a first game of the primary player and the first symbol in a second game of the primary player. Thus, by shifting data frames used in a sequence of games of a primary player, a new sequence of games may be generated for a secondary player.</li>
                <li id="ul0012-0013" num="0201">1.10.4.6. A secondary player may bet on the same outcome, but with a different pay structure. For example, a secondary player may lose on a royal flush. In some embodiments, a secondary player may receive the same outcomes as does a primary player. However, the pay table that applies to the secondary player may differ from that which applies to the primary player. For example, in a game of video poker, the primary player may win 5 coins with a flush, but the secondary player may only win 2 coins.</li>
            </ul>
            </li>
        </ul>
        </li>
        <li id="ul0002-0028" num="0202">1.11. A secondary player may bet on an aggregate outcome of a primary player. For example, a secondary player may bet that a primary player will be ahead or behind after an hour. In some embodiments, a secondary player may place a bet that depends on multiple games or outcomes of a primary player. For example, the secondary player may bet that the primary player will win the next three games in a row, or that the primary player will win the next game but lose the following game. The secondary player may bet that the winnings or losses of the primary player will satisfy one or more conditions after a designated period of time. The secondary player may bet that the winnings of the primary player will total more than a given amount in the next hour. The secondary player may bet that the losses of the primary player will exceed more than $1000 in the next 6 hours. The secondary player may bet that primary player will either lose more than $100 or will win more than $200 in the next 15 minutes. Winnings and losses may be net of each other (e.g., a $20 win and $10 loss may net to a $10 win) or may count separately (e.g., a winnings total is the sum of all amounts won regardless of bets lost). The secondary player may bet on any statistic pertaining to outcomes received by the primary player. For example, the secondary player may bet that the primary player will receive more than 10 payouts of more than 20 coins each in the next 25 minutes. The secondary player may bet that the primary player will achieve 4 full-houses in the next 50 games. In various embodiments, the secondary player may track the net winnings or net losses of the primary player. Thus, for example, if the primary player has lost $200 after an hour, the secondary player will also have lost $200. If the primary player has won $734, the secondary player will also have won $734.
        <ul id="ul0013" list-style="none">
            <li id="ul0013-0001" num="0203">1.11.1. A secondary player may take the upside of a primary player, but not his downside. In some embodiments, the secondary player may make a payment or place a bet that entitles the secondary player to an amount equal to the primary player's winnings, if any, over a period of time, but does not obligate the secondary player for anything if the primary player has net losses. For example, if the primary player achieves winnings over the next hour of $50, the secondary player may also receive $50. However, if the primary player loses in the next hour, the secondary player does not owe anything beyond his initial bet or payment. In various embodiments, the secondary player may receive, or owe monies based on more complicated functions of the primary player's winnings and losses. For example, the secondary player may receive three times the primary player's winnings (if there are any) for the next hour, but may owe 1.5 times the primary player's losses if the there are losses.</li>
            <li id="ul0013-0002" num="0204">1.11.2. In some embodiments, a secondary player may bet that a primary player will receive five payouts of over 20 coins.</li>
        </ul>
        </li>
        <li id="ul0002-0029" num="0205">1.12. A secondary player may bet the difference between what a primary player bet and what the primary player could have bet. A secondary player may complete a partial bet and thereby win only the extra payouts that resulted from the extra amount bet. In some embodiments, a secondary player may place a bet that a primary player could have made but did not. This includes completing a bet that the primary player made. The secondary player may, in this fashion, win any payments that a primary player would have won, beyond those the primary player actually did win, had the primary player made the bet.
        <ul id="ul0014" list-style="none">
            <li id="ul0014-0001" num="0206">1.12.1. For example, many machines require three coins bet to win the jackpot. If a primary player bets only two coins, then a secondary player may bet the 3<sup>rd </sup>and then win the difference of what someone would win with three coins versus two coins bet. Various gaming devices include pay tables that are based on the number of coins bet. For example, if a player bets one coin and receives the outcome &#x201c;bell-bell-bell&#x201d;, then the player wins 100 coins. If, however, the player bets two coins and receives the same outcome, then the player wins 200 coins. Many gaming devices provide better payout odds for each incremental coin bet. Thus, in the prior example, if the player bets three coins and receives the outcome &#x201c;bell-bell-bell&#x201d;, then the player wins 400 coins. Thus, the incremental payout odds for the third coin bet are better than those for the second coin bet, at least with respect to &#x201c;bell-bell-bell&#x201d;. Accordingly, for example, if a primary player bets only two coins in a game, a secondary player may take advantage of the better incremental payout odds offered for the third coin bet by betting the third coin himself. If the outcome of &#x201c;bell-bell-bell&#x201d; occurs, the secondary player may thus receive the difference between the payout for three coins bet and the payout for two coins bet, i.e., the difference between 400 coins and 200 coins, equal to 200 coins.</li>
            <li id="ul0014-0002" num="0207">&#x2003;In various embodiments, a secondary player may add to or complete a bet on a game made by a primary player so that the total bet of both the primary and secondary player would result in a higher set of payouts. The secondary player may receive any extra payouts associated with his bet. Thus, if the payout associated with the primary player's bet alone is X, and the payout associated with the primary player's bet plus the secondary player's bet is Y, then the primary player may receive X, and the secondary player may receive Y-X.</li>
            <li id="ul0014-0003" num="0208">1.12.2. In craps, placing bets behind the bets of other people. In various embodiments, a primary player in a game of craps is given additional opportunities to bet during the course of a game. For example, when the primary player establishes a point for a pass line bet, he has the opportunity to place bets behind his pass line bet, called &#x201c;odds bets&#x201d;. The odds bets often have no house edge, and therefore are typically more advantageous to a player than almost any other bet in a casino. However, a player at a craps table often does not make an odds bet, or does not make the full amount of an odds bet that he is allowed. In various embodiments, a secondary player is allowed to make an odds bet that a primary player could have made. The secondary player may then be paid for the odds bet if the odds bet wins. Accordingly, the secondary player may enjoy the opportunity to make a bet at true odds, without the requirement of first making a disadvantageous pass line bet.</li>
            <li id="ul0014-0004" num="0209">1.12.3. In various embodiments, a secondary player may make odds bets or may make partial bets such as betting the third coin at a slot machine, even if the primary player has already made such bets. The secondary player may nevertheless receive the incremental payouts associated with such bets. For example, the secondary player may bet a single coin which counts as the third coin bet at a slot machine. The secondary player may thus be eligible to win the difference in payouts between the payout for three coins bet and the payout for two coins bet.</li>
        </ul>
        </li>
        <li id="ul0002-0030" num="0210">1.13. Primary players might see who or how many people are betting on them. In various embodiments, a primary player may be made aware of a secondary player who is participating in the game of the primary player, or who subsequently participates in the game of the primary player. The primary player may receive a name, an image, and description of various attributes (e.g., age, occupation, area of residence, etc.) of the secondary player. The primary player may also receive an indication of the performance of the secondary player while participating in the games of the primary player. For example, the primary player may see how much the secondary has won or lost, what types of bets he has made, how many games he has participated in, for how long he has been participating in the games of the primary player, and so on. The primary player may derive a measure of satisfaction or gratification from the participation of secondary players. For example, a primary player may feel proud that a large number of secondary players have participated in his games. He may feel proud to have won money for them. In various embodiments, the primary player may have the opportunity to communicate with a secondary player. For example, the casino server may provide the primary player with contact information for a secondary player.</li>
        <li id="ul0002-0031" num="0211">&#x2003;In various embodiments, a primary player may be compensated based on participation by secondary players in the games of the primary players. The primary player may be compensated per secondary player and per game. For example, the primary player may receive 0.5 cents per secondary player per game. Thus, if three secondary players each participate in two games of the primary player, the primary player may receive 0.5 cents&#xd7;3 secondary players&#xd7;2 games=3 cents. Thus, the primary player benefits by having more secondary players and by increasing the number of games in which each secondary player participates. The primary player may be compensated with a percentage of the bets made by secondary players participating in his games. The primary player may be compensated with some percentage of expected winnings to be derived from the bets of secondary players participating in the games of the primary player.</li>
        <li id="ul0002-0032" num="0212">&#x2003;A primary player may thus be encouraged to convey some value to secondary player so as to attract secondary players to participating in his games. The primary player may convey value by employing good strategy, for example. The primary player may also attempt to provide entertainment, e.g., by telling jokes or by making commentary about his games.</li>
        <li id="ul0002-0033" num="0213">&#x2003;In various embodiments, the games of a primary player, and/or data from the games of a primary player may be made available for participation and/or for viewing by interested secondary players. Data from the games of a primary player may be made available on an ongoing, continuous, and/or real-time basis. Secondary players may, at their leisure or pleasure, view or participate in the games. As such, data from the games of the primary player may be broadcast or transmitted in an analogous fashion to programs on a television or radio show, or analogously to periodically updated Web pages. Secondary players may tune in or out as desired. Each primary player may constitute a &#x201c;channel&#x201d; or &#x201c;station&#x201d;. A secondary player may, for example, view a list of primary players just as he would a list of television stations. The secondary player may then decide which primary player or &#x201c;station&#x201d; he wants to participate with. When selecting a primary player, the secondary player may also have the opportunity to review data about historical games played by the primary player. For example, the secondary player may be able to review the primary player's wins and losses over the prior <b>20</b> games.</li>
        <li id="ul0002-0034" num="0214">&#x2003;In various embodiments, a casino may select from a subset of available primary players to choose primary players for whose games data will be made available to secondary players. In some embodiments, a casino may serve as a &#x201c;disc jockey&#x201d; by choosing which primary players will have their data made available to others. The disc jockeys may be humans (e.g., casino employees), or may be computer algorithms which automatically select certain primary players based, for example, upon a defined set of rules. The disc jockey or jockeys may select primary players based on any number of factors. A primary player may be selected based on: (a) recent results (e.g., recent wins or high payouts); (b) based on long term results (e.g., long term profits); (c) based on skill at playing a game (e.g., based on his use of basic strategy in blackjack); (d) based on his celebrity status (e.g., based on whether his name has been published in any newspaper in the past year); (e) based on a history of being favored by secondary players; and so on. At any given time, a disc jockey may decide to stop making data available from certain primary players, and/or to commence making data available from other primary players. For example, a disc jockey may decide that a primary player has hit a string of losses and therefore would not be of interest to any secondary player. The disc jockey may accordingly stop making data from the primary player available. For example, a disc jockey may decide that a given primary player has just won a large payout and therefore would be of interest to secondary players. Accordingly, the disc jockey may commence making data from the primary player available.</li>
        <li id="ul0002-0035" num="0215">&#x2003;In various embodiments, the data about the games of a primary player may be made available across one or more casinos. A first casino may broadcast or transmit data from the games of one or more primary players to a second casino. The broadcast may occur via the radio or television spectrums, via mobile wireless frequencies, via microwave frequencies, via metal or optical cables, or via any other means. Secondary players in one or more of the casinos may view the data (e.g., may view games that are reconstructed based on the data). The data may be made available on the Internet, on one or more radio stations, on television, on interactive television, and so on. For example, a secondary player may visit a web page on which are listed names or identifiers for one or more primary players. The secondary player may click on an identifier in order to view data about games of the corresponding primary player. In some embodiments, a secondary player may set the channel on his television to a particular channel whereby identifiers for various primary players are listed on a menu. The secondary player may select an identifier from the menu (e.g., using a remote control) and may thereby call up on the television screen further data pertaining to the games of the primary player.</li>
        <li id="ul0002-0036" num="0216">&#x2003;In various embodiments, data about the game of a primary player may originate in a first casino. For example, the primary player may play the game in the first casino. Data about the game may be transmitted to a second casino. From the second casino (e.g., from a terminal located in the second casino), a secondary player may participate in the game. The second casino may thereby derive revenue from the secondary player by using data originating from the first casino. In various embodiments, the first casino and the second casino may split revenue, win, profits, theoretical win, or any other financial gain that has been derived from the use of the data at the secondary casino. For example, 50% of the theoretical win from a bet by the secondary player (i.e., the casino advantage on the bet multiplied by the amount bet by the secondary player) may be given to the first casino by the second casino. The financial gain may be split with one percentage going to the first casino and another percentage going to the second casino. In some embodiments, the second casino pays a flat fee to the first casino for the use of the data. The flat fee may cover all possible uses of the data (i.e., uses of the data in as many games as the second casino desires) or may cover a single use of the data (i.e., in one game). In some embodiments, the second casino keeps a fixed financial gain from the use of the data and pays any remaining financial gain to the first casino. For example, the second casino may keep 2 cents of theoretical win per game in which the data is used, and give the remaining portion of the theoretical win to the first casino. As will be appreciate, financial gain may be split between the first and second casinos in many other ways.</li>
        <li id="ul0002-0037" num="0217">1.14. A secondary player watches games in progress. The secondary player may have various ways of watching or following the game or games in which he is participating. Following a game may include receiving information about the outcome or result of the game, receiving information about symbols or indicia that have arisen in the game (e.g., cards that have been dealt), receiving information about outcomes or results received by a dealer or opposing players, receiving information about decisions that are available or have been made in a game (e.g., decisions by a primary player to hit or stand), receiving information about player mannerisms in a game (e.g., facial expressions of a primary player or his opponents), information about amounts bet on a game (e.g., amounts bet by the primary player or the secondary player), information about amounts won on a game (e.g., amounts won by the primary player or the secondary player); and so on.
        <ul id="ul0015" list-style="none">
            <li id="ul0015-0001" num="0218">1.14.1. A split screen allows the secondary player to see all the roulette wheels in the casino at once. In various embodiments, the secondary player may follow the progress of one or more games in which he participates using one or more display screens. Display screens may include cathode ray tubes, flat panel displays, plasma displays, liquid crystal displays, diode displays, light-emitting diode displays, organic light-emitting diode displays, projection displays, rear projection displays, front projection displays, digital light processing (DLP) displays, surface-conduction electron-emitter (SED) displays, electronic ink displays (e.g., E-Ink Corp's display technology), holographic displays, and so on. A secondary player may follow the progress of a game using a device such as a Blackberry&#xae;, iPod&#xae;, personal digital assistant, mobile phone, laptop computer, camera, personal computer, television, electronic book (eBook) and so on. A single screen may contain information about a single game in which the secondary player participates. A single screen may also contain information about multiple games in which the secondary player participates. The display screen may display information about one game on one part of the screen, and about another game on another part of the screen. For example, the screen may be divided into four quadrants, each quadrant showing information about a different game that the secondary player is participating in. A secondary player participating in two games may view a first of the two games on one display screen, and a second of the two games on another display screen. A secondary player may thus watch or follow the progress of games using multiple displays screens.</li>
            <li id="ul0015-0002" num="0219">1.14.2. Views come from overhead cameras. In various embodiments, a secondary player may follow the progress of a game in which he participates using video and/or audio feeds from the proximity of the game. For example, a camera may capture the progress of a blackjack game played by a primary player. By watching a video feed, the secondary player may see the cards dealt in the game, the decisions made by the primary player, the decisions made by the dealer, and the result of the game (e.g., win for the primary player, win for the dealer, blackjack for the primary player, tie). In various embodiments, video or audio feeds may be live, delayed, or may be stored and played back at a later time for the secondary player.</li>
            <li id="ul0015-0003" num="0220">1.14.3. Data is piped electronically from the slot machines. In various embodiments, data may be captured from a gaming device or live table game, encoded into electronic form, and transmitted to a display device, speaker, or other output device used to present the data to the secondary player. The output devices may decode the electronic data and present it in a sensible form for human viewing. The presentation may include a text description of occurrences in the game. For example, text may read, &#x201c;At 9:02 pm, slot machine number 1423 achieved the outcome of bar-bar-bar. Congratulations, you have won 20 coins.&#x201d; The presentation may include a reconstruction of the game. For example, the game may be reconstructed using animated renditions of the game. For example, an animated slot machine may show animated reels spinning and stopping to show the outcome achieved by the actual slot machine which generated the game the secondary player participated in. In another example, an animated dealer using animated cards may be used to reconstruct a live table game of blackjack. In various embodiments, a computer synthesized voice may report to the secondary player occurrences in a game in which the secondary player participates.</li>
            <li id="ul0015-0004" num="0221">1.14.4. Only active machines are shown to the secondary player. For example, the machine currently resolving into an outcome is shown. In various embodiments, a secondary player may participate in several games at once. The games may not necessarily all proceed at the same pace. For example, one game may finish while another is still in progress. In some embodiments, games or aspects of games may be presented to the secondary player only as important or relevant events occur in the game. For example, when a first game finishes, all or part of the game may be presented to the secondary player. For example, when the first game finishes, a depiction or an image of the final outcome (e.g., the final cards in the primary player's hand) may be flashed onto a display screen viewed by the secondary player. The image pertaining to the first game may be removed when a second game finishes. When the second game finishes, a depiction or image of the final outcome in the second game may be flashed onto the display screen. In this way, the secondary player need only view aspects of a game that are most relevant, most important, or most interesting to him. When a game is in an uninteresting stage (e.g., when the reels of a slot machine are spinning), the secondary player may view information about other games. Information that may be deemed worthy of showing to a secondary player may include: information about a decision that is to be made in a game (e.g., the primary player has received an initial hand of blackjack and must now decide to hit or stand); information about a decision that has been made in a game (e.g., the primary player has decided to hit); information about a new card, symbol, or other indicium obtained in a game (e.g., a new reel of the slot machine has stopped, showing a new symbol for the pay-line); information about a final outcome of a game; information about entry into a bonus round or bonus game (e.g., the primary player has just won the opportunity to play a bonus round); information about a symbol, card, or other indicium obtained by a dealer or by an opponent of the primary player; information about an amount bet (e.g., by the primary player or by the secondary player); and information about an amount won (e.g., by the primary player or by the secondary player).</li>
        </ul>
        </li>
        <li id="ul0002-0038" num="0222">1.15. The secondary player is alerted when his favorite primary player sits down. In various embodiments, a secondary player may prefer to participate in the games of particular primary players, in the games of particular gaming devices, in games played at particular gaming tables, in games played with particular dealers, and so on. A secondary player may explicitly record his preferences, e.g., by informing the casino. In some embodiments, the secondary player may be assumed to have certain preferences, based, for example, on a history of participating in the games of a particular primary player. For example, if a secondary player has participated in 300 games of a particular primary player, the secondary player may be assumed to prefer or to enjoy participating in the games of the primary player. In some embodiments, the casino may inform a secondary player when a game in which the secondary player may be interested in participating is or will be in progress. For example, suppose that the secondary player has indicated that he likes to participate in games played by primary player Joe Smith. When Joe Smith sits down at a gaming device and begins playing, the casino may detect the presence of Joe Smith (e.g., by means of a player tracking card inserted by Joe Smith) and may then alert the secondary player that Joe Smith has begun playing. The secondary player may then place bets on the games of Joe Smith. The casino may alert the secondary player using any number of communication means. A casino representative may call the secondary player, may send a text or email message to the secondary player, may page the secondary player, may find the secondary player in person, and so on.
        <ul id="ul0016" list-style="none">
            <li id="ul0016-0001" num="0223">1.15.1. A secondary player is alerted as to the presence of a primary player who has done well for him. A secondary player may be alerted when a primary player commences play if the secondary player has had favorable results in the past when participating in the games of the primary player. Favorable past results may mean that: the secondary player is ahead in terms of winnings based on all prior participation in the games of the primary player; the secondary player was ahead in the most recent time period during which he participated in the games of the primary player; the secondary player won more than a predetermined amount of money (e.g., more than $500) in a single session while participating in the games of the primary player; the secondary player won a jackpot or other high-paying outcome while participating in the games of the primary player; the secondary player was ahead in the most recent X number of games when participating in the games of the primary player; or any other measure of performance while participating in the games of the primary player.</li>
            <li id="ul0016-0002" num="0224">1.15.2. A secondary player is alerted as to the presence of a primary player with good statistics. A secondary player may be alerted when a primary player commences play if the primary player has a certain historical record or certain statistics that may be of interest to the secondary player. The historical record may include a record of: having won one or more jackpots or other high-paying outcomes; having won money for other secondary players; having achieved profitable sessions in the most recent gaming session or in any prior gaming session; having achieved a profit during some prior time period (e.g., during the past six months); and so on. A secondary player may also be alerted if a primary player that has some measure of popularity commences play. For example, primary players may be rated, e.g., by one or more secondary players, based on the secondary players' degree of satisfaction with, or other feelings towards the primary player. A primary player may, for example, be rated highly if he has won money for many secondary players in the past. Thus, for example, if a highly rated primary player commences play, a secondary player may be alerted and may be given the opportunity to participate in the games of the primary player.</li>
            <li id="ul0016-0003" num="0225">1.15.3. A secondary player is alerted when good machine is taken. In various embodiments, a secondary player may be alerted if play commences at a gaming device or table that is or may be of interest to the secondary player. The gaming device may be of interest due to a number of factors, among them: the secondary player has won a jackpot or other high-paying outcome while participating in games of the gaming device; the secondary player has had profitable sessions at the gaming device; the secondary player has had recent profitable sessions at the gaming device; the secondary player has had profitable sessions at another gaming device similar to the gaming device (e.g., at a gaming device of the same type or from the same manufacturer); one or more recent games at the gaming device have resulted in jackpots or high-paying outcomes; recent games at the gaming device have resulted in profits for the player or players at the gaming device; the gaming device is highly rated (e.g., by secondary players); and so on.</li>
        </ul>
        </li>
        <li id="ul0002-0039" num="0226">1.16. A secondary player pays a fee to participate in games. In various embodiments, a secondary player may be required to pay in order to participate in the game of a primary player. The amount paid may be based on the status, rating, historical results, or requests of the primary player. For example, if the primary player is a well-known celebrity, the fees required of a secondary player may be higher than if the primary player were a lesser-known celebrity. If the primary player has had highly favorable historical results (e.g., has made large profits in the past), then the fees required of the secondary player may be higher than if the primary player did not have such favorable historical results. In various embodiments, the primary player may also declare a fee required for secondary players to participate in his games. A portion of such fee paid by a secondary player may be paid to the primary player.</li>
        <li id="ul0002-0040" num="0227">1.17. Rules for using old data in a game with real money on the line. There is opportunity of misconduct since the player and/or the casino may know the data already. The use of historical games, outcomes, and other data related to a game presents an opportunity for an advantage by any party with knowledge of a data. For example, a casino might provide secondary players with the opportunity to participate only in games whose results the casino knows are losing for the player (and therefore winning for the casino). In another example, a secondary player may have already participated in a particular game (e.g., as a primary player) and may therefore know the outcome of the game in advance. The secondary player may thus make a large bet on the game if he knows the game will result in a winning outcome for him, and will make a small bet or no bet on the game if he knows the game will result in a losing outcome for him.
        <ul id="ul0017" list-style="none">
            <li id="ul0017-0001" num="0228">1.17.1. Before the original data is generated, it may be tagged for reuse at a particular date and time in the future. That way, the casino may be afforded no discretion as to whether or not to use the data. In various embodiments, before a particular game is played for the first time, a casino designates a time, date, location, and/or any other situation or circumstance under which the game will be made available for participation by others. The situation under which the game will be made available may be chosen randomly, according to some algorithm, or in any other fashion. Once the situation or circumstances for future participation in the game have been established, the game may commence for the first time. In this way, the casino has established future circumstances under which the game may be made available for participation by others (e.g., by secondary players) before the casino is aware of the outcome of the game. The casino cannot, therefore, decide not to allow participation in the game if the game turns out to result in a jackpot for the player. In various embodiments, the establishment of future circumstances under which a game will be available for participation by others is binding upon the casino. Regulators may keep track of when games must be made available for future participation, and may verify that the games have in fact been made available. In various embodiments, players or other parties may not necessarily know the circumstances under which a game must be made available in the future. In this way, players will not be able to selectively choose games to participate in based on advanced knowledge of the outcomes. In various embodiments, a record is stored, the record including information about a game and information about circumstances under which the game is to be made available in the future for participation by others.</li>
            <li id="ul0017-0002" num="0229">1.17.2. Data may be put in a queue. When it reaches the front of the queue, it must be used. In various embodiments, when a game is played or generated for the first time, data or information about the game is placed in a queue. Games from the queue are then made available for participation by secondary players based on a first-in-first-out model. Thus, a game becomes available for participation based on a relatively straightforward scheduling algorithm, and there is little discretion on the part of the casino as to when the game will become available for participation. In various embodiments, other scheduling algorithms may be used. For example, games are made available according to a last-in-first-out scheduling algorithm. Any other scheduling algorithm may be used, particularly if the casino has little control over the schedule once the outcome of a game is known.</li>
            <li id="ul0017-0003" num="0230">1.17.3. One set of data may be used after and only after another set of data. In various embodiments, data about a second game may be associated with data about a first game. The association may dictate that the data about the second game may be used to allow participation in the second game by a secondary player when, and only when, the data about the first game has been used. Similarly, data about a third game may be associated with the data about the second game, such that the data about the third game may be used when, and only when, the data about the second game has been used. In this way, through a chain of association, data about different games can be made available in sequence, allowing the secondary player to participate in a sequence of games. Data about different games may be associated in many ways. For example, data about a first game and a second game can be stored in locations with sequential addresses in a semiconductor memory. The casino may access the locations in the memory sequentially by address, and thereby make available data about the first game and data about the second game in sequence. In some embodiments, data about a given game may be associated with an index. The index may be a numerical index using integer numbers, for example. With such an indexing scheme, data about a game associated with index <b>235</b>, for example, would be made available once data about a game associated with index <b>234</b> had already been made available. In some embodiments, the index may be a time. The time may represent a time during which the associated data was originally generated, or a time when the data should be made available again, for example. For instance, when the time associated with a particular set of data actually comes to match the current time, the particular set of data may be made available so that a secondary player might participate in a game generated using the data.</li>
            <li id="ul0017-0004" num="0231">1.17.4. The time, date, and/or the machine that generated the data may be chosen at random. In various embodiments, a game that is made available for participation by a secondary player is selected at random using one or more randomly chosen variables or parameters. For example, a time and/or date may be chosen at random. Once a time and date have been chosen, for example, a game played at that time and date may be made available for participation by the secondary player. A gaming device, player, dealer, casino, location, and type of game may also constitute parameters that are chosen at random. In various embodiments, several parameters must be chosen at once in order to narrow down the universe of games to one particular game. For example, to determine a unique game, a time, date, and machine number may be required. In various embodiments, the parameters may be chosen by the secondary player, by the casino, or by third parties, such as regulators. Parameters may, in various embodiments, be chosen after the game has been played for the first time.</li>
            <li id="ul0017-0005" num="0232">1.17.5. The secondary player may choose the time and/or machine. In various embodiments, a secondary player may choose the time, date, machine, or other parameter used to select a game. The choice may not necessarily by random.</li>
            <li id="ul0017-0006" num="0233">1.17.6. Regulators may choose the time and/or machine. In various embodiments, a third party, such as a gaming regulator, may select a game that will be made available for participation by a secondary player. The third party may, in particular, have no stake in the outcome of the game. Therefore the third party may not be biased towards selecting a game that is winning for the secondary player or winning for the casino. The regulator or other third party may not necessarily select the game directly. Rather the third party may select one or more parameters (e.g., a time, date, machine number) that may be used to select a game that meets the selected parameters.</li>
            <li id="ul0017-0007" num="0234">1.17.7. A player who had his player tacking card in a gaming device when the data was originally generated may be prevented from playing a game based on that data. In various embodiments, the casino may verify that the secondary player was not present for a game when it was originally played and/or had no knowledge of the result of the game. The casino may verify that the player was not staying at the casino's hotel during the day or time when the game was played. For example, the casino may check records of who had checked into its hotel on the day of the game. The casino may check to see whether the player made any bets at the casino on the day of the game. For example, the casino may check to see whether the player had a player tracking card inserted into a gaming device, or otherwise on record, for the day of the game. It will be understood that the casino may verify the presence of the player not just during a particular day, but during longer or shorter time periods as well. For example, the casino may verify that there is no record of a player's presence during an entire 5 day period surrounding the day of the game. A casino may verify that a player was not in the same city where the game was played at the time the game was played. For example, the casino may verify that there is no record of the player at any other casino affiliated with the casino (e.g., under the same ownership as the casino) during the day of the game. The casino may use any practicable means to verify that the player had no knowledge of the game or the outcome of the game.</li>
            <li id="ul0017-0008" num="0235">1.17.8. Disallowing variation of bet size. In various embodiments, a secondary player may be prevented from varying the sizes of his bets over the course of a gaming session. In particular, the secondary player may be prevented from varying his bet sizes if he is participating in games that were first played in the past. The secondary player may thereby be prevented from varying his bet sizes based on advanced knowledge of the outcomes of the game. For example, the secondary player may be prevented from making larger bets when he knows the outcome of a game will be favorable, and a small bet when he knows the outcome of a game will be unfavorable.</li>
            <li id="ul0017-0009" num="0236">1.17.9. Bet limits on game. In various embodiments, limits may be placed on the size of bets placed on games that have already been generated or played. For example, a secondary player may be permitted to bet no more than $1 on a game that has been played in the past. In this way, the casino's losses will be limited even if the secondary player has knowledge of the outcome of the game. In some embodiments, the total amount of bets placed on a game may be limited. For example, bets placed by all secondary players participating in a particular game may be limited to totaling less than $5.</li>
            <li id="ul0017-0010" num="0237">1.17.10. Limits on winnings. In various embodiments, potential winnings or payouts for a game may be capped. For example, if the payout for an outcome of &#x201c;bell-bell-bell&#x201d; in an original game was 2000 coins, the potential payout for the same game may be reduced to 500 coins when a secondary player is participating in the game. This may limit the potential losses to a casino for a secondary player that has knowledge of the outcome of a game.</li>
            <li id="ul0017-0011" num="0238">1.17.11. Disguising a game. In various embodiments, one or more aspects of a game may be disguised before a secondary player is allowed to participate in the game. Thus a secondary player who had previously participated in the game may still fail to recognize the game and to bet accordingly. A game may be disguised in a number of ways. One or more graphics of the game may be changed to appear differently. For example, a &#x201c;cherry&#x201d; symbol may appear in a different shade of red or with three cherries on a stem rather than two. In some embodiments, new symbols are substituted in for old symbols. For example, rather than &#x201c;cherry&#x201d; symbols, a game may use &#x201c;blueberry&#x201d; symbols. However, outcomes containing blueberries may result in the same winnings as did outcomes with cherry symbols in the original game. In some embodiments, sound effects are changed or disguised. For example the background music in the disguised game may be different from that in the original game. In some embodiments, the animation or video sequences may be altered. For example, reels of a gaming device may appear to spin faster or slower, to appear jerkier or less jerky, etc., than they did in the original game. For live games, features of one or more players may be hidden or disguised. For example the face of a dealer at a live game may be blurred out in footage of the game. In some embodiments, a new face may be super-imposed over the old face of a dealer or player so as to heighten the effect of the disguising. As will be appreciated, there are many other possible ways of disguising a game so that its outcome is not predictable to even a player who has knowledge of the original game. As described elsewhere in this document, a game may be disguised by using a different game skin while maintaining the same underlying events, outcomes, logic, etc. In some embodiments, a game may be generated and presented using at least two steps. In a first step, the results of one or more random events are determined, leading to the determination of a final outcome and a final payout for the game. In the second step, data about the results of the random event(s), the final outcome, and the final payout are used to create a graphical presentation for the player. For example, once it is determined that a player will receive an outcome consisting of three like symbols, with an associated payout of 20 coins, such data may be fed into the second step. In the second step, a graphical rendering of slot machine reels may be created, with such rendering showing the reels spinning and finally landing on an outcome with three like symbols. Further the graphical rendering may include a flashing message that says, &#x201c;Congratulations, you won 20 coins!&#x201d; It will be appreciated that the first step may be performed by a first device, processor, algorithm or set of algorithms, and that the second step may be performed by a second device, processor, algorithm, or set of algorithms. Accordingly, the second device, processor, algorithm, or set of algorithms may be removed and replaced with a third device, processor, algorithm, or set of algorithms. This third device, processor, algorithm, or set of algorithms may receive the same set of data from the first step as did the second device, processor, algorithm, or set of algorithms. However, the third device, processor, algorithm, or set of algorithms may perform the second step in a different fashion. The third device, processor, algorithm, or set of algorithms may thereby generated a different set of graphics, graphical renderings, or other presentation formats than did the second device, processor, algorithm, or set of algorithms. Thus, the underlying structure of the game has remained the same, but it has been presented using a different skin.</li>
        </ul>
        </li>
        <li id="ul0002-0041" num="0239">1.18. Choosing aspects of a game. In various embodiments, a secondary player may choose a game in which to participate based on one or more attributes of the game or associated with the game. The secondary player may indirectly choose the game by first choosing an attribute, and then having the opportunity to participate in one or more games having the chosen attribute. Various attributes may be especially meaningful to a secondary player and thus a secondary player may prefer to play games having those attributes. In various embodiments, the casino may select for the secondary player a game with an attribute that is anticipated to be meaningful for the secondary player. In various embodiments, the casino may provide the secondary player with the ability to search for a game based on one or more attributes of the game.
        <ul id="ul0018" list-style="none">
            <li id="ul0018-0001" num="0240">1.18.1. Choose a special date. In various embodiments, a secondary player may find a particular date to be meaningful. Thus, the secondary player may select a game that was played on the date. If the casino knows a date to be meaningful for the secondary player, then the casino may select for the player a game played on that date.
            <ul id="ul0019" list-style="none">
                <li id="ul0019-0001" num="0241">********. Choose the secondary player's birthday. A meaningful date for a secondary player may be a birthday. The birthday may be the birthday of the secondary player, of a relative of the secondary player's, of a pet of the secondary player's, of a friend of the secondary player's and so on. The secondary player may indicate to the casino that such a date is meaningful to the secondary player. The casino may accordingly select a game for the secondary player that was played on the date. The casino may also have a record of the secondary player's birthday based on information already provided to the casino by the secondary player. For example, the secondary player may have provided the casino with his date of birth when signing up for a player tracking card, or when taking a loan from the casino. The casino may then select, without request from the secondary player, a game that was first played on the birthday of the secondary player.</li>
                <li id="ul0019-0002" num="0242">********. Choose a date on which a big jackpot was won. In various embodiments, a secondary player may wish to play a game that was first played on the date that a large payout, such as a jackpot, was won. This may give the secondary player the opportunity to participate in the game in which the jackpot was won. The secondary player may indicate to the casino a desire to play a game that was first played on the day of a big jackpot. The casino may then allow the secondary player to participate in one or more games played on the day of the jackpot. The secondary player may not himself know the date when a big jackpot was won. Thus, the secondary player may request that he be allowed to participate in games from the same date as the date that the last big jackpot was won.</li>
                <li id="ul0019-0003" num="0243">********. Choose a date when the progressive was still big. The secondary player may have a shot at the large progressive. In various embodiments, a secondary player may wish to have the opportunity to win a large progressive jackpot. As is well known, the size of a progressive jackpot may vary over time. In general, as time passes without a progressive jackpot being won, the progressive jackpot becomes larger. The current size of a progressive jackpot may not be large enough to satisfy the desires of a secondary player. Therefore, the secondary player may wish to participate in a historical game from a time that the progressive jackpot was larger. Accordingly, the secondary player may request to participate in a game that was first played at a time the progressive jackpot was in excess of a certain threshold. The casino may, accordingly, allow the secondary player to participate in such a game.</li>
            </ul>
            </li>
            <li id="ul0018-0002" num="0244">1.18.2. Choose a gaming device. In various embodiments, a secondary player may search for a gaming device having desired attributes or characteristics. Upon finding a gaming device with desired attributes or characteristics, the secondary player may choose to participate in games played at the gaming device. The secondary player may search for a gamine device using a search form. In the search form, the player may select from among various characteristics of a gaming device, some of which are described below.
            <ul id="ul0020" list-style="none">
                <li id="ul0020-0001" num="0245">********. A secondary player may search for a gaming device based on the historical results of the gaming device. For example, a secondary player may search for a gaming device with one or more of the following characteristics: (a) the gaming device has paid more than X amount of money in the last Y amount of time; (b) the gaming device has paid more than X amount of money in general; (c) the gaming device has paid X amount of in excess of what it has taken in, in the last Y amount of time; (d) the gaming device has made X amount in excess of what it has taken in, in general; (e) the gaming device has generated winning games for players in X % of its games in the last Y period of time; (f) the gaming device has generated winning games for players in X % of its games out of the last Y games; (g) the gaming device generated winning games for players in X of its most recent games; (h) the gaming device has paid X payouts greater than Y in the last Z games; (i) the gaming device has paid X payouts greater than Y; (j) the gaming device has paid a jackpot in the last X days (or other time period); (k) the gaming device has paid X jackpots in general; (l) the gaming device has entered X number of bonus rounds in his last Y games; (m) the gaming device has entered X number of bonus rounds ever.</li>
                <li id="ul0020-0002" num="0246">1.18.2.2. A secondary player may search for a gaming device based on the type of game or based on a characteristic of a game played at the gaming device. A secondary player may search for a gaming device with one or more of the following attributes: (a) the gaming device uses mechanical reels; (b) the gaming device uses video reels; (c) the gaming device has three reels; (d) the gaming device has five reels; (e) the gaming device has X number of reels; (f) the gaming device accepts a particular denomination of bets (e.g., penny, nickel, quarter, dollar); (g) the gaming device has X number of pay-lines; (h) the gaming device has 1 pay-line; (i) the gaming device has 3 pay-lines; (j) the gaming device has more than 1 pay-line; (k) the gaming device allows multiple bets per pay-line; (l) the gaming device is made by a particular manufacturer; (m) the gaming device or a game at the gaming device was introduced in the last X years (e.g., the game is a new game); (n) the gaming device has a particular theme (e.g., I Love Lucy, Regis Philbin); (o) the gaming device features a slot game; (p) the gaming device features a video poker game; (q) the gaming device features video blackjack; (r) the gaming device is part of a particular cluster of gaming devices (e.g., a cluster of gaming devices where an outcome at one gaming device may influence an outcome at another gaming device in the cluster); and so on.</li>
                <li id="ul0020-0003" num="0247">********. A secondary player may search for a gaming device based on one or more payouts that may be provided by the gaming device. Such payouts may be contingent on a primary player of the gaming device obtaining a particular outcome at the gaming device. A secondary player may search for a gaming device that has a top payout of over X times a bet, that has a payout of over X amount, and/or that has at least X payouts over Y amount. A secondary player may search for a gaming device that has more than X outcomes that are winning and/or a gaming device that has more than X outcomes that pay more than Y. A secondary player may search for a gaming device that has a particular or a particular range of payout frequency. For example, a secondary player may search for a gaming device that pays, on average, between once ever five games and once every seven games.</li>
            </ul>
            </li>
            <li id="ul0018-0003" num="0248">1.18.3. Choose a primary player. In various embodiments, a secondary player may search for a primary player having desired attributes or characteristics. Upon finding a primary player with desired attributes or characteristics, the secondary player may choose to participate in games of the primary player. The secondary player may search for a primary player using a search form. In the search form, the player may select from among various characteristics of the primary player, some of which are described below. For example, the secondary player may enter an age or age range desired in a primary player. The secondary player may also select a characteristic of a primary player from a menu. For example, the secondary player may select one of fifty states from a menu, the state indicating a desired residence location for a primary player. As will be appreciated, a secondary player may search for a primary player in many other ways. For example, a secondary player may communicate to a casino representative (e.g., via text message) a description of a primary player. The casino representative may then check records of people currently checked into its hotel or currently playing at gaming devices (e.g., with tracking cards inserted), and may attempt to locate a person matching the description provided by the secondary player. In some embodiments, a secondary player may seek a particular and unique individual, i.e., the secondary player may submit a description that can only be satisfied by one person in the world. For example, the secondary player may submit a name. In some embodiments, the secondary player may submit a description that may be satisfied by any one or a plurality of primary players. The secondary player need not have a particular individual in mind.
            <ul id="ul0021" list-style="none">
                <li id="ul0021-0001" num="0249">********. A secondary player may search for a primary player based on the historical results of the primary player. For example, a secondary player may search for a primary player with one or more of the following characteristics: (a) the primary player has won more than X amount of money in the last Y amount of time; (b) the primary player has won more than X amount of money in general; (c) the primary player has made X amount of profits in the last Y amount of time; (d) the primary player has made X amount of profits in general; (e) the primary player has won X % of his games in the last Y period of time; (f) the primary player has won X % of his games out of the last Y games; (g) the primary player won X of his most recent games; (h) the primary player has won X payouts greater than Y in the last Z games; (i) the primary player has won X payouts greater than Y; (j) the primary player has won a jackpot in the last X days (or other time period); (k) the primary player has won x jackpots in general; (l) the primary player has used optimal strategy in his last X games; (m) the primary player has used good or expert level strategy in his last X games; (n) the primary player has entered X number of bonus rounds in his last Y games; (o) the primary player has entered X number of bonus rounds ever.</li>
                <li id="ul0021-0002" num="0250">********. A secondary player may search for a primary player based on a historical relationship between the primary player and the secondary player. The secondary player may search for a primary player in whose game or games the secondary player has previously participated. The secondary player may search for a primary player, where, participating in the games of the primary player: (a) the secondary player has won a jackpot; (b) the secondary player has made a profit; (c) the secondary player has entered X number of bonus rounds; (d) the secondary player has won in X of the last Y games; (e) the secondary player has won X % of the last Y games; (f) the secondary player has won X payouts more than Y amount; and so on. The secondary player may also search for a primary player where the secondary player has participated in more than X number of games with the primary player.</li>
                <li id="ul0021-0003" num="0251">********. A secondary player may search for a primary player based on demographic characteristics of the primary player. For example, the secondary player may search for a primary player based on one or more of the primary player's: (a) age; (b) race; (c) marital status; (d) number of children; (e) number of grandchildren; (f) religion; (g) place of birth; (h) place of residence; (i) gender; (j) occupation; (k) income; (l) disability status; (m) education level; (n) high school attended; (o) college attended; and so on. For example, the secondary player may wish to participate in games of a primary player who shares one or more demographic characteristics with the secondary player.</li>
                <li id="ul0021-0004" num="0252">********. A secondary player may search for a primary player based on hobbies enjoyed by the primary player. For example, the secondary player may search for a primary player that enjoys a particular game or sport, or for a primary player that is a fan of a particular sports team.</li>
                <li id="ul0021-0005" num="0253">********. A secondary player may search for a primary player with whom the secondary player has some prior connection or relationship. The secondary player may search for a primary player in whose games the secondary player has previously participated. The secondary player may search for primary players in whose game the secondary player has previously won money, won a jackpot, won a large payout, or had some other result of interest to the secondary player.</li>
            </ul>
            </li>
            <li id="ul0018-0004" num="0254">1.18.4. In various embodiments, a secondary player may search for a particular game based on attributes of the game. The search may be particular to an individual game. For example, a search may distinguish between two games played by the same primary player at the same gaming device. In some embodiments, a secondary player may search for a game in which a certain amount has been bet. For example, a secondary player may search for a game in which three coins have been bet. The bet of three coins may make the primary player of the game eligible to win the jackpot. The secondary player may search for a game in which X number of pay-lines are activated, or a game in which X number of hands of video poker are being played simultaneously. A secondary player may search for a game based on the time or date on which the game was played.
            <ul id="ul0022" list-style="none">
                <li id="ul0022-0001" num="0255">1.18.4.1. In some embodiments, a secondary player may search for a game based on events that transpire within the game. For example, the game may have already occurred, or the game may be in process at the time of the secondary player's search. A secondary player may search for a game in which: (a) a particular set of cards have been dealt (e.g., a video poker game where a pair has been dealt in an initial hand, or a blackjack hand where cards totaling 11 have been dealt as a starting hand); (b) a particular symbol or symbols of an outcome have been determined (e.g., two bar symbols have appeared on the reels of a gaming device out of an outcome consisting of three symbols); (c) a bonus round has been reached; and/or (d) a certain level of a bonus round has been reached.</li>
            </ul>
            </li>
            <li id="ul0018-0005" num="0256">1.18.5. Providing a game for the secondary player to participate in. At some point, the secondary player may be ready to participate in a game with certain attributes. The attributes may be attributes specified by the secondary player. For example, the secondary player may have searched for a game with the certain attributes, or otherwise provided an indication of a desire to participate in a game with the certain attributes. In some embodiments, the casino may, for other reasons, wish to have the secondary player participate in a game with the certain attributes.
            <ul id="ul0023" list-style="none">
                <li id="ul0023-0001" num="0257">********. An actual historical game is provided. Given a set of attributes or characteristics, a casino may retrieve data about a historical game with the given set of attributes or characteristics. The historical game may be a game that was actually played by a real human player. For example, when a secondary player has indicated a desire to play in a game of video poker that was played by a primary player aged 60 years old, the casino may retrieve data about a game that was actually played in the past by a 60 year-old primary player and that was played at a video poker machine. The data retrieved may be used to display information about the game to the secondary player (e.g., to show screen shots of the cards being dealt in the game), to determine what the outcome of the game was, to determine whether the secondary player is a winner based on bets placed on the game by the secondary player, and to determine an amount to pay the secondary player. Data about historical games may be stored in a database or in any other storage means. Data about historical games may be indexed by different attributes, such as the age of the player or the type of game. Games may thus be searched by attributes, and data about games with attributes desired by a secondary player may be retrieved.</li>
                <li id="ul0023-0002" num="0258">********. A historical simulated game is provided. Given a set of attributes or characteristics, a casino may retrieve data about a historical game that was simulated. The game may not ever have been played by a real human being. In some embodiments, the outcome of the game may have been determined prior to play by a real human being. However, subsequent to the outcome being generated, a person (e.g., a secondary player) may have participated in the game. As with a historical game originally played by a live player, data about a historical game that was simulated may be stored in a database and indexed by attributes. Subsequently, data about historical games may be searched according to desired attributes. The data may then be used to recreate the game for a secondary player, and to determine an outcome and an amount to be paid to a secondary player.</li>
                <li id="ul0023-0003" num="0259">********. A current actual game is provided. Given a set of attributes or characteristics, a casino may determine a current game in progress with the given set of attributes or characteristics. For example, a 60 year-old primary player from Wisconsin may currently be involved in a game at a video poker machine in which an initial hand with a pair has been dealt. The secondary player may be allowed to participate in the game in progress. For example, the secondary player may be allowed to place a bet on what the final outcome of the game will be. In various embodiments, the secondary player need not have the benefit of the same pay table as does the primary player, since the secondary player is placing a bet in the middle of the game and has more information than the primary player did at the start of the game.</li>
                <li id="ul0023-0004" num="0260">********. A current simulated game is provided. Given a set of attributes or characteristics, a casino may simulate a game having the given attributes or characteristics. The casino may, for example, use a computer algorithm to determine cards to deal in a card game (e.g., video poker) or to determine symbols to show in a simulated reel slot machine. For example, if a secondary player desires to participate in a game of video poker, the casino may simulate a game of video poker. If the secondary player desires to participate in a video slot machine game, the casino may simulate a video slot machine game. In various embodiments, the casino may use algorithms to simulate table games as well as games typically played on a gaming device. For example, the casino server may simulate craps, blackjack, or poker. If other players would normally be present in a game, the casino may use computer algorithms to simulate the decisions that would have been made by humans. For example, in order to simulate a game of poker, the casino may use algorithms designed to bet, call, fold, raise, or check, according to certain pre-programmed rules. In some embodiments, a secondary player may wish to participate in a game in which certain symbols or outcomes occur. The casino may, in some embodiments, simulate multiple games until the desired symbols or outcomes occur. The secondary player may have the opportunity to participate only in the game, of the multiple games, in which the desired symbols or outcomes occurred. For example, the secondary player may indicate a desire to participate in a game in which three-of-a-kind was dealt on the initial hand in a game of video poker. The casino may deal a number of simulated hands of video poker. Only when the casino finally deals an initial hand with three-of-a-kind, e.g., due to random chance, does the casino allow the secondary player to then place a bet and to receive winnings for the final outcome of the game. In some embodiments, the casino may accept a bet from the secondary player first, simulate multiple games until a game with desired characteristics is simulated, and then pay the player based upon the outcome of the game with the desired characteristics. In some embodiments, the simulation may begin with a game of the desired attributes. For example, if a secondary player desires to play in a game of video poker with three-of-a-kind dealt on the starting hand, then the simulation may begin by immediately dealing three-of-a-kind. The simulation may randomize the remaining cards (e.g., shuffle the cards remaining after the three cards of the same rank have been dealt, the remaining cards completing a standard deck of 52 cards). The game may continue with two additional cards dealt from the randomized deck to complete the initial hand, followed by the discarding of one or two cards, followed by the replacing of the discarded cards with new cards from the randomized deck. In various embodiments, the secondary player may or may not have the opportunity to make decisions in a simulated game. For example, in some embodiments, the secondary player may choose which cards to discard in a game of video poker. In some embodiments, the cards that are discarded may be chosen automatically, e.g., by a computer algorithm employing optimal poker strategy.</li>
                <li id="ul0023-0005" num="0261">********. An alert is provided for when a game with desired characteristics will be played. Given a set of attributes or characteristics, a casino may determine when such a game will be played or will be likely to be played. For example, a secondary player may wish to participate in a game played by a primary player at a 3-reel slot machine, the primary player having three kids and a birthday in April. The casino may determine that a primary player with three kids and a birthday in April is indeed seated at a 3-reel slot machine. The primary player may have been playing for 20 minutes already, and presumably will continue to play. Therefore, a secondary player may be permitted to participate in games of the primary player from that point forward. The casino may alert the secondary player that a primary player with desired characteristics has been found and that the secondary player may begin placing bets in the games of the primary player. Further, the casino may begin transmitting information about the games of the primary player to the secondary player.</li>
            </ul>
            </li>
        </ul>
        </li>
        <li id="ul0002-0042" num="0262">1.19. A secondary player participates in a game where a progressive jackpot is won. In various embodiments, a secondary player may participate in a game for which the primary player is eligible to win a progressive jackpot. However, in various embodiments, a progressive jackpot constitutes a single pool of money, and therefore cannot be paid in its entirety to multiple different players.
        <ul id="ul0024" list-style="none">
            <li id="ul0024-0001" num="0263">1.19.1. The secondary player gets a fixed substitute. In various embodiments, when a primary player wins a progressive jackpot, a secondary player participating in the same game receives a fixed payment. The fixed payment may be some predetermined amount, such as $10,000.</li>
            <li id="ul0024-0002" num="0264">1.19.2. The secondary player gets a fixed percentage. In various embodiments, when a primary player wins a progressive jackpot, a secondary player participating in the same game receives percentage of the progressive jackpot.
            <ul id="ul0025" list-style="none">
                <li id="ul0025-0001" num="0265">********. The primary player gets the full amount, or less so the secondary player can be paid. In various embodiments, when a secondary player receives a percentage of a progressive jackpot won by a primary player, the amount received by the primary player from the jackpot may be correspondingly reduced. For example, if the secondary player receives X % of a progressive jackpot, the primary player may receive 100%-X % of the progressive jackpot. In various embodiments, for each bet placed on a game with a progressive jackpot, a portion of the bet is contributed towards increasing the size of the progressive jackpot. Thus, when a primary player and a secondary player each place a separate bet on a game, a portion of the primary player's bet may add to the size of the progressive jackpot, and a portion of the secondary player's bet may contribute to the size of the progressive jackpot. For each game, a fixed contribution to the progressive jackpot may be required. Thus, if both a primary player and a secondary player participate in a game, the contribution from the primary player towards the progressive jackpot may be less for that game than if only the primary player were participating in the game. In various embodiments, the primary player may receive the full amount of the progressive jackpot. The amount received by the secondary player may be over and above the amount paid out to the primary player. Even so, the secondary player may receive an amount equal to a predetermined percentage of the progressive jackpot, such as 10% of the progressive jackpot.</li>
            </ul>
            </li>
            <li id="ul0024-0003" num="0266">1.19.3. Part of progressive amount is set aside for secondary players before it is paid out. In various embodiments, a progressive jackpot is divided into two or more portions. A first portion is available to be won by primary players. A second portion is available to be won by secondary players. If a progressive jackpot is won in a game, a primary player participating in the game would win the portion of the progressive jackpot available to primary players, and a secondary player participating in the game would win the portion of the progressive jackpot available to secondary players. If there is no secondary player for the game, then the portion of the progressive jackpot available for secondary players may remain unclaimed.</li>
            <li id="ul0024-0004" num="0267">1.19.4. There is a progressive just for secondary players. In various embodiments, a progressive jackpot (other similar terms used herein may include &#x201c;progressive prize&#x201d;, &#x201c;progressive prize pool&#x201d;, &#x201c;progressive pool&#x201d;, &#x201c;progressive payout&#x201d;) may grow from the contributions of only secondary players. The progressive jackpot may be available to be won only by secondary players. For example, for each bet a secondary player puts on a particular type of game, a portion of the bet may be set aside and added to a progressive jackpot. If a secondary player participating in the particular type of game later wins the progressive jackpot, the jackpot may go to the secondary player. The size of the progressive prize pool may then go down to zero. In some embodiments, once a progressive prize pool has been claimed, the next pool may be seeded with some money by a casino, e.g., with $10,000, so as to garner interest from secondary players. In various embodiments, a display visible by a secondary player may track the size of a progressive. For example, a secondary player may participate in games using a mobile device (e.g., a mobile device as set forth in Nevada bill AB471). The mobile device may maintain on its display screen a running tally of the size of the progressive pool. In various embodiments, two or more separate progressive jackpots may be available for secondary players. In various embodiments, a secondary player may be eligible to win a progressive prize based on the location or geographic region from which the secondary player participates in games. For example, a secondary player participating while seated in Casino A may be eligible for a first progressive prize pool of $10,000. Another secondary player participating while seated in Casino B may be eligible for a second progressive prize pool of $20,000. A progressive prize pool may be available to be won by a particular secondary player based on one or more characteristics or circumstances of the secondary player, such characteristics or circumstances including: (a) a demographic of the secondary player, such as an age, birthday, birthplace, marital status, educational status, and so on (e.g., there may be a first progressive pool for secondary players aged 60 or over and a second progressive pool for secondary players aged 59 or under); (b) the particular type of game the secondary player is participating in (e.g., there may be separate progressive prizes for slot machine games and video poker games); (c) the location or geographic region from which the secondary player is participating (e.g., there may be different progressive pools for different casinos, different cities, different states, etc.); (d) the time or date during which the secondary player is participating (e.g., there may be a different progressive prize offered during each six-hour period in a day); (e) the identity of the primary player (e.g., there may be a first progressive prize pool associated with the games of a first set of primary players, and a second progressive prize pool associated with a second set of primary players); (f) a characteristic or circumstance of the primary player (e.g., demographic, location, etc. of the primary player); (g) a bet being made by the secondary player (e.g., a secondary player may be eligible for a first progressive prize if his bet is more than $3, and a second progressive prize if his bet is less than $4); and so on. In various embodiments, a progressive prize pool may be associated with a given period of time. For example, a progressive prize pool may be associated with a particular day. The progressive prize pool may be associated with a guarantee that it will be won on its associated day (or its associated period of time). According to the guarantee, the progressive prize may be claimed by the first secondary player to achieve outcome A, the first secondary player to achieve outcome B if no secondary player achieves outcome A, the first secondary player to achieve outcome C if no secondary player achieves outcomes A or B, and so on. In various embodiments, a progressive prize pool may have its probability of occurrence set so that it is likely the pool will be won during an associated time period. For example, if it is anticipated that secondary players will play 10,000 games during a given time period in which they have a chance of winning a progressive, the probability of winning for each game may be set at 1/5000. The probability that the progressive will be won during the time period may then be approximately 86%. In some embodiments, as the casino may be aware in advance of the outcomes of games to be played by a secondary player, the casino may intentionally offer for play at least one game that will result in a progressive prize being won. One such game may be offered during every period in which a progressive prize is guaranteed to be won. In various embodiments, two or more progressive prize pools may be simultaneously available to be won by a secondary player. One progressive pool may be associated with a relatively shorter period of time, while another progressive pool may be associated with a relatively longer period of time. For example, a first progressive prize pool may be won, on average, once a year. In fact, the first progressive prize pool may be guaranteed to have a winner every year. A second progressive prize pool may be won, on average, once a day. A secondary player may be eligible to win either of the progressive prize pools in the same game. In some embodiments, a secondary player may win only the first progressive prize pool while participating in a first game. In some embodiments, a secondary player may be eligible to win only the second progressive prize pool while participating in a second game.</li>
            <li id="ul0024-0005" num="0268">1.19.5. A secondary player cannot play games with progressives. In various embodiments, secondary players may not be allowed to participate in games with progressive payouts.</li>
            <li id="ul0024-0006" num="0269">1.19.6. A secondary player wins the full amount of the progressive. In various embodiments, when a progressive payout is won in a game, the secondary player may receive the full amount of the progressive. For example, suppose a primary player wins a progressive jackpot in a game for which the progressive jackpot is $100,000. The primary player may receive $100,000. The secondary player may also receive $100,000.</li>
            <li id="ul0024-0007" num="0270">1.19.7. Making up extra funds to pay secondary players. In various embodiments, a progressive payout (e.g., a progressive jackpot) may consist of funds held in reserve for a time when the jackpot must be paid out. If a progressive jackpot is won in a game where a secondary player is participating, the progressive jackpot may go to the primary player and additional funds must be obtained by the casino to pay the secondary player. In various embodiments, the casino may pay the secondary player out of a separate pool of funds, such as an account used by the casino for general business expenses. In some embodiments, the secondary player may receive a promise of payment. The secondary player may receive a portion of contributions towards future progressive payouts. For example, the secondary player may receive 50% of all portions of bets withheld for a subsequent progressive jackpot until such time as the subsequent progressive jackpot is won.</li>
        </ul>
        </li>
        <li id="ul0002-0043" num="0271">1.20. Anti-vulture provisions. A secondary player may be prevented from playing in games with a positive expected value. Various situations may arise with respect to a gaming device or with respect to a live table game where betting circumstances are favorable to a player. Favorable circumstances may include circumstances where a player might expect to receive, on average, more than 100% of his bet from winnings in a game. For example, if a progressive jackpot or other payout at a slot machine reaches a certain level, the slot machine may return, on average, more than 100% of an amount bet. In some slot machines, certain symbols, tokens, or other objects may be accumulated from game to game. For example, Double Diamond Mine&#xae; slots, made by IGT, allow a player to accumulate diamond symbols from game to game. Once 10 diamond symbols from a particular reel have been accumulated, the player wins a payout. A slot machine in which a number of such objects have been accumulated may return, on average, more than 100% of an amount bet. In games of blackjack, such as in live table games of blackjack, a game may return more than 100% of an amount bet if the cards remaining in a deck have a predominance of one type of card (e.g., of high cards).</li>
        <li id="ul0002-0044" num="0272">&#x2003;In various embodiments, a secondary player may be allowed to search for historical games in which the expected payout is more than 100% of the bet. For example, the secondary player may search for games at a Double Diamond Mine&#xae; slot machine where nine diamond symbols for each reel have already been accumulated. In another example, the secondary player may be allowed to search for gaming devices in which a progressive jackpot has exceeded a certain threshold. The secondary player may be allowed to participate in such games. However, in some embodiments, the secondary player may be prevented from participating in games in which an expected payout is more than 100% of the bet. In some embodiments, a secondary player may only be allowed to participate in games returning more than 100% of an amount bet if such games arise during a longer sequence or session of play. For example, a secondary player may be allowed to participate in a Double Diamond Mine&#xae; slot game for which nine diamond symbols have accumulated for each reel only if the secondary player has already participated in immediately prior games that had occurred at the same slot machine.</li>
        <li id="ul0002-0045" num="0273">&#x2003;Tracking of game data usage. In some embodiments, a game that was originally played at a first casino or other establishment may subsequently be recreated at a second casino or establishment. For example, a secondary player at a second casino may participate in a game that was originally played at a first casino. The second casino may derive revenue, profit, or other financial gain from the recreation of the game at the second casino. For example, when a secondary player places a bet on the game at the secondary casino, the secondary casino may expect to win some portion of the bet, on average. In some embodiments, the second casino may compensate the first casino for the privilege of using or recreating the game that was first generated or played at the first casino. In various embodiments, the use of games for participation by secondary players may be tracked. The tracking of such use may allow a first casino (e.g., the casino that originally generated a game) to track how much it is owed, and a second establishment (e.g., the casino that recreated the game for play by the secondary player) to track how much it owes. The use of a game at a casino may be tracked in a number of ways. Data related to the game, e.g., a game identifier, may be stored in a database. A time during which the game was recreated may be stored. Other items stored may include: (a) an identity of a secondary player who played the game; (b) an amount bet on the game; (c) an amount won or lost by the casino recreating the game; (d) a type of bet placed on the game; (e) a number of secondary players who participated in the game; (f) a location of a secondary player who bet on the game; (g) an amount owed to the casino that originally generated the games; and so on. Data about individual games may not be stored, in some embodiments. Rather, data about blocks or groups of games may be stored. For example, a casino may store a record indicating that a group of 1000 games was recreated during the afternoon of Aug. 17, 2010, and that a total of $40,000 was bet on the games.</li>
        <li id="ul0002-0046" num="0274">&#x2003;In various embodiments, a casino that used or recreated one or more games may send a report about the use of the games to the casino that originally generated the games. For example, the casino that recreated the games may send a printed report with each line on the report detailing, e.g., a particular game, a particular time the game was recreated, an amount bet, and an amount owed to the casino that originally generated the games. The report may be a paper or electronic report. The report may be sent by postal mail, email, fax, via download from the Internet, or via any other means. A report may cover a single game or a group of games. A report may be sent in real time (e.g., a report about the use of a game may be sent to the casino that originated the game as the game is used or immediately after the game has been used), periodically (e.g., every hour), or once (e.g., at the end of a period for which the casino using the games is authorized to use the games by the casino that first generated the games).</li>
        <li id="ul0002-0047" num="0275">&#x2003;Data stored by a casino relating to the use or re-creation of games within the casino may be obtained from devices used for play by secondary players. For example, a terminal at which a secondary player participates in a game may store and/or transmit various data to the casino server, such as amounts bet by the secondary player, which games the secondary player played, and so on.</li>
        <li id="ul0002-0048" num="0276">&#x2003;In various embodiments, a casino that uses data about games originally generated at another casino may track or record the use of various images associated with the game. Based on the use of images, royalties may be paid to copyright holders of the image. Also, the casino that originally generated the game may track the use of images from the game.</li>
        <li id="ul0002-0049" num="0277">1.21. Bucket shop paradigm. Under this paradigm an establishment hopes to invest the least amount possible in casino infrastructure, including games, and even licenses to be a casino operator. Instead, the establishment plans to just reuse data from a real casino, set up a nice fa&#xe7;ade, and open up for business. In various embodiments, an operator may set up a gaming facility which uses solely or predominantly games or outcomes that have already been generated. The operator may thereby save various costs, possibly including the costs of purchasing gaming equipment, costs of obtaining accounting software and other infrastructure, and costs associated with meeting various regulations. For example, by reusing outcomes that have already been generated, an operator need not buy expensive gaming machines to generate original outcomes. Further, the operator need not submit such gaming machines for regulatory approval or inspection. In some embodiments, an operator of a facility that only reuses games and outcomes already generated may not be required to obtain the same types of regulatory approval as does a facility that generates original games and outcomes. The operator of the facility that reuses games and outcomes need not, in some embodiments, submit devices used by secondary players to the same process of regulatory approval that ordinary gaming devices (e.g., slot machines) are subject to. Rather the regulatory approval process may be simpler for the devices used solely by secondary players. In some embodiments, an entire facility that only reuses games or outcomes may not be subject to the same regulatory processes as is a facility that generates original outcomes. Rather, the regulatory processes may be simpler for facilities that solely reuse games or outcomes.</li>
        <li id="ul0002-0050" num="0278">&#x2003;In some embodiments, by using outcomes already generated, an operator may use accounting data that has already been generated to account for amounts received, won, and lost based on the outcomes. Thus, the operator may save on accounting software and other accounting infrastructure, such as networks or intranets for conveying accounting related information.
        <ul id="ul0026" list-style="none">
            <li id="ul0026-0001" num="0279">1.21.1. Use of shell machines that simply display outcomes from other machines. In various embodiments, an operator may install machines or devices with simplified functionality. The machines may include currency acceptors, credit card acceptors, or other acceptors for consideration to be used for betting purposes. The machines may include output devices, such as microphones for audio output and display screens for video or graphical output. The machines may further include dispensers for cash, coins, currency, tokens, chips, cashless gaming receipts, or other consideration. Consideration may be paid to a player based on amounts won while participating in games, or based on amounts remaining from an initial deposit made by a player. The machines may further include media players and/or media storage devices. For example, the machines may include DVD players or VHS players. The machines may include VHS tapes, DVDs, CDs, flash memory, or other media storage devices. The machines may further include buttons, handles, and touch screens for use by a player to input information, such as amounts to bet. The machines may further include network interfaces for sending and receiving information via a network, such as an intranet or internet. Network interfaces may include wireless network interfaces, such as antennae. Operationally machines according to various embodiments may receive a record of historical games, stored on a media device, such as a DVD. The machines may receive currency from a player. The machines may then receive an indication of an amount to bet. The machines may then receive an initiation signal for a game from the player. The player may convey the initiation signal, for example, by pressing a button labeled &#x201c;spin&#x201d; on the machine. The machine may then play for the player a video or other depiction of a stored game from the DVD. For example, the machine may play a 10-second video clip from the DVD, the video clip depicting a historical game that occurred at an actual slot machine. The machine may determine an outcome of the game. For example, the DVD may store, in association with each game, information about a payout or payout ratio associated with the game. Based on the information about the payout, the machine may pay the player. The player may be paid by, e.g., dispensing currency through a dispenser of the machine, or by adding to a balance of player credits stored on the machine. In various embodiments, the machine does not itself generate any outcomes or games. The machine merely replays games that have been previously generated. In various embodiments, the machine may recreate games based on a limited amount of information about the games. For example, the machine may receive information about the outcome of a game. The machine may then display an animated sequence depicting slot reels spinning and stopping to show the outcome. In some embodiments, the machine need not store information about prior games locally on the machine. Rather, the machine may receive information about historical games via the network. As information about historical games is received, the machine may recreate the historical games for the benefit of a secondary player at the machine.</li>
            <li id="ul0026-0002" num="0280">1.21.2. Simplified regulatory license. An operator is just reusing data that's already been certified. There is no need to recertify data. In various embodiments, an operator using historical outcomes may operate without one or more licenses required of a typical gaming operator. A special license may be granted for operators who use only historical outcomes. A special license may be granted for operators who use only historical outcomes which have come from licensed gaming establishments.</li>
            <li id="ul0026-0003" num="0281">1.21.3. Reuse of accounting data. There is no need for an operator to generate his own accounting data. In various embodiments, a casino operator may generate a number of original games or outcomes. Based on the outcomes, the casino may generate a record of amounts won, amounts lost, amounts collected, amounts owed in taxes, and so on. Such data may constitute accounting data. The casino operator may subsequently share such accounting data with a second operator who reuses the outcomes generated by the first casino operator. Since the outcomes used are the same, the accounting data required may be the same or similar. Therefore, in some embodiments, the second operator may receive the accounting data from the first casino operator, and reuse the accounting data for its own records.</li>
            <li id="ul0026-0004" num="0282">1.21.4. Pre-inspection of the data is not allowed, as then the bucket shop could be accused of knowing the outcomes in advance. In various embodiments, an operator using historical games or outcomes is forbidden by law, regulation, convention, or other policy from obtaining knowledge about the games or outcomes prior to the participation in the games by a secondary player. In this way, the operator may be discouraged from selectively making available games or outcomes that are unfavorable to the operator.</li>
        </ul>
        </li>
        <li id="ul0002-0051" num="0283">1.22. Multi-Tiered Poker Game. In various embodiments, a poker game occurs. The poker game may include a number of live players at a table at a casino. The poker game itself may be referred to as a first tier game. Based upon the first tier game, a second tier game may be played. The second tier game may involve a different set of players. In some embodiments, the second tier game includes one player for each player in the first tier game. Each person in the second tier game may be associated or matched with a person in the first tier game. In various embodiments, a person in the second tier game may bet on what his associated player will do in the first tier game. For example, the player in the second tier game may bet that his associated player in the first tier game will check, bet, raise, call or fold. Further, the person in the second tier game may place a bet on the amount that the associated person in the first tier game will bet. For example, if Joe in the second tier game is associated with Sue in the first tier game, then Joe may bet that Sue will raise by at least 30 chips. In various embodiments, a person in the second tier game cannot communicate with his associated person in the first tier game. In various embodiments, no one in the second tier game can communicate with anyone in the first tier game, and vice versa. In various embodiments, a person in the second tier game knows the cards of the associated person in the first tier game, but does not know the cards of any other player in the first tier game.</li>
        <li id="ul0002-0052" num="0284">&#x2003;In various embodiments, a person in the second tier game may also check, bet, raise, fold, or call against other people in the second tier game. He may bluff and hope other people in the second tier game will fold. Should two or more players remain in a second tier game once the first tier game has reached its conclusion, a pot in the second tier game may be awarded to a person in the second tier based on the results of the first tier game. Namely, if a person in a second tier game is associated with the person in the first tier game who won the first tier game, then the person in the second tier game will also win in the second tier game. In some embodiments, the result or outcome of the second tier game is decided as if each person in the second tier game held the cards of his associated person in the first tier game. In various embodiments, if a player in the first tier game folds, the associated player in the second tier game folds automatically, and thus loses in the second tier game.</li>
        <li id="ul0002-0053" num="0285">&#x2003;In various embodiments, there may be higher tiers. For example a third tier may include the same number of players as are in the second tier (or, equivalently, the first tier). Each player in the third tier may be associated with a player in the second tier. Thus, the player in the third tier may automatically be associated with the person in the first tier to whom is associated the player in the second tier that is associated with the player in the third tier. In other words, one player in each tier may be associated with a particular hand of cards, and all such players may be associated with one another. Players in the third tier may place bets on what bets will be made by associated players in the second or first tiers, and on how much will be bet by such players. Further players in the third tier may make bets against one another to be decided by results of lower tiers. A player in the third tier may win a pot if he has not folded, his associated player in the second tier has not folded, his associated player in the first tier has not folded, and his associated player in the first tier has the best poker hand at the conclusion of the first tier game. However, if an associated player in the first or second tier folds, a player in the third tier is automatically folded. Note, however, that a player in the second tier is not automatically folded if an associated player in the third tier has folded. It will be appreciated that there may be any number of tiers, with fourth, fifth, sixth, etc., tiers operating in an analogous fashion to what has been described with respect to the first three tiers. In some embodiments, a person in a tier greater than the first tier may see the cards of all players in the first tier.
        <ul id="ul0027" list-style="none">
            <li id="ul0027-0001" num="0286">1.22.1. There may be time limits on people in higher tiers so they can't stall to see what happens in the actual game. In some embodiments, a player in tier two or above may have a time limit for making bets or other game decisions. The time limit may force a player in tier two or higher to take action before the game proceeds in tier one, and thus before the player in tier two or above discovers important information from watching the first tier players that might aid him in his game decision.</li>
            <li id="ul0027-0002" num="0287">1.22.2. A higher tier game may not occur in a live environment. Thus higher tier players may bet after the fact. In various embodiments, tier two, tier three, and higher tier games may occur after the tier one game has occurred. Accordingly, a playback of the action in the tier one game may be halted until all appropriate actions have been taken in the higher tier games.</li>
            <li id="ul0027-0003" num="0288">1.22.3. Tiers could form among people at the pool, using handheld devices. In various embodiments, a second tier, third tier, or higher tier game may form amongst players that are remote from a poker table. For example, players located poolside at a casino may engage in a second tier game using handheld devices, such as personal digital assistants. Thus, the second tier players may benefit from the work of a dealer and from the use of physical cards, but without having to be physically present at a poker table.</li>
        </ul>
        </li>
        <li id="ul0002-0054" num="0289">1.23. In various embodiments, a first secondary player may receive an alert regarding the activities of a primary player and/or of a second secondary player. An activity that may trigger an alert may include: (a) the primary player inserts a tracking card into a gaming device; (b) the primary player inserts currency or other consideration into a gaming device; (c) the primary player presents a tracking card or other identification at a table game (e.g., at a blackjack game); (d) the primary player buys chips at a table game; (e) the primary player places a bet in a slot machine game; (f) the primary player places a bet in a game; (g) the primary player participates in a game; (h) the primary player receives a payout in a game; (i) the primary player checks into a hotel; (j) the primary player pays for a meal at a restaurant (thereby identifying himself with a credit card, for example); and so on. Similar activities by the second secondary player may trigger an alert for the first secondary player. An alert may be sent to the secondary player if the primary player was or is flagged for any reason, such as being of interest to the first secondary player. For example, the first secondary player may have indicated that the primary player is the favorite player of the secondary player. Thus, the first secondary player may wish to be alerted any time the primary player is playing or will begin playing so that the first secondary player may have the opportunity to participate in the games of the first primary player. An alert may be transmitted to a device of the second secondary player, including a cell phone, personal digital assistant, Blackberry&#xae;, laptop, personal computer, television, and so on.</li>
        <li id="ul0002-0055" num="0290">&#x2003;An alert may also be transmitted to the first second secondary player under other triggering conditions. An alert may be sent to the first secondary player if a primary player of interest: (a) is playing a particular game (e.g., a favored game of the second secondary player); (b) has had a streak, such as a winning streak or losing streak (e.g., the primary player has won 10 games in a row; e.g., the primary player has lost games in a row); (c) the primary player has won a certain amount (e.g., the primary player has won more than $100); and so on. An alert may be sent to the first secondary player based on similar triggering conditions involving the second secondary player.</li>
        <li id="ul0002-0056" num="0291">1.24. Embodiments disclosed herein need not apply only to casino gaming. Rather, where applicable, disclosed embodiments may apply to a wide variety of games, contests, sporting events, random events, unknowns, and so on. Where applicable, disclosed embodiments may apply to anything that may be the subject of a bet. Disclosed embodiments may apply to table games, video games, boxing matches, sporting events, the price movements of equities, the price movement of bonds, the movements of other market securities, the results of elections, the weather, the temperature, the average test scores of a body of students, and so on. For example, a secondary player may place a bet on whether a stock price will go up or down in the next ten minutes. Note that, in various embodiments, a primary player need not be explicitly present. For example, a secondary player may bet on the temperature a day in the future even though there is no primary player per se who effects the temperature.</li>
        <li id="ul0002-0057" num="0292">1.25. Embodiments described herein need not apply only to complete games. Where applicable, embodiments described herein may apply to events within games. For example, a secondary player may bet on the next card that a primary player will receive in a game. A secondary player may bet on the next roll of the dice, on how many times a player will hit in a game of blackjack, on the point total of the dealer's hand in a game of blackjack, on the contents of a flop in a poker game of Texas Hold'em, and so on. A secondary player may be alerted when certain sequences of events have occurred. For example, a secondary player may be alerted when the last ten cards dealt in a game were red cards (i.e., hearts or diamonds). A secondary player may view historical data about events within a game or games. For example, the secondary player may examine historical data about the number of times the number 12 has been rolled in craps in the last 10 minutes.</li>
        <li id="ul0002-0058" num="0293">1.26. A secondary player just watches a primary player. In various embodiments, a secondary player may wish to watch the play of a primary player, watch the games of a primary player, watch the facial expressions of the primary player, follow the strategies of the primary player, examine the historical results of the primary player, or otherwise track the primary player. The secondary player may wish to track the primary player without betting or risking any money on the games of the primary player. For example, a secondary player may wish to watch the games of a primary player who is a celebrity. Simply watching the celebrity player may provide entertainment for the secondary player.</li>
        <li id="ul0002-0059" num="0294">&#x2003;A secondary player may search for a primary player based on any number of criteria, such as those mentioned above. A secondary player may search for a primary player based on a name (e.g., Ben Affleck); based on a demographic; based on a celebrity status (e.g., a name that generates more than 1000 hits in a Google search); based on a typical amount bet (e.g., a secondary player may search for any player who bets more than $100 per game); based on a history of wins or losses; based on strategies employed; based on facial expressions (e.g., a computer algorithm may score the expressiveness of a primary player's face and allow the secondary player to search for the most expressive faces); and/or based on any other criteria.</li>
        <li id="ul0002-0060" num="0295">&#x2003;In various embodiments, a secondary player may pay a fee for watching the games of primary players. A fee paid by the secondary player may allow the casino to profit from the secondary player even if the secondary player does not place any bets. The secondary player may pay a fee per game watched, per time period during which he watches, or based on any other metrics. In various embodiments, the primary player may receive a portion of the fee paid by the secondary player.</li>
        <li id="ul0002-0061" num="0296">&#x2003;In various embodiments, the primary player's permission must be obtained before a secondary player may track the play of the primary player.</li>
    </ul>
    </li>
    <li id="ul0001-0006" num="0297">2. Bet on a smaller aspect of someone else's game. For example, bet on what the next card will be, what the next roll of the dice will be, etc. In various embodiments, a person who does not directly participate in a game at a casino may nevertheless place bets on various events in the game. An event may include the rolling of a die, the drawing of a card, the spinning of a roulette wheel, the spinning of a reel of a slot machine, and so on. An event may come to a resolution in the form of a number revealed on the top face of a die, in the form of a rank or suit of a card drawn, in the form of a number achieved at a roulette wheel, in the form of a symbol appearing on a reel at a pay-line, and so on. An event may also include a decision or action made by a player who is directly involved in the game. For example, an event may include a player making a decision to hit or stand in blackjack, a player making a decision to bet or fold in poker, a player making a decision of which prize door to choose in a bonus round of a slot machine game, and so on. Such an event may come to a resolution in the form of an actual decision made. For example, a resolution may include an actual decision made by a player, such as &#x201c;hit&#x201d;, &#x201c;draw&#x201d;, or &#x201c;fold&#x201d;. An event may include a dealer making a decision in a game. For example, in a game of Pai Gow poker an event may include an arranging of the dealer's seven cards into a two-card hand and a five-card hand. The resolution of the event may take the form of an actual five-card hand and an actual two-card hand that the dealer has arranged.</li>
    <li id="ul0001-0007" num="0298">&#x2003;As used herein, the term &#x201c;payout odds&#x201d; may refer to a statement of an amount a player will receive, in the event of a win, per amount bet. For example, 3:2 payout odds means that a player will receive 3 units per 2 units bet (in addition to keeping his original bet), provided the player wins the bet. It will be understood that a payout ratio may be readily determined from payout odds and vice versa via mathematical operations. Therefore, it will be understood that embodiments described herein using payout ratios could readily be performed with payout odds, and vice versa.</li>
    <li id="ul0001-0008" num="0299">&#x2003;For a given event, an appropriate set of payout ratios may be determined. For example, if a secondary player is betting on a two as the resolution of a roll of a six-sided die, the secondary player may stand to win five times his initial wager (a payout ratio of 5) if the two is in fact rolled. Note that the player is assumed to give up his bet initially, so his net profit would be 4 times his initial wager if a two occurs. A set of payout ratios may be determined based on the inherent probabilities of various possible resolutions of the event. In the above example, the inherent probability of a two being rolled is 1/6. Thus, a payout ratio of five seeks to provide the player with a payout commensurate with the inverse of the probability of the resolution that would be winning for the player, while still allowing for a casino profit, on average.</li>
    <li id="ul0001-0009" num="0300">&#x2003;Once the event has resolved, it may be determined whether the secondary player has won. For example, suppose a secondary player has bet that the next card dealt in a game of poker will be the ace of spades. Once the next card has been dealt, it may be determined whether the card is in fact the ace of spades, and therefore whether the secondary player has won. If the secondary player has won, the secondary player may be paid according to the payout odds.</li>
    <li id="ul0001-0010" num="0301">&#x2003;In various embodiments, an event on which a secondary player bets does not constitute a complete game for the primary player of the game. For example, a secondary player may bet on what the next card will be in a game of video poker. However, the outcome of the game of video poker is not solely based on the next card, but rather is based on at least four other cards making up a complete hand of poker. Thus, a primary player may place a bet and may be paid based on his bet and based on the resolutions of a first and a second event in a game. A secondary player may place a bet on the same game and may be paid based on his bet and based on only the resolution of the second event in the game.</li>
    <li id="ul0001-0011" num="0302">&#x2003;In various embodiments, the secondary player may be remote from the game. For example, the primary player may participate in the game while physically present at a slot machine, video poker machine, table game, or other game location. However, the secondary player may be remote from the primary player, such as 50 feet away, such as in a different room, such as in a different building, such as in different city, and so on. In various embodiments, the secondary player may bet on an event in a game after the game has been completed. For example, the secondary player may bet on an event in a game completed the prior week. The events of the game may be unknown to the secondary player, since the secondary player may not have been observing or participating in the game when it was originally played.
    <ul id="ul0028" list-style="none">
        <li id="ul0028-0001" num="0303">2.1. Betting interface. In various embodiments, a secondary player may use a betting interface to make bets on events within a game. The betting interface may be a graphical user interface, and may include interactive features such as buttons, microphones, touch areas, mice, keyboards, and any other features for receiving designations of a secondary player's bet. An exemplary betting interface is shown in <figref idref="DRAWINGS">FIG. 9</figref>. The betting interface depicted in <figref idref="DRAWINGS">FIG. 9</figref> includes an area where the names of available primary players are listed. The secondary player may elect to bet on events for the games played by these primary players. Next to each primary player is listed an indication of the last event resolution. For example, next to primary player Robert Clemens is listed the J<img id="CUSTOM-CHARACTER-00001" he="2.79mm" wi="1.78mm" file="US07997973-20110816-P00001.TIF" alt="custom character" img-content="character" img-format="tif"/>, or the jack of spades. This indicates that in the most recent event of Robert Clemens' game, the event being the dealing of a card, the resolution to the event was that a jack of spades was dealt. Next to Sue Baker is listed a &#x201c;bar&#x201d;. This indicates that in the most recent event of Sue Baker's game, the event being the random determination of a symbol to show in a viewing window of a slot machine game, the resolution to the event was that a bar occurred. In the case of TeeBone, the most recent card dealt was the two of hearts. The betting interface depicted in <figref idref="DRAWINGS">FIG. 9</figref> includes two game windows in which a secondary player may bet on events within a game. In the game of TeeBone, the secondary player has just bet $5 that the next card dealt in the game will be a club. In the game of Sue Baker, two symbols have already appeared in the viewing window of the slot machine game in which Sue Baker is involved. The status of the game is such that the secondary player may bet on the third symbol that is yet to come in the same game of Sue Baker. The secondary player may use the &#x201c;Bet Menu&#x201d; area of the screen to select a symbol to bet on. At present, a &#x201c;cherry&#x201d; symbol appears in the Bet Menu area. The secondary player may, however, scroll through additional symbols in the menu and select (e.g., by touching three times in rapid succession) a symbol on which to bet.</li>
        <li id="ul0028-0002" num="0304">2.2. Determining pay tables. In various embodiments, payout ratios may be determined for an event within a game. Payout ratios may be based on the probability that a bet on the event becomes a winning bet. Payout ratios may also be determined based on a number of other factors. Payout ratios may be displayed or otherwise presented for a secondary player. In some embodiments, payout ratios are displayed in the form of a pay table. The pay table may include a first column depicting various possible resolutions of an event, and a second column depicting the amount to be paid per amount wagered on each of the possible resolutions.
        <ul id="ul0029" list-style="none">
            <li id="ul0029-0001" num="0305">2.2.1. Determining appropriate odds. In various embodiments, payout ratios may be determined based on a desired average amount to be won by a casino per bet received by the casino (e.g., based on a desired house advantage), on a house advantage of the game within which the event is occurring, and/or based on jurisdictional rules pertaining to allowable house advantages.
            <ul id="ul0030" list-style="none">
                <li id="ul0030-0001" num="0306">2.2.1.1. A desired house advantage. In various embodiments, a casino may determine a desired house advantage for a bet on an event in a game. It will be appreciated that the casino may determine any number of equivalent desired metrics, where such equivalent metrics may be determined through deterministic mathematical transformations of a house advantage. For example, a casino may equivalently determine a desired average amount that a player will win per unit wagered. Exemplary house advantages may be 15%, 10%, and 5%. The desired house advantage may be determined based on any number of factors, including perceptions as to what house advantages would be attractive to players while still providing the casino with adequate profits.</li>
                <li id="ul0030-0002" num="0307">2.2.1.2. Same as the gaming device. In various embodiments, a house advantage for an event within a game is determined based on the house advantage for the game itself. For example, the house advantage for a bet on an event in a game may be the same as for the house advantage for a bet on the game. In various embodiments, the house advantage for an event within a game may be close, but not identical to the house advantage of the game. For example, the house advantage of the event may differ by 2 percentage points from the house advantage of the game. Achieving identical house advantages may not be practical due, for example, to a requirement for integer payouts or to a limited number of possible resolutions of an event (e.g., there are only 6 resolutions to the roll of a die).</li>
                <li id="ul0030-0003" num="0308">2.2.1.3. Amount wagered. In various embodiments, the house advantage for an event within a game may be determined based on the amount bet on the event. In some embodiment, the greater the amount bet, the less the house advantage. This provides the player with an incentive to bet more.</li>
                <li id="ul0030-0004" num="0309">2.2.1.4. Jurisdiction minimum. In various embodiments, laws, rules, policies, or other conventions may dictate a maximum allowable house advantage for a gaming device. Accordingly, a house advantage for an event may be determined which is less than or equal to the maximum allowable house advantage.</li>
            </ul>
            </li>
            <li id="ul0029-0002" num="0310">2.2.2. Player preferences affecting the pay table. In various embodiments, an event in a game may have more than two possible resolutions. For example, the rolling of a die may have six possible resolutions, while the drawing of a card from a deck may have 52 possible resolutions. Payout ratios may be associated with each of the possible resolutions. Thus, a pay table may be formed for the event, where the pay table details payout ratios for one or more of the possible resolutions. In various embodiments, it may be possible to form many different pay tables for the same event. Further, many different pay tables may result in the same or similar house advantages. For example, a first pay table for a roll of a die may provide a payout ratio of 5 for a roll of a 6, and a payout ratio of 0 for any other roll. A second pay table for a roll of a die may provide a payout ratio of 3 for a roll of 6, a payout ratio of 2 for a roll of 5, and a payout ratio of 0 for any other roll. With the first pay table, the player may expect to win 5 times his wager with probability 1/6, yielding an expected payout of 5/6 times his wager, which yields a house advantage of (1&#x2212;5/6)/1=16.67%. With the second pay table, the player may expect to win 3 times his wager with probability 1/6, or two times his wager with probability 1/6, yielding an expected payout of 3/6+2/6=5/6. Thus, the second pay table has the same house advantage of 16.67%.
            <ul id="ul0031" list-style="none">
                <li id="ul0031-0001" num="0311">2.2.2.1. Player selects pay tables from range of pay tables. In various embodiments, a secondary player may select among various possible pay tables to use for an event. For example, when betting on the draw of a card, a secondary player may choose a pay table which pays 48 times an initial wager only if an ace of spades is drawn, or the secondary player may choose a pay table which pays 12 times an initial wager if any ace is drawn. In one embodiment, a secondary player may choose between a pay table which provides a relatively high payout with a relatively low probability and a pay table which pays a lower payout or payouts, but with greater probability. Over a set of repeated games, the former pay table would tend to provide less frequent but greater rewards, while the latter pay table would tend to provide more frequent but smaller rewards. A secondary player might therefore decide on his preferred method of receiving rewards. A secondary player may be given the opportunity to select among a range or continuum of possible pay tables, each with approximately the same house advantage, but each having different maximum payouts and/or different frequencies for providing payouts. A player may select a pay table by selecting a maximum payout. Typically, though not necessarily always, a pay table with a relatively higher maximum payout ratio will tend to pay less frequently than does a pay table with a relatively lower maximum payout ratio. A player may also select a pay table based explicitly on a payout frequency associated with a pay table. In some embodiments, the player may adjust a dial, where one limit on the dial is associated with a pay table with one or more relatively high payouts and a relatively low frequency of payout, and an opposite limit of the dial is associated with a pay table with one or more relatively low payouts and a relatively higher frequency of payout.</li>
            </ul>
            </li>
            <li id="ul0029-0003" num="0312">2.2.3. Determining odds of a particular symbol in a slot machine on a reel. In some embodiments, a player may bet on the occurrence of a particular symbol or indicium during a game. In some embodiments, the probability of occurrence of a symbol may be determined. In some embodiments, the probability of occurrence of a symbol at a particular position may be determined. For example, the probability of occurrence of a particular symbol in the first position across a pay-line of a slot machine may be determined. The determination of a probability of occurrence of a symbol or of a symbol at a particular location may allow the determination of a payout ratio that is commensurate with the probability. For instance, if the probability is determined to be lower, then the payout ratio may be set relatively higher, and vice versa.
            <ul id="ul0032" list-style="none">
                <li id="ul0032-0001" num="0313">2.2.3.1. Monte Carlo. In some embodiments, the probability of occurrence of a particular symbol may be determined through a large number of trials, where each trial may include the playing of a game, or a simulated game. The game may be played at an actual gaming device, at a table game, or on a computer executing game software. The game may be played or run with actual money at risk (e.g., in the form of bets) or with no money at risk. For example, a game at a slot machine may be played ten thousand times. A program may track statistics of interest from the game, such as how often a &#x201c;cherry&#x201d; symbol occurred in the first position of the pay-line, how often a &#x201c;bar&#x201d; symbol occurred in general, and so on. The probability that a symbol occurs at a particular location on a pay-line may then be determined as the number of trials in which the symbol occurred at the particular location divided by the number of trials. Analogously, the probability of any an event coming to a particular resolution can be determined or estimated through a large number of trials in which the event occurs, and measuring the proportion of the trials in which the particular resolution occurred.</li>
                <li id="ul0032-0002" num="0314">2.2.3.2. Going through virtual pay table. In some embodiments, the probability of occurrence of a particular symbol at a particular location on a pay-line may be deduced with reference to an internal algorithm used by a gaming device for generating game outcomes. In some embodiments, the algorithm used may employ one or more &#x201c;virtual reels&#x201d;. A virtual reel may comprise a table with one column of outcomes (e.g., a set of symbols), and with one column of ranges of numbers, each range of numbers corresponding to an outcome. A random number generator may generate a random number. The random number may then be matched to an outcome from the virtual reels based on the range of numbers in which the random number falls. Each outcome may thus be assumed to have a probability of occurrence that is proportional to the size of the corresponding range of numbers. For example, an outcome with a corresponding range of numbers of 100-299 is twice as likely to occur as an outcome with a corresponding range of numbers of 300-399, since the first range includes 200 numbers that may be generated by the random number generator, and the second range includes only 100 numbers that may be generated by the random number generator. With reference to the virtual reel, the probability of occurrence of each possible outcome may be determined. Then, the probabilities of all outcomes which include a particular symbol may be added up, thus yielding the probability of the occurrence of that symbol in a game. The probabilities of all outcomes which include a symbol in a particular location may similarly be added to determine the probability of occurrence of that symbol at that particular location. For example, to determine the probability that a &#x201c;bell&#x201d; symbol occurs at position <b>3</b> in an outcome, the probabilities of occurrence of all outcomes containing the &#x201c;bell&#x201d; symbol at position <b>3</b> may be added.</li>
            </ul>
            </li>
            <li id="ul0029-0004" num="0315">2.2.4. Odds of a particular card. In various embodiments, the probability that a particular card will constitute the resolution of a particular event may be determined as follows. First, the number of unknown or unrevealed cards may be determined. Unknown cards may include cards that have not already been shown face-up in a game. Provided the card of interest has not already been shown, the probability may be determined to be equal to one divided by the number of unknown cards.</li>
        </ul>
        </li>
        <li id="ul0028-0003" num="0316">2.3. Distinguishing between two dice. In various embodiments, a secondary player may wish to place a bet that would have an ambiguous resolution during conventional play of a game. For example, a secondary player may wish to bet that a particular die in a game of craps will show a six. However, the way craps is often played conventionally, it may be difficult or impossible to distinguish between the two dice used in a game. Thus, once the two dice land following a roll, it might conventionally be ambiguous as to which was the die that the player bet on.
        <ul id="ul0033" list-style="none">
            <li id="ul0033-0001" num="0317">2.3.1. Distinguishing two otherwise similar objects. In various embodiments, two or more similar objects used in the play of a game may be made to appear distinct. In a game of craps, two dice may be colored differently. For example, one die may be colored green, while the other is colored red. In this way, a secondary player would be able to bet on either the red die or the green die without worry of an ambiguous result. In a game with three dice, such as in Sic Bo, there may be three dice of different colors. In a game of roulette involving the use of two balls at once, the two balls may include different patterned markings. A player may thereby bet on, e.g., the striped ball or the spotted ball. In some embodiments, two or more similar objects may be made detectably distinct, even if the distinction cannot be made visually. For instance, radio frequency identification (RFID) tags may be placed in or on objects. Two dice with different RFID tags inside them would be distinguishable by an RFID tag reader from the differing signals coming from the tags.</li>
            <li id="ul0033-0002" num="0318">2.3.2. Bet that the lower die will be above two. In some embodiments, a secondary player may place a bet on a resolution of one of several events, in which the one event becomes distinguishable only after all of the events have been resolved. For example, a secondary player bets that the higher of two dice rolled in a game of craps will show a 6. In this example, two events may be deemed to occur, each event constituting the rolling of a die. However, the actual die a player is betting on becomes clear only after both events have resolved. In other words, only after both dice have been rolled and have come to rest can it be determined which is the higher die. A secondary player may, in some embodiments, bet on the lower of two dice, on the middle die (e.g., in a game with three dice), on the roulette ball showing the highest number, and so on. In various embodiments, a secondary player's bet may comprise at least two parts. The first part may be a method to distinguish between two or more events to determine which of the two or more events the secondary player is betting on. The second part may be an indication of what will constitute a winning or losing resolution for the secondary player. For example, suppose that a secondary player bets that the higher of two dice will show a five. The first part of the bet is a way to distinguish the rolling of one die from the rolling of the other die, and indicating which of the now distinct events the secondary player has bet on. The second part of the bet indicates that a winning resolution will be for the die that the player has bet on to show a five.</li>
            <li id="ul0033-0003" num="0319">2.3.3. Specify a position of a card. For example, the third card drawn is the Ace of spades. In some embodiments, in order to clarify the specific event that a secondary player is betting on, a position, location, sequence number, or other clarification may be specified. For example, rather than betting that &#x201c;a&#x201d; card will be an ace of spades, a secondary player may bet that &#x201c;the third card dealt&#x201d; will be an ace of spades. In a game of video poker, a secondary player may bet that a card in a specified position in a video poker hand (e.g., the fourth card in the final hand), will be of a certain rank and suit. In a game of a blackjack, a secondary player may bet, for example, on the first card dealt to a player, the second card dealt to a player, the third card dealt to a player, etc. The secondary player may also bet, for example, on the first card dealt to the dealer, the second card dealt to the dealer, etc. The player may also specify an event by means of an orientation. For example, in a game of blackjack, the secondary player may bet on the dealer card that is face down, or on the dealer card that is face up.</li>
        </ul>
        </li>
        <li id="ul0028-0004" num="0320">2.4. Receive aids in your prediction. In various embodiments, a secondary player may be provided with data, hints, or other aids in making bets on an event in a game. Data may include historical data relevant to the game at hand. For example, if a secondary player is to bet on the decision that will be made by a primary player, data about the decision of the primary player in prior games might aid the secondary player in his bet.
        <ul id="ul0034" list-style="none">
            <li id="ul0034-0001" num="0321">2.4.1. The sequence of what occurred in the past. In various embodiments, a secondary player may be shown or otherwise provided with data from games or events within games that were played prior to the game that includes the event on which the secondary player is betting. The data may help the secondary player to choose a resolution of the event which will constitute a winning resolution. A secondary player who is to bet on a particular event in a particular game played by a particular primary player may be shown data about other events that have occurred. Other events may include events that have occurred: (a) in games played by the same particular primary player; (b) in games under similar circumstances to those which are present in the particular game (e.g., the same initial two cards occurred in a prior game of blackjack as have in the particular game, and the particular event of interest is the dealing of the third card in the particular game); (c) in games played at the same gaming device that the particular game is or was played at; (d) in the recent past (e.g., events that have occurred in the five minutes prior to the time that the secondary player bets on the particular event); (e) just prior to when the particular event originally occurred (e.g., events occurring in games that had been played in the five minutes prior to the particular game); (f) in games played at the same gaming device that the particular game is or was played at, where such games constitute a sequence of games that immediately preceded the particular game (e.g., such games were the five games played before the particular game); and (g) in games played by the same particular primary player, where such games constitute a sequence of games that the primary player played immediately preceding the particular game.</li>
            <li id="ul0034-0002" num="0322">2.4.2. What would perfect strategy be here? In various embodiments, a secondary player may be provided with an indication of a decision that would be made according to some strategy. For example, if a secondary player is betting on the decision that will be made by a primary player in a game of blackjack, the secondary player may be shown what decision would be made using Basic Strategy (i.e., the strategy used to maximize expected winnings without any special knowledge of what cards have already been dealt). For example, the secondary player may be told that the proper decision according to Basic Strategy is for the primary player to hit. As another example, if a secondary player is betting on what cards will be discarded by a primary player in a game of video poker, the secondary player may be told which combination of discards would maximize the expected winnings for the primary player. In various embodiments, the secondary player may be told what decision would be made according to a strategy that is not a perfect or optimal strategy. For example, a secondary player might be told which decision would be made according to a strategy that aims for the highest payout in a game.</li>
            <li id="ul0034-0003" num="0323">2.4.3. What has this player done in similar situations? In various embodiments, a secondary player may be provided with an indication of what decisions a primary player has made in situations which are similar to the situation of the game in which the secondary player is participating. Games in which a primary player was in a similar situation may include games in which the primary player: (a) had the same cards; (b) had the same point total (e.g., in a game of blackjack); (c) had the same hand ranking (e.g., in a game of poker); (d) had the same sequence of initial events (e.g., in a game of craps, the primary player had the same three initial rolls as he does in the game situation under consideration); (e) was in the same seat position (e.g., the primary player was just to the left of the dealer); (f) faced the same opponent or opponents; (g) was at the same gaming device; (h) faced the same bet or bets from opponents (e.g., in a game of poker, the primary player may have faced the same bets that he does at present); and so on. Games in which the primary player was in a similar situation may include games in which the dealer had a similar hand (e.g., in a game of blackjack, the dealer had the same card showing), or games in which an opponent of the primary player had a similar card to what the primary player's opponent has in the game under consideration. In some embodiments, the secondary player may be provided with an indication of what the primary player did in games with similar external contexts, such as games played at the same time of day, games played at the same table, games played at the same casino, games played just after a big loss for the primary player, and so on.</li>
            <li id="ul0034-0004" num="0324">2.4.4. What cards have been dealt already? In various embodiments, a secondary player may be provided with an indication of what cards have already been dealt in a game. For example, in a game of blackjack, the secondary player may be told what cards have been dealt from a deck in prior games where the deck was used. If, for example, the secondary player thinks the primary player has been counting cards, the secondary player may use information about prior cards dealt in order to predict the reaction by the primary player to the card count. In a game of poker, the secondary player may have the opportunity to view cards that have been dealt, e.g., as part of an initial hand. Looking at the cards of the initial hand may then help the secondary player to better predict a primary player's decision.</li>
            <li id="ul0034-0005" num="0325">2.4.5. The secondary player is provided with a probability. In various embodiments, a secondary player may be provided with the probability of a particular resolution to an event. For example, if the secondary player is betting on the roll of a die, the secondary player may be told that the probability of a six being rolled is 1/6.</li>
            <li id="ul0034-0006" num="0326">2.4.6. Regulatory requirements for hints. In various embodiments, regulations may dictate whether or not a hint must be provided. In some embodiments, regulations may dictate that the probability of a resolution be provided. In some embodiment, regulations may require that a secondary player be given a probability that an event comes to a particular resolution if there would be no way for the secondary player to know such a probability. For example, while it is possible for a secondary player to know the probability that a 6-sided die will land in a certain way, a secondary player may have no way of knowing that a reel of a slot machine will display a certain symbol since the reel may be controlled by a secret algorithm. In some embodiments, regulations may dictate that a hint not mislead a secondary player. For example, in game of video poker, a hint inform a secondary player of a decision that would be made by a primary player using a particular strategy. However, the strategy may not be a strategy that would typically be employed by any player, and thus the hint would not likely give the secondary player the proper direction. In some embodiments, regulations may dictate the form in which a hint must be provided. Regulations may require that a hint be given in multiple languages. Regulations might require that a player have the option of which language will be used to view the hint.</li>
            <li id="ul0034-0007" num="0327">2.4.7. Form of hints (for example, secondary players are simply not allowed to make certain bets). In some embodiments, a hint may take the form of preventing a secondary player from making certain bets. Such bets may be disadvantageous for the secondary player or for the casino. For example, a graphical user interface may display options for what resolutions the secondary player can bet on. In a game of blackjack, such options may include a &#x201c;hit&#x201d; option for betting that a primary player will hit, a &#x201c;stand&#x201d; option for betting that a primary player will stand, and a &#x201c;double down&#x201d; option for betting that a primary player will double down. If the primary player has been dealt an initial hand with a point total of 10, then the &#x201c;stand&#x201d; option may be grayed out such that the secondary player cannot bet that the primary player will stand. This is because it would make no sense for the primary player to stand when the primary player can hit, increase his point total, and have no risk of busting.</li>
        </ul>
        </li>
        <li id="ul0028-0005" num="0328">2.5. Setting the odds on an event. In some embodiments, the casino may set the payout odds on an event by reference to historical data. Historical data may be used to arrive at a probability of a resolution of an event. For example, historical data may be used to determine the probability with which a primary player will make a particular decision in a game. This probability may be used, in turn, to provide payout odds to a secondary player who wants to bet that the primary player will make the particular decision.
        <ul id="ul0035" list-style="none">
            <li id="ul0035-0001" num="0329">2.5.1. Data not including the current game. In some embodiments, the casino may use data from historical games of primary players in order to determine a probability that a primary player will make a particular decision. For example, the casino may examine a set of historical games in which various primary players had hands with 16 points against a dealer's 10 points showing. The casino may determine the number of primary players who hit and the number of primary players who stood in order to arrive an estimated probability for what a primary player will do in a particular game under consideration. For example, the casino may look at 100 historical games and may find that 45 times the primary player hit, and 55 times the primary player stood. Thus, the casino may determine that there is a 45% chance that a primary player will hit and a 55% chance that a primary player will stand under a similar situation. Once the casino has an estimate of the probabilities of various outcomes, the casino may set payout odds in order to create a positive house advantage. For example, in the aforementioned example, the casino may set payout odds of 1:1 if the secondary player bets on &#x201c;hit&#x201d;, and 3:4 odds if the secondary player bets on stand. In various embodiments, historical data may include data about historical games of the primary player who is involved in the particular game in question. For example, to determine the probability that a particular primary player will make a decision, the casino may look at historical data for that primary player.</li>
            <li id="ul0035-0002" num="0330">2.5.2. Data including the current game. In some embodiments, payout odds may be set for a game based on a set of games which include that game. For example, the casino may use a set of games that include X (e.g., 1000) games in which a player had a pair of nines and the dealer showed an 8 in a game of blackjack. The casino may determine how many times the player with the nines split, and how many times the player just stood. The casino may thus know, with certainty, the probability that the nines would be split and the probability that the primary player would stand for a game randomly selected from the set of X games. Accordingly, the casino could then set payout odds for a bet on standing and a bet on splitting. The casino could set such payout odds in order to create a positive house advantage. The casino may then allow a secondary player to bet on a decision of a primary player in a game from the set of 1000 games, such as from a randomly selected game of the set of 1000 games.</li>
        </ul>
        </li>
        <li id="ul0028-0006" num="0331">2.6. Bet on a random action in the game. In various embodiments, a secondary player may bet on the resolution of any desired event. For example, in a table game of craps, the secondary player may bet that one die will bounce off the table. In a game of poker, the secondary player may bet that one of the primary players will throw his cards, that a primary player will get ejected from the game, that a primary player will bet out of order, or that any other resolution to an event will occur. In some embodiments, a secondary player may bet on any resolution that is external to the normal play of a game. For example, the secondary player may bet that a player will spill a drink at a gaming table.</li>
        <li id="ul0028-0007" num="0332">2.7. Bet on a particular sub-outcome. There are many events on which a secondary player may bet. For each event, there may be one or more resolutions on which the secondary player may bet.
        <ul id="ul0036" list-style="none">
            <li id="ul0036-0001" num="0333">2.7.1. blackjack. In a game of blackjack a secondary player may bet on: (a) the rank or suit of a particular card, such as the first, second, third, etc. player card or the first, second, third, etc. dealer card; (b) a decision that will be made by a primary player (e.g., hit, stand); (c) a decision that will be made by a dealer; (d) whether a primary player will bust; (e) whether a dealer will bust; (f) whether the primary player will receive two identical cards; (g) whether the primary player will receive two or more cards of the same suit; (h) whether two primary players in a game receive the same cards; (i) a starting point total for a primary player; (j) a starting point total for a dealer; (k) whether a primary player's ending point total will fall within a particular range; and so on.</li>
            <li id="ul0036-0002" num="0334">2.7.2. Roulette. In a game of roulette, a secondary player may bet on (a) red; (b) black; (c) a particular number; (d) a particular range of numbers; (e) the occurrence of a number in a particular sector of a wheel; (f) an amount that a primary player will bet; (g) a number that a primary player will bet on; (h) green; and so on.</li>
            <li id="ul0036-0003" num="0335">2.7.3. Slot machines. In a slot machine game a secondary player may bet on: (a) the occurrence of a symbol on a reel; (b) the occurrence of a set of symbols on a set of reels (e.g., the secondary player bets that the first reel will show a &#x201c;bar&#x201d; and the second reel will show a &#x201c;lemon&#x201d;); (c) whether a bonus round will be reached; (d) the level of a bonus round that will be reached; (d) a decision that a primary player will make in a bonus round; (e) a resolution of a bonus round (e.g., how much money the primary player will win from the bonus round); (f) the amount that the primary player will bet; (g) the number of pay-lines that the primary player will bet; (h) the number of pay-lines that will win, and so on.</li>
            <li id="ul0036-0004" num="0336">2.7.4. Card Games. In a card game, such as a game of poker, a secondary player may bet on: (a) the occurrence of a particular card in a hand of cards; (b) the occurrence of a particular combination of cards in a hand of cards (e.g., the occurrence of a pair); (c) an order in which cards are dealt (e.g., the secondary player may bet that each card dealt will have a higher rank than the last card dealt); (d) a position in which a card will be dealt (e.g., an ace will be dealt as the first card in a player's hand; and so on.
            <ul id="ul0037" list-style="none">
                <li id="ul0037-0001" num="0337">2.7.4.1. Poker. In a game of poker, a secondary player may bet on what bets will be made by primary players in the game. A secondary player may bet on whether a bet will be a check, call, bet, raise, or fold; on how much a primary player will bet; on how many callers there will be for a bet or raise; on how many times a pot will be raised; on how many rounds of betting there will be; on how many players will be all-in; and so on. In some embodiments, a secondary player may bet on the total size of a pot. In some embodiments, a secondary player may bet on whether there will be a tie. In some embodiments, a secondary player may bet on the size of a side-pot.</li>
            </ul>
            </li>
            <li id="ul0036-0005" num="0338">2.7.5. Dice Games. In a game of dice, a secondary player may bet on one roll of the dice. For example, the secondary player may bet that two dice rolled will total to 12. In a game of Sic Bo, a player may bet that one of the three dice rolled will show a 4.</li>
        </ul>
        </li>
        <li id="ul0028-0008" num="0339">2.8. Bet on length of the game. In various embodiments, a secondary player may bet on the length of a game.
        <ul id="ul0038" list-style="none">
            <li id="ul0038-0001" num="0340">2.8.1. Time. A secondary player may bet on the time that a game will last. A game may be counted to start when a primary player makes a bet, when a first random event occurs in a game, when a first card is dealt, when a first roll of the dice is made, when a first player decision is made, and so on. A game may be counted to end when a payout is made, when a player's bet is collected, when a last random outcome is generated, when objects used in a game are collected (e.g., when cards are collected), when a payout is announced), or when a subsequent game starts.</li>
            <li id="ul0038-0002" num="0341">2.8.2. Number of cards required. In some embodiments, a secondary player may bet on the number of cards that will be dealt in a game. A secondary player may bet on the number of cards that will be dealt to a particular hand (e.g., to a player hand in blackjack; e.g., to a dealer hand in blackjack); or to a particular combination of hands (e.g., to the hands of both the player and the dealer; e.g., to three players in a game of blackjack). A secondary player may bet on the number of cards that will be dealt as common cards. For example, regarding a game of Texas Hold'em, the secondary player may bet that all five common cards will be dealt. In other words the secondary player may bet that at least two people will remain in the game until the fifth common card is dealt.</li>
            <li id="ul0038-0003" num="0342">2.8.3. Number of rolls of dice required. In various embodiments, a secondary player may bet on the number of rolls of dice that will occur in a game. For example, a secondary player may bet that there will be seven rolls of dice in a game of craps. In other words, the secondary player may bet that the primary player will set a point and then take six additional rolls to either roll the point number again or achieve a seven.</li>
            <li id="ul0038-0004" num="0343">2.8.4. Number of bonus round levels reached. In various embodiments, a secondary player may bet on the number of levels that a primary player will reach in a bonus round, e.g., in a bonus round of a slot machine game. A bonus round may have a plurality of separate levels. If a primary player does well in earlier levels, e.g., by correctly choosing the location of hidden treasures, the primary player may make it to later levels. However, if the primary player does poorly in earlier levels, the primary player may not reach later levels. Thus, the number of levels reached in a bonus round may be effectively random. In some embodiments, a secondary player may bet on the number of spaces a character will advance on a game board in a bonus round. For example, regarding a bonus round in a game of Monopoly&#xae;, a secondary player may bet on the number of spaces that a game character will traverse on the game board. In some embodiments, a secondary player may bet on the space or spaces on which a game character will land in a game. For example, a secondary player may bet that a game character will land on Boardwalk in a game of Monopoly&#xae;.</li>
        </ul>
        </li>
        <li id="ul0028-0009" num="0344">2.9. Bet on a different game within the game. E.g., bet on poker within blackjack. In some embodiments, a secondary player may bet on the occurrence of an outcome from a first game, but in the context of a second game. For example, a secondary player may bet that a primary player who is involved in a game of blackjack will receive cards that create a poker hand which is three-of-a-kind. In a game of Sic-bo, a secondary player may bet that two of three dice used will form a winning roll in a game of craps.</li>
        <li id="ul0028-0010" num="0345">2.10. Bet on the order in which people will remain in the game. Various games include multiple primary players. In some multi-player games, players may be eliminated or may drop out of the games. For example, in a game of poker, players may drop out of the game as they fold. In various embodiments, a secondary player may bet on the manner in which primary players are eliminated.
        <ul id="ul0039" list-style="none">
            <li id="ul0039-0001" num="0346">2.10.1. Who will be the first one out? In various embodiments, a secondary player may bet on which primary player will be the first primary player eliminated. A secondary player may bet on who will be the second primary player eliminated, the third primary player eliminated, or who will be the primary player eliminated in any other spot.</li>
            <li id="ul0039-0002" num="0347">2.10.2. Who will be the last two standing? In various embodiments, the secondary player may bet on which primary player will be the last one remaining. The secondary player may bet on who will be the second to last primary player remaining, who will be the third to last remaining, and so on. The secondary player may bet on who will be the last two primary players remaining. In various embodiments, the secondary player may bet on any combination of primary players and on any combination of places (e.g., last, second to last) in which primary players are eliminated. The secondary player may win the bet if the designated combination of primary players was eliminated in the designated combination of places. A secondary player may bet that a particular three primary players will be the last three remaining, regardless of the order in which they are eliminated after the final three. In some embodiments, the secondary player may bet not only that a particular group of primary players will be the last three remaining, but also on the order in which the last three will be eliminated (e.g., players A, B, and C will be the last three, player A will be the last, and player B will be the second to last remaining).</li>
            <li id="ul0039-0003" num="0348">2.10.3. Who will be the three in after the flop? In various embodiments, a secondary player may bet on the number of primary players that will be remaining in a game at a certain point in the game. For example, a secondary player may bet on the number of primary players that will be remaining by the flop in a game of Texas Hold'em poker, or by fifth street in a game of seven-card stud poker. A secondary player may bet on how many primary players will be remaining in a game after X number of cards have been dealt in the game, regardless of whom the cards have been dealt to. A secondary player may bet that a particular primary player will remain in a game at a certain point in the game. For example, a secondary player may bet that primary player Joe Smith will be remaining in the game after the flop.</li>
            <li id="ul0039-0004" num="0349">2.10.4. Which three people won't bust? In various embodiments, a secondary player may bet on a combination of people who will bust in a game of blackjack. For example, a secondary player may bet that, of a particular group of three primary players in a game of blackjack, all will bust. A secondary player may bet that one player will not bust. A secondary player may bet that of a group of primary players, none will bust during a game.</li>
        </ul>
        </li>
        <li id="ul0028-0011" num="0350">2.11. Bet on what the primary player himself will do. In some embodiments, a secondary player may bet on a decision that will be made by a primary player in a game.
        <ul id="ul0040" list-style="none">
            <li id="ul0040-0001" num="0351">2.11.1. The primary player will hit here. In some embodiments, a secondary player may bet on a decision that a primary player will make in a game of blackjack. A secondary player may bet that a primary player will do one or more of the following: (a) hit; (b) stand; (c) surrender; (d) split; (e) double down; (f) take insurance.</li>
            <li id="ul0040-0002" num="0352">2.11.2. The primary player will draw to the flush. In some embodiments, a secondary player may bet on a strategy that a primary player will employ in a game of video poker. The strategy may be specified with a specification of which cards a primary player will discard. For example, the secondary player may specify that the primary player will discard the first, third, and fourth cards from a starting hand. In some embodiments, the secondary player may specify one or more cards that will be discarded while not excluding the possibility that additional cards might be discarded. For example, the secondary player may specify that the primary player will discard the second card in his hand. The secondary player may then win his bet if the primary player discards the second card, regardless of other cards that the primary player might discard. A secondary player may specify the strategy of a primary player in terms of a goal attributable to the strategy. For example, the secondary player might specify that the primary player will &#x201c;draw to a flush&#x201d; or &#x201c;draw to a straight&#x201d;.</li>
            <li id="ul0040-0003" num="0353">2.11.3. How much will the primary player bet? In some embodiments, a secondary player may bet on the amount that a primary player will bet. For example, the secondary player may bet that a primary player will bet $5 in a slot machine game. For example, the secondary player may bet that the primary player will raise by $25 in a game of poker.</li>
            <li id="ul0040-0004" num="0354">2.11.4. What bet will the primary player make? In various embodiments, a secondary player may bet on a particular bet that a primary player will make in a game. For example, in a game of craps, there are many possible bets that a primary player can make, including a pass bet a don't pass bet, an &#x201c;any seven&#x201d; bet, an &#x201c;any eleven&#x201d; bet, a &#x201c;horn bet&#x201d;, and so on. The secondary player may bet on which of these, or other possible bets, the primary player will make.</li>
            <li id="ul0040-0005" num="0355">2.11.5. Which pay-lines will the primary player activate? In various embodiments, a secondary player may bet on whether or not a primary player will bet on a particular pay-line at a gaming device. For example, a gaming device may have three pay-lines. A secondary player may bet that the primary player will bet on the third pay line.</li>
            <li id="ul0040-0006" num="0356">2.11.6. Bet on primary players' heart rate, breathing, and other bio signatures. In various embodiments, a secondary player may bet on a vital sign of a primary player. The secondary player may bet on the heart rate, breathing rate, blood pressure, skin conductivity, body temperature, pupil dilation, muscle tension, or any other indicator tied to the primary player. For example, the secondary player may bet that the peak heart rate of a primary player will be 120 during a game of poker. For example, a secondary player may bet that a primary player will take 5 breaths in the next minute. The secondary player, by betting on the vital signs of a primary player, may indirectly bet on the stress level of a game and/or the primary player's response to stressful stimuli.</li>
            <li id="ul0040-0007" num="0357">2.11.7. When will the primary player stop playing? Now? After five games? In various embodiments, a secondary player may bet on the length of a playing session of a primary player. The length may be measured in terms of time, the number of games played, the number of bets made, the number of cards dealt during a session, the number of times dice are rolled, or in terms of any other metric. For example, a secondary player may bet that a primary player will play five more games before quitting. For example, a secondary player may bet that a primary player will play for 40 more minutes before quitting. A session may be defined as having ended after: (a) a primary player has stopped playing for X amount of time; (b) a primary player has left the location of a game; (c) a primary player has cashed out; (d) a primary player has exchanged chips for money; (e) a primary player has run out of money; and so on.</li>
            <li id="ul0040-0008" num="0358">2.11.8. What drink will the primary player order? In various embodiments, a secondary player may bet on a service that the primary player will receive. A secondary player may bet on a drink a primary player will order, on the type of food the primary player will order, on the price of a primary player's food or drink, on the amount that a primary player will tip a casino representative, and so on.</li>
            <li id="ul0040-0009" num="0359">2.11.9. How many pulls will the primary player complete in an hour? In various embodiments, a secondary player may bet on the speed with which a primary player plays. A secondary player may bet on: (a) the number of handle pulls that a primary player makes in an hour or in any period of time; (b) the time between two handle pulls; (c) the time between the start of two games of blackjack; (d) the time between the placing of a bet in a game and the time of the provision of a payout; and so on.</li>
            <li id="ul0040-0010" num="0360">2.11.10. Any combination of what primary players will do. For example, five primary players split. In various embodiments, a secondary player may bet on any combination of decisions that will be made by primary players in a game. For example, a secondary player may bet that at least 3 primary players will split in a game of blackjack; a secondary player may bet that a particular group of three primary players will split in a game of blackjack; a secondary player may bet that exactly three primary players in a game of blackjack will hit and that exactly one will split; and so on. Regarding a game of poker, a secondary player may bet that exactly two primary player will call a particular bet. In various embodiments, a secondary player may bet that certain decisions will or will not be made without regard to who makes the decisions. For example, regarding a game of poker, a secondary player may bet that one primary player will bet and that three primary players will call, without specifying which primary players will be the ones to bet and call. The secondary player may win his bet if any primary player bets and if any three primary players call.</li>
        </ul>
        </li>
        <li id="ul0028-0012" num="0361">2.12. Bet only on the third pay-line. Unlike the primary player, the secondary player does not have to bet on pay-lines 1 and 2 before betting on pay-line 3. In various embodiments, a secondary player may bet on an event in isolation on which the primary player was not allowed to bet in isolation. For example, the secondary player may bet on only the third pay-line of a slot machine. However, the primary player may have been required to bet on the first and second pay-lines at the slot machine before he could bet on the third pay-line. In a game of craps, a secondary player may be allowed to make an odds bet even without making a pass-line bet. Often, a primary player must first make a pass-line bet before making an odds bet.</li>
        <li id="ul0028-0013" num="0362">2.13. Bet on what ad shows on the gaming device. In various embodiments, a secondary player may bet on an advertisement that will be displayed on a gaming device. In various embodiments, a gaming device may display an advertisement. In various embodiments, a gaming device may display an advertisement occasionally or periodically. An advertisement may be displayed at random or according to a schedule that is unknown to the secondary player. Accordingly, the secondary player may bet on what advertisement will be shown at a gaming device. For example, a secondary player may bet that an advertisement for vitamin water will be displayed on a gaming device. An advertisement may take the form of text, a still image, a video, or any other output that serves to promote a product or service, either directly or indirectly. A secondary player may specify a bet on an advertisement by specifying the product that will be promoted. For example, a secondary player may specify that Triscuit crackers will be advertised. A secondary player may specify a bet in terms of a general product category, such as crackers or snack foods. A secondary player may specify a bet on an advertisement by specifying a brand for a product or a name of a manufacturer for a product. In some embodiments, a secondary player may specify a bet on an advertisement through a multiple choice selection, where the secondary player may specify from among multiple possible different products to bet on. In some embodiments, a secondary player may bet on the time until the next advertisement. In some embodiments, a secondary player may bet on when the next advertisement for a particular product will be.</li>
        <li id="ul0028-0014" num="0363">2.14. Combine sub-outcomes from several games to form larger outcomes. In some embodiments, a secondary player may bet on the outcome of a game which is created synthetically using events from more than one game. For example, synthetic game may be created for the secondary player using a first set of cards that was dealt in a first game for a primary player, and a second set of cards that was dealt in a second game for the primary player. As another example, a synthetic game may be created using a first roll of two dice from a first craps game, and a second roll of two dice from a second craps game. As another example, a synthetic slot machine game may be created using the symbol appearing on reel <b>1</b> in a first game, the symbol appearing on reel <b>2</b> in a second game, and the symbol appearing on reel <b>3</b> in a third game. If, for example, all three symbols are &#x201c;cherry&#x201d;, then the secondary player may be paid as if all three cherries had occurred on the same spin on adjacent reels.</li>
        <li id="ul0028-0015" num="0364">2.15. Bet on a machine malfunction, or coin refill. In various embodiments, a secondary player may bet on the occurrence of a machine malfunction. For example, a secondary player may bet that a machine will malfunction within the next hour. In various embodiments, a secondary player may bet that a gaming device will need a coin refill. For example, the secondary player may bet that a gaming device will need a coin refill within the next 10 minutes.</li>
        <li id="ul0028-0016" num="0365">&#x2003;Embodiments described herein with respect to complete games or outcomes may similarly apply to events within a game. For example, just as a secondary player may search for games having particular characteristics, a secondary player may search for events within a game having particular characteristics, or a secondary player may search for games with particular characteristics so as to bet on events within such games. A secondary player may search for particular primary players and bet on events within the games of such primary players.</li>
        <li id="ul0028-0017" num="0366">&#x2003;In some embodiments, a secondary player may seek to view historical or current games. The secondary player may desire to participate in the games. The secondary player may, in some embodiments, perform a search for games which satisfy a first set of criteria. For example a secondary player may search for games which were played by a particular primary player. The search may yield a plurality of games. The games may then be sorted using a second set of criteria. The plurality of games may be sorted according to: (a) the time at which the games were played (e.g., the games may be sorted from the most recently played to the one played the furthest in the past); (b) the amounts won in the games (e.g., the games may be sorted from the game with the highest payout to the game with the lowest payout); (c) the amounts bet on the games; (d) the rankings of hands dealt in the games (e.g., games of poker may be sorted according to the poker ranking of the initial hand; e.g., games of blackjack may be sorted according to the point total of the final hand); (e) the results of the games (e.g., the primary player won; e.g., the dealer won); (f) the initial number rolled on a die in each game of the games; (g) the location in which the games were played (e.g., games may be sorted according to the floor in the casino where the games were played); (h) the name of the gaming devices on which the games were played (e.g., games may be sorted such that the gaming devices on which the games were played are in alphabetical order); (i) the name of the primary players who initially played the games; (j) the number of secondary players who participated in each of the games; and so on.</li>
        <li id="ul0028-0018" num="0367">&#x2003;Any physical game described herein may be implemented electronically in various embodiments. For example, embodiments pertaining to the play of blackjack at a physical card table may pertain as well to a game of blackjack played over an electronic network. For example, a primary player may play blackjack using a video blackjack device. As another example, a primary player may play blackjack over the Internet. A secondary player may bet on the outcomes of the game of the primary player and/or on events within the game of the primary player.</li>
        <li id="ul0028-0019" num="0368">&#x2003;In various embodiments, a secondary player may participate in the game of a primary player, but take the game in a different direction from the direction in which the primary player took the game. For example, the primary player may be involved in a game which requires a decision on the part of the primary player. The primary player may make a first decision in the game. The secondary player, meanwhile, may be participating in the game, but may prefer a different decision from the decision made by the primary player. Thus, the secondary player may have the opportunity to complete the game in a different fashion than does the primary player. For example, the outcome based on which the secondary player is paid may be different from the outcome based on which the primary player is paid. Note that the secondary player may participate in a game after the primary player has participated in the game. Thus, the secondary player may participate in a historical game. The secondary player may, nevertheless, seek to take a different direction in the game than what happened in the original game.</li>
        <li id="ul0028-0020" num="0369">&#x2003;The following is an example of some embodiments. A primary player begins play of a game of blackjack. The primary player is dealt a nine and a three as his initial hand. The dealer shows a two face up. The primary player decides to hit. The primary player is dealt a ten and therefore busts because his point total is now 22. The secondary player, prior to seeing the ten which was dealt to the primary player, decides he would rather stand than hit. At this point, the casino server determines what would have happened had the primary player stood. The casino server may then play the dealer's hand, or at least a simulated version of the dealer's hand. The casino server may reveal the dealer's down card to be a 10, providing the dealer with an initial point total of 12. The casino server may then make a hit decision on behalf of the dealer. The casino server may then deal a 10 to the dealer (the same 10 that had gone to the primary player before). The dealer then busts, and the secondary player wins. Thus, both the primary player and the secondary player have started from the same game. However, the primary player and the secondary player have taken the game in different directions by making different decisions at a juncture in the game. As a result, the primary player has lost but the secondary player has won.</li>
    </ul>
    </li>
    <li id="ul0001-0012" num="0370">3. In various embodiments, a secondary player may replay and/or redo some aspect of a game of a primary player.
    <ul id="ul0041" list-style="none">
        <li id="ul0041-0001" num="0371">3.1. A secondary player may redo a game knowing different information from what the primary player knew. When facing a decision in a game, a primary player may have a given amount of information available to him. For example, in a game of blackjack, a primary player facing a decision to &#x201c;hit&#x201d;, &#x201c;stand&#x201d;, &#x201c;double down&#x201d;, &#x201c;split&#x201d; or &#x201c;surrender&#x201d;, may know his own two cards and one of the dealer cards. However, the primary player may not know other potentially valuable information, such as the dealer's face-down card, or the next card to be dealt at the top of the deck. In various embodiments, a secondary player participating in the game of a primary player may have access to additional information that the primary player does not or did not have at the time the primary player originally plays or played the game.
        <ul id="ul0042" list-style="none">
            <li id="ul0042-0001" num="0372">3.1.1. Know the cards yet to come. In various embodiments, a secondary player participating in the game of a primary player may be presented with information about a card that was unknown to the primary player at the same juncture in the game. For example, a secondary player participating in a game of video poker may be presented with information about the next card to be dealt in the deck. In various embodiments, a secondary player may be presented with information about a card: (a) in the dealer's hand; (b) in an opponent's hand (e.g., in the hand of an opponent in a game of Texas Hold'em); (c) in another primary player's hand (e.g., in the hand of another primary player in a game of blackjack in embodiments where primary player hands are not dealt completely face up); (d) that was burned; (e) that will not be dealt (e.g., a card at the bottom of a deck of cards may have no chance of being dealt in a game); (f) that is unlikely to be dealt (e.g., a card that is in the middle of a deck may be unlikely to be dealt in a game); and so on. Information about a card may include information about a suit of the card, and information about a rank of a card. For example, a secondary player may be told that a card is a heart, or that a card is not a spade. For example, a secondary player may be told that a card is a 10-point value card (e.g., in a game of blackjack). For example, a secondary player may be told that a card's rank is between two and six, or that a card is not a seven. In various embodiments, a secondary player may be told the exact rank and suit of a card, such as a queen of diamonds.</li>
            <li id="ul0042-0002" num="0373">3.1.2. Know the primary player made a losing decision. In various embodiments, a secondary player may be given information about the consequences of a primary player's decision in a game. For example, the secondary player may be told that the primary player's decision resulted in the primary player losing a game. For example, if a primary player in a game of blackjack decided to hit and busted, a secondary player may be told that the primary player's decision led to the primary player busting. A secondary player may be told that a primary player's decision did not achieve the best possible outcome of a game. Even if a primary player's decision led to a winning outcome, the secondary player may still be told that the primary player's decision did not lead to the best possible outcome. For example, in a game of video poker, if a primary player drew three cards and made a three-of-a-kind, the primary player may have had the potential to draw three cards in a different way and to make a straight-flush. Thus, the primary player may not have obtained the best outcome that he could of. Of course, the primary player may have made the correct decision from his point of view since he did not know that he would have been able to successfully draw to the straight-flush. In various embodiments, a secondary player may be informed of the relative merits of the primary player's decision or strategy in relation to other possible decisions or strategies. For example, regarding a game of video poker, a secondary player may be told that the primary player made the second best possible decision in terms of what outcomes the primary player could have achieved. In various embodiments, the secondary player may be told the merits of a primary player's decision or strategy assuming the primary player had perfect information about what the results of the various decisions or strategies would be. In some embodiments, the primary player will not have or have had perfect information about the consequences of his decisions, so that pronouncements on the merits of the primary player's decisions would not necessarily indicate that the primary player made a bad or wrong decision. In some embodiments, a secondary player may be provided with an indication of the merits of a strategy or decision, whether or not the primary player chose such a decision or strategy. For example, in some embodiments, a secondary player may be told that a particular strategy is a good strategy but not the best possible strategy. For example, a secondary player may be told that a particular strategy is a losing strategy. In various embodiments, the casino may have knowledge about cards that would be unknown to the secondary player in a game. Thus, the casino may be able to inform the secondary player based on such knowledge and thereby provide useful strategy recommendations to the secondary player without explicitly sharing the knowledge.</li>
        </ul>
        </li>
        <li id="ul0041-0002" num="0374">3.2. A secondary player may redo a game with the same ordering of a deck of cards, or with a different ordering. In various embodiments, the consequences of all possible primary player decisions are determined in advance, e.g., at the beginning of a game or prior to a decision of a primary player. For example, in a game of video poker, the shuffling and ordering of a deck of cards before a game serves to determine the consequences of any decision the primary player may make in a game. For example, the shuffling leads to a particular order of the deck such that any new cards that the primary player may decide to draw can be determined deterministically by dealing cards from the top of the deck. In various embodiments, the consequences of all combinations of primary player decisions in a game may be determined in advance. For example, in a game of blackjack, the shuffling of a deck before a game may place the cards to be dealt to primary players in a deterministic order. Thus, for a given set of primary player decisions (and given rules dictating what decisions must be made by the dealer), an outcome of the game for each set of primary player decisions may be determined deterministically from the ordering of cards in the deck. In various embodiments, the symbols that will be revealed on each reel of slot machine are determined in advance and prior to the revelation of even a single symbol. For example, the symbol that will be revealed on the third reel of a slot machine may be determined even before the symbol on the first reel of the slot machine is revealed. In various embodiments, the advanced determination of all possible consequences of a primary player's decision may or may not also apply to a possible alternate decision by a secondary player. In various embodiments, the advanced determination of one or more symbols in a game may or may not apply to the secondary player prior to the revelation of the symbols to the primary player or to the secondary player.
        <ul id="ul0043" list-style="none">
            <li id="ul0043-0001" num="0375">3.2.1. Same ordering. In various embodiments, the advanced determination of all possible consequences of a primary player's decision may apply in the same way to the possible consequences of a secondary player's decision. In other words, suppose the primary player is or has played a game, and the secondary player is participating in the game. At a given juncture in the game, a particular decision by the secondary player (e.g., &#x201c;hit&#x201d;) will have the same consequences for the secondary player as the same particular decision made by the primary player would have for the primary player. For example, a decision by the secondary player to &#x201c;hit&#x201d; would result in the secondary player being dealt a four of diamonds. Likewise, a decision by the primary player to hit would result in the primary player being dealt the four of diamonds. It should be noted that for the primary player and the secondary player to experience the same consequence given the same decision may mean that the primary and secondary players will experience the same outcomes or will receive the same symbols or indicia. The actual payouts received by the primary player and the secondary player may differ, in some embodiments, due to differing bets by the primary and secondary players.</li>
            <li id="ul0043-0002" num="0376">&#x2003;In various embodiments, a secondary player may decide to continue a game that has already been started. The secondary player may decide to join a game, for example, after an event within the game has been resolved. For example, a secondary player may decide to join a game after a first symbol on reel of a slot machine has been revealed, but before symbols on a second reel or on a third reel have been revealed. Once the secondary player decides to join the game, the game may proceed exactly as it had for the primary player who originally played the game (or exactly as it will for the primary player currently involved in the game). In other words, once the secondary player joins the game, the secondary player may receive the same outcome of the game that the primary player does or has. This may occur by virtue of the outcome of the game having been determined in advance, even before the revelation of the first symbol, for example.</li>
            <li id="ul0043-0003" num="0377">3.2.2. Different ordering. In some embodiments a secondary player may participate in the game of a primary player, make all the same decisions as does the primary player, yet achieve a different result. The consequences of secondary player decisions may not be the same as the consequences of primary player decisions. In some embodiments, the consequences of a secondary player's decisions are determined after the start of a game. For example, the consequences of a secondary player's decisions are determined at the juncture in a game where a secondary player makes a decision, just prior to when a secondary player makes a decision, or even after a secondary player makes a decision. The consequences of possible decisions to be made by a secondary player may be determined by shuffling a remaining portion of a deck of cards from which cards will be dealt in the game in which the secondary player is participating. For example, suppose a primary player has been involved in a game of blackjack and has received an initial two-card hand. The primary player may decide to hit, and may thereby receive a king of clubs dealt from the top of the deck. A secondary player may participate in the same game. The secondary player may also decide to hit after the initial two-card hand has been dealt. However, prior to the second player receiving a new card in his hand, the remaining portion of the deck of cards may be reshuffled. Thus, the secondary player may receive a different card than did the primary player, e.g., the secondary player may receive the five of hearts. Thus, the consequences of the secondary player's decision to hit will have been determined only after the secondary player has made his decision, the determination being made through the reshuffling of the deck of cards.</li>
            <li id="ul0043-0004" num="0378">&#x2003;In embodiments where the secondary player does not make the same decision as does the primary player, the consequences of the secondary player's decision may not necessarily be determined at the beginning of the game. For example, in a game of video poker, a primary player may decide to discard the fourth and fifth cards from a starting hand. The secondary player, who is participating in the same game as the primary player and therefore has the same starting hand, may instead decide to discard the first and second cards from the starting hand. The primary player may be dealt a ten of diamonds and a queen of clubs. The secondary player may be dealt a jack of hearts and a nine of hearts. The secondary player may receive different cards than does the primary player because the cards to be dealt to the secondary player after the initial hand may be determined using a separate randomization process from that used to determine the cards dealt to the primary player after the initial hand. For example, after the initial cards in a game of video poker have been dealt, the remaining cards in the deck may be reshuffled from the order they had in the deck used in the game of the primary player. In some embodiments, the remaining cards in the deck may be reshuffled in both the game of the primary player and in the game of the secondary player. The two reshufflings may be different from one another, however, so that the order of the remaining cards in the deck for the primary player is different from the order of the remaining cards in the deck for the secondary player.</li>
            <li id="ul0043-0005" num="0379">&#x2003;In various embodiments, a copy of a game, a deck, or of other game elements may be used in completing a game of a secondary player. For example, when a primary player begins a game, the deck of cards used in the game of the primary player may be copied. The deck may be copied so that the order of the cards within the deck is copied as well. The primary and the secondary player may then play out the remainder of the game from the two separate copies of the deck, without interfering with one another. In one embodiment, both the primary player and the secondary player start out using the same deck to generate, e.g., an initial hand. Thereafter, the remaining portion of the deck (e.g., the part of the deck that hasn't been dealt yet), is copied. This part of the deck may then be reshuffled, or it may not be reshuffled. The secondary player may then play out the remainder of the game using the copied portion of the deck. Thus, the secondary player may play out the remaining portion of the game separately from the primary player without interfering with the game of the primary player.</li>
            <li id="ul0043-0006" num="0380">&#x2003;In various embodiments, a secondary player may participate in slot machine game. A first symbol from the slot machine game may be revealed. The secondary player may wish to continue the game from the point after the first symbol has been revealed. However, the secondary player may wish to continue the game in a different fashion from that in which the primary player has continued the game. In other words, the secondary player may want the remaining symbols of his outcome to be generated randomly using a different random process than that used to generate the remaining symbols for the primary player. Thus, in some embodiments, the casino (or the gaming device working on behalf of the casino) may randomly determine additional symbols to generate and display for the secondary player, where such symbols need not necessarily be the same as those generated and displayed for the primary player. In various embodiments, a casino may randomly determine a way to generate additional symbols as follows. A casino may determine all outcomes containing the one or more symbols that have already been generated. Such outcomes may be probability weighted so that, for example, it is understood that some are more likely to occur than others. The casino may then select from among the probability weighted outcomes randomly and in proportion to their weightings. Thus, for example, an outcome with twice the probability weighting of another outcome would be twice as likely to be selected.</li>
        </ul>
        </li>
        <li id="ul0041-0003" num="0381">3.3. A secondary player may redo the game after the fact. In various embodiments, a secondary player may replay a game from a certain juncture after the game has already been completed. For example, one hour after a game of video poker has been completed, a secondary player may replay the game starting after the initial hand has been dealt but before any decision has been made as to which cards to discard. As described above, a secondary player may replay a game with different outcomes or consequences than those experienced by the primary player, even if the secondary player and the primary player made the same decisions in the game. This is because the replayed game may be replayed with a different randomization process used than was used for the original game.
        <ul id="ul0044" list-style="none">
            <li id="ul0044-0001" num="0382">3.3.1. Replay a live game. In various embodiments, a secondary player may replay a game that was originally played with multiple primary players. For example, the secondary player may replay a game of Texas Hold'em poker in which there were originally <b>9</b> primary players. The secondary player may wish to play the hand of one of the 9 players.
            <ul id="ul0045" list-style="none">
                <li id="ul0045-0001" num="0383">*******. The casino uses AI. In various embodiments, in order for the secondary player to have the opportunity to replay a multi-player game, other entities may take the positions of primary players other than the player who the secondary player has replaced. Thus, in some embodiments, the casino may use computer algorithms to take the place of the other primary players. The computer algorithms may be programmed to make decisions in a game, such as in a game of poker. For example, the computer algorithms may include a set of rules detailing what actions to take for any given game situation. When replaying the game, the secondary player may thus play against one or more computer algorithms. In some embodiments, the casino may disclose to the secondary player one or more attributes of a computer algorithm used in a multi-player game. The casino may disclose the rules used by the computer algorithm. The casino may disclose a personality of the algorithm, such as &#x201c;aggressive&#x201d; or &#x201c;tight&#x201d;. In various embodiments, the casino may be required to disclose one or more attributes of a computer algorithm. The requirements may come from casino regulators, for example.</li>
                <li id="ul0045-0002" num="0384">*******. Secondary player plays against other secondary players. In various embodiments, if a first secondary player replays a game involving multiple primary players, the positions of other primary player may be filled with other secondary players. Thus, in some embodiments, the first secondary player may replay a game against other secondary players. In some embodiments, a first secondary player may replay a game against one or more other secondary players and against one or more computer algorithms.</li>
                <li id="ul0045-0003" num="0385">*******. Other players are not opponents. In some embodiments, a secondary player may replay a game that included multiple primary players. However, the primary players may not have been opponents of one another. For example, a secondary player may replay a game of blackjack from a live table game which originally included 6 primary players. The primary players were not opponents, but rather were competing against the casino. When the secondary player replays the game, the secondary player may wish for positions of the other primary players at the game to be filled as well. Thus, in some embodiments, computer algorithms may fill the places of other primary players. In some embodiments, other secondary players may fill the places of other primary players.</li>
            </ul>
            </li>
        </ul>
        </li>
        <li id="ul0041-0004" num="0386">3.4. A secondary player may make a different decision in real time and diverge into a different game. In various embodiments, a secondary player may participate in a game that is currently being played by a primary player. Thus, the secondary player may participate in a game of a primary player in real time. However, at a particular point in a game, the secondary player may wish to diverge from the course of the primary player. For example, the secondary player may wish to make a different decision in the game than does the primary player. In some embodiments, the secondary player may not know which decision the primary player will make. However, the secondary player may wish to make his own decision anyway, even if it turns out that the decision of the secondary player will be the same as the decision of the primary player. Once the games of both the primary player and the secondary player have finished, the secondary player may rejoin the primary player for the next game. In other words, the secondary player and the primary player in the next game may receive the same symbols, indicia, or other event resolutions. If the primary player finishes his game before the secondary player does, the primary player may be delayed by the casino until the secondary player has an opportunity to bet on the next game.</li>
        <li id="ul0041-0005" num="0387">3.5. Searching for games with certain characteristics. In various embodiments, a secondary player may search for games with particular characteristics. As described elsewhere herein, a secondary player may search for the games of a particular primary player, for games played at a particular gaming device, for games played at a particular time of day, for games played at a particular casino, for games played right before a big win, and so on. However, the secondary player may also search for games which would give the secondary player an opportunity to proceed from a certain starting point in a beneficial fashion. Once the secondary player finds a game in a search, the secondary player may have the opportunity to play out the game from a certain point in the game, such as from a decision point in the game.
        <ul id="ul0046" list-style="none">
            <li id="ul0046-0001" num="0388">3.5.1. The wrong decision was made. In some embodiments, a secondary player may search for a game in which a primary player made a decision that met or failed to meet one or more criteria. A secondary player may search for a game in which the primary player: (a) did not make a decision which generated the highest expected winnings for the primary player; (b) did not make a decision which made the primary player eligible for the highest paying outcome that the primary player could have been eligible for; (c) did not make a decision that followed a generally recommended strategy (e.g., the primary player did not make a decision in blackjack that followed basic strategy); (d) did not make a decision that followed a strategy of interest to the secondary player; and so on. For example, a secondary player may search for a game of blackjack in which the primary player has a point total of 13 with no aces, in which the dealer shows a 3 up-card, and in which the primary player chose to stand. The secondary player may choose to search for such games because, under various rules, the basic strategy recommendation would be to hit. Thus the secondary player will have searched for a game in which the primary player has not made the correct decision according to the recommendations of basic strategy.</li>
            <li id="ul0046-0002" num="0389">3.5.2. There is a certain starting hand. In various embodiments, a secondary player may search for a game of a primary player in which there was a particular starting hand or in which there was a particular category of starting hand. For example, a secondary player may search for a game of a primary player which was a game of video poker and which included an initial hand with exactly four hearts in it. A secondary player may search for a video poker game in which the primary player has an initial hand with a pair of jacks. A secondary player may search for a video poker game in which the primary player has an initial hand which includes the ace of spades, king of spades, queen of spades, jack of spades, and the four of hearts. A secondary player may search for a game of blackjack in which the primary player had a particular point total, such as 11. A secondary player may search for a game of blackjack in which the primary player had a first point total or a first combination of cards, and in which the dealer showed a second card. For example, the primary player had a point total of 14 and the dealer showed a 4. A secondary player may search for a game of blackjack in which the primary player had already hit twice and still had a point total of less than 14. In various embodiments, a secondary player may search for a game in which one or more symbols occurred at a slot machine. In replaying the game, the secondary player may have the opportunity to obtain additional symbols where such symbols differ from the ones obtained by the primary player in the same game.</li>
            <li id="ul0046-0003" num="0390">3.5.3. A primary player had a near miss. In various embodiments, the secondary player may search for games in which the primary player had a near miss. The secondary player may search for games in which: (a) an outcome obtained by the primary player differed by X or fewer symbols from a high-paying outcome (e.g., there was only one symbol different between the outcome achieved by the primary player and a jackpot outcome); (b) a primary player had four cards to a royal flush in video poker but did not obtain the fifth card; (c) an outcome obtained by a primary player differed by one symbol from a jackpot outcome, and the symbol necessary for the jackpot outcome was just one position removed on a reel from the pay-line; and so on. A secondary player may keep the symbols of an outcome from a game of a primary player that would contribute to a high-paying outcome, and may have any additional symbols regenerated in an attempt to obtain all the symbols necessary for obtaining the high-paying outcome.</li>
        </ul>
        </li>
        <li id="ul0041-0006" num="0391">3.6. Adjust the odds of a game based on what situation the secondary player is starting from. In various embodiments, a secondary player who begins play from the middle of a game, or who begins play in a game after finding out any information about a possible final outcome of the game, may have different probabilities of achieving a given final outcome from what any player would have had at the start of a game. For example, if a secondary player starts a game of video poker at the midpoint after an initial hand with four cards to the royal flush has been dealt, the secondary player will have a greater chance of achieving the royal flush than if the secondary player were starting the game from the beginning. As described herein, a house advantage may be derived from the products of payout ratios and probabilities corresponding to outcomes. Thus, in some embodiments, if the probabilities of paying outcomes go up, then the payout ratios associated with such outcomes must go down in order to maintain a constant house advantage, or in order to maintain any house advantage at all. Thus, in some embodiments, the payout ratios associated with an outcome may change when a secondary player begins a game after some information has been revealed in the game. For example, a payout ratio for a royal flush may be 500 for a game of video poker in which a player starts from the beginning. However, if a player starts the game with an initial hand that contains the ace of spades, king of spades, queen of spades, jack of spades, and 3 of hearts, then the payout ratio for the royal flush may be set to 25 rather than 500. In various embodiments, payout ratios for outcomes may be adjusted for a game started in the middle so that the house advantage for the game started in the middle is the same (or nearly the same) as for the same game started from the beginning. For example, suppose the house edge on a game of video poker is 2% with perfect play. If a secondary player is allowed to start in the middle of a game (e.g., after an initial hand of poker is dealt), then payout ratios for one or more outcomes may be adjusted so that the house advantage over the secondary player is still approximately 2% (e.g., between 1% and 3%). As will be appreciated, the payout ratio for a game may be adjusted in several ways, any of which are contemplated in various embodiments. In various embodiments, a payout ratio may be changed by changing a required bet from a secondary player while maintaining constant payouts on outcomes. In various embodiments, a payout ratio may be changed by changing the payouts for one or more outcomes while maintaining the same required bet amount. In various embodiments, a payout ratio may be changed by changing both the payouts for one or more outcomes, and the amount of a required bet.
        <ul id="ul0047" list-style="none">
            <li id="ul0047-0001" num="0392">3.6.1. Odds adjustments in a game of Hold'em. In various embodiments, a secondary player may wish to participate in a game that involves multiple primary players. The secondary player may wish to take the place of a first primary player in the game and to make one or more decisions in the game going forward from a particular point. However, probabilities for possible outcomes of a multi-player game may not be readily quantifiable since the outcomes may depend on the actions of human beings, each with their own independent wills. As such, it may be difficult for the casino to set a payout ratio for a secondary player who is joining in the middle of a multi-player game. Further, the secondary player will not necessarily be interacting with the other primary players in the game (e.g., the primary players in the game other than the primary player whose place the secondary player has taken), since the game may have been played in the past, or since the primary player whose place the secondary player will be filling may still be in the real game. Thus, the secondary player may complete the remainder of the game against computer algorithms which fill in for other primary players. The secondary player may complete the remainder of the game against other secondary players who fill in for other primary players.
            <ul id="ul0048" list-style="none">
                <li id="ul0048-0001" num="0393">3.6.1.1. Assume all players will stay in and then decide? In some embodiments, a probability that a secondary player wins a game may be derived or estimated based on an assumption that all other players in a game (e.g., all algorithms filling in for primary players; e.g., all secondary players filling in for primary players) remain in the game. In other words, there may be an assumption that no player folds after the point at which the secondary player has joined the game. Based on an assumption that no further player will fold in a game, the probability that a secondary player will win can be derived in a straightforward fashion. In one embodiment, all possible combinations of additional cards to be dealt can be tested. For example, in a game of Texas Hold'em in which the flop has been dealt already, all possible combinations of turn and river cards may be tested. The proportion of the combinations that lead to a win for the secondary player may then be used to determine the probability that the secondary player will win. In some embodiments, a large number of deals of additional cards in the game may be simulated in order to determine the proportion of such simulations which the secondary player wins. Such a proportion may be used to estimate the probability that the secondary player will win. It will be appreciated that a probability that the secondary player will tie may be determined in a similar fashion to the way a probability of winning may be determined. For example, all possible combinations of additional cards to be dealt may be tested, and the proportion of such combinations which lead to a tie may be used to estimate the probability that the secondary player will tie.</li>
                <li id="ul0048-0002" num="0394">3.6.1.2. Do a simulation with good AI players? In some embodiments, a probability that a secondary player will win in a multi-player game may be determined using a simulation in which computer algorithms fill in for each of the primary players in the original game. For example, 1000 simulated games may be run using computer algorithms filling in for each of the primary players. The proportion of the time that the computer algorithm wins while filling in at the position desired to be played by the secondary player may be used to determine the probability that the secondary player will win. In some embodiments, the average amount won or lost by the computer algorithm filling in at the position desired to be played by the secondary player may be used to estimate an expected amount that will be won or lost by the secondary player in the game. In various embodiments, once a probability that a secondary player will win and/or tie in a game is determined, a payout ratio for the game may be determined. In various embodiments, once an expected amount that a secondary player will win or lose is determined, a required bet amount for the secondary player may be determined. A payout ratio or required bet amount may be determined for any manner in which a secondary player completes a game from the point or juncture at which the secondary player joins. For example, a payout ratio or required bet amount may be determined whether a secondary player completes a game against other secondary players, whether a secondary player completes a game against computer algorithms, or whether the secondary player completes a game against any combination of the two.</li>
            </ul>
            </li>
        </ul>
        </li>
        <li id="ul0041-0007" num="0395">3.7. If a secondary player does diverge in time, then there may be some catch-up, or the secondary player may skip to the current outcome. For example, the secondary player may be busy on a bonus round while the primary player goes off playing more games. In various embodiments, a secondary player may complete a game in a different manner from the way in which a primary player completes the game. For example, a secondary player may be participating in real time in a game of a primary player. At some point in the game, the primary player may make a first decision and the secondary player may make a second decision. As a result of the different decisions, or for any other reason, the game of the secondary player may last longer than does the game of the primary player. For example, in a game of blackjack, a decision to &#x201c;hit&#x201d; by a primary player may lead to the primary player busting, and thereby to an immediate end to the game of the primary player. On the other hand, a decision to &#x201c;stand&#x201d; by the secondary player may cause the dealer in the game of the secondary player to make one or more decisions, thereby prolonging the game of the secondary player. If the game of a secondary player lasts longer than the game of a primary player in whose games the secondary player has been participating, then the primary player may on occasion begin a new game before the secondary player has completed an old game.
        <ul id="ul0049" list-style="none">
            <li id="ul0049-0001" num="0396">3.7.1. The secondary player sits out the next game and joins a future game. In some embodiments, if a primary player begins a new game before a secondary player has completed a prior game he started with the primary player, then the secondary player may sit out the new game. The secondary player may sit out any number of new games until the old game of the secondary player has finished. The secondary player may then join in the next game to be started by the primary player.</li>
            <li id="ul0049-0002" num="0397">3.7.2. The secondary player gets involved in two games simultaneously. In some embodiments, even if a secondary player has not completed a prior game, the secondary player may still participate in a new game of a primary player. For example, the secondary player may follow the progress of his old and new games using a split-screen view on his terminal. As will be appreciated, the secondary player may be involved in more than one old game even as a new game is started. The secondary player may potentially view the progress of one or more old games along with the new game.</li>
            <li id="ul0049-0003" num="0398">3.7.3. The old game is finished quickly. In various embodiments, once when a primary player finishes a first game and/or begins a second game, the older game of the secondary player (e.g., the offshoot from the first game of the primary player) may be sped up. For example, the casino may cause outcomes to be generated or displayed more rapidly or instantaneously. For example, rather than showing renditions of cards being dealt, the house may show cards appearing instantly in the hand of the secondary player. In various embodiments, the house may make decisions for the secondary player automatically. For example, the house may make decisions for the secondary player according to one or more strategies, such as according to optimal strategy or according to basic strategy.</li>
            <li id="ul0049-0004" num="0399">3.7.4. The games of the primary player are stored and the secondary player can participate in the games later on. In various embodiments, a secondary player who is still involved in an older game may not immediately participate in a new game of a primary player. However, data about the new game may be stored by the casino. The secondary player may then, at a later time, choose to participate in the game. The casino may store a record of which games of the primary player the secondary player missed and may then give the secondary player the option of participating in such games.</li>
            <li id="ul0049-0005" num="0400">3.7.5. The secondary player gets the EV of a game. In various embodiments, a secondary player may not complete a game in the standard fashion, but may rather receive a settlement payment. The settlement payment may be based on an average amount that the secondary player might have expected to win had he completed the game. In various embodiments, a secondary player may be involved in a bonus round (e.g., the bonus round of a slot machine game). The secondary player, rather than playing out the bonus round, may receive a settlement amount for the bonus round. The secondary player may thereby save the time of playing through the entire bonus round, and may therefore be able to participate in a new game that the primary player would otherwise have started without the secondary player's participation.</li>
        </ul>
        </li>
        <li id="ul0041-0008" num="0401">3.8. The secondary player may bet different pay-lines. In various embodiments, a secondary player may choose to bet on different pay-lines from those on which the primary player bet or bets. For example, the primary player may bet a first pay-line and a second pay-line at a slot machine while a secondary player bets only the first pay-line. For example, a primary player may bet a first pay-line at a slot machine while a secondary player bets a first pay-line and a second pay-line. For example, a primary player may bet a first and second pay-line while a secondary player bets a second and third pay-line. For example, a primary player may bet a first pay-line while a secondary player bets a second pay-line at a slot machine.</li>
        <li id="ul0041-0009" num="0402">3.9. The secondary player may bet different amounts than did the primary player. For example, the secondary player may bet the full three coins rather than just one. In various embodiments, a secondary player may bet a different amount than does a primary player. For example, in a game of poker, such as in a multiplayer game of Texas Hold'em, a secondary player may decide he would rather raise by $20 instead of the $10 raise made by a primary player. Accordingly, the secondary player may play out the remainder of the game, taking the position of the primary player, and playing against computer algorithms taking the place of other primary players. In various embodiments, a primary player may bet a first amount at the start of the game, while the secondary player may bet a second amount on the same game.</li>
        <li id="ul0041-0010" num="0403">&#x2003;Embodiments described herein, where applicable may be performed based on games played electronically as well as based on games played using physical tokens, devices, instruments, tables, etc. In various embodiments, a primary player may play a game using physical tokens (e.g., physical cards and chips), while a secondary player may participate in the game and view an electronic version of the game. In some embodiments, a primary player may play an electronic version of a game and a secondary player may participate in the game via an electronic version of the game. In some embodiments, primary player may play a physical version of a game and a secondary player may participate in the game using physical tokens. For example, when a secondary player makes a decision in a game that is different from the decision made by the primary player, the a deck of cards used in the primary player's game may be duplicated by taking another physical deck of cards and putting the cards in the same order as are the cards in the deck used in the game of the primary player.</li>
    </ul>
    </li>
</ul>
</p>
<?DETDESC description="Detailed Description" end="tail"?>
</description>
<us-claim-statement>The invention claimed is:</us-claim-statement>
<claims id="claims">
<claim id="CLM-00001" num="00001">
<claim-text>1. A method comprising:
<claim-text>receiving an indication of a first bet from a first player;</claim-text>
<claim-text>receiving an indication of a second bet from a second player;</claim-text>
<claim-text>determining an ordering of at least one deck of cards;</claim-text>
<claim-text>providing an indication of a hand of a first set of cards from the deck, thereby yielding the hand of the first set of used cards and a first set of unused cards remaining in the deck;</claim-text>
<claim-text>determining, for at least one outcome, a first payout ratio for the first player;</claim-text>
<claim-text>determining, for the at least one outcome, a second payout ratio for the second player, in which the first payout ratio is different from the second payout ratio;</claim-text>
<claim-text>determining, by a processor, a second set of unused cards that includes the cards of the first set of unused cards;</claim-text>
<claim-text>receiving from the first player a first indication of which cards from the hand of the first set of used cards to discard, the quantity of such cards indicated being equal to a first number;</claim-text>
<claim-text>providing an indication of the first number of cards from the first set of unused cards, thereby yielding a first set of replacement cards;</claim-text>
<claim-text>receiving from the second player a second indication of which cards from the hand of the first set of used cards to discard, the quantity of such cards indicated being equal to a second number;</claim-text>
<claim-text>providing an indication of the second number of cards from the second set of unused cards, thereby yielding a second set of replacement cards;</claim-text>
<claim-text>determining a first payment based on the first bet, the hand of the first set of used cards less the cards described by the first indication, and the first set of replacement cards;</claim-text>
<claim-text>determining a second payment based on the second bet, the hand of the first set of used cards less the cards described by the second indication, and the second set of replacement cards;</claim-text>
<claim-text>providing an indication of the first payment to the first player; and</claim-text>
<claim-text>providing an indication of the second payment to the second player.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00002" num="00002">
<claim-text>2. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref> in which the cards described by the first indication are different from the cards described by the second indication.</claim-text>
</claim>
<claim id="CLM-00003" num="00003">
<claim-text>3. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref> further comprising:
<claim-text>prior to providing the indication of the second number of cards, determining an ordering of the second set unused cards that is different from the ordering of the first set of unused cards.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00004" num="00004">
<claim-text>4. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref> in which the first number is different from the second number.</claim-text>
</claim>
<claim id="CLM-00005" num="00005">
<claim-text>5. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref>, in which determining the first payment includes determining the first payment based on the first payout ratio, and in which determining the second payment includes determining the second payment based on the second payout ratio.</claim-text>
</claim>
<claim id="CLM-00006" num="00006">
<claim-text>6. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref>, in which the outcome includes hand of the first set of used cards less the cards described by the first indication, and the first set of replacement cards and the hand of the first set of used cards less the cards described by the second indication, and the second set of replacement cards.</claim-text>
</claim>
<claim id="CLM-00007" num="00007">
<claim-text>7. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref>, further comprising presenting an indication of the second payout ratio to the second player by transmitting an indication from the processor to a mobile device.</claim-text>
</claim>
<claim id="CLM-00008" num="00008">
<claim-text>8. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref>, further comprising presenting an indication of the first payout ratio to the first player prior to receiving the first bet.</claim-text>
</claim>
<claim id="CLM-00009" num="00009">
<claim-text>9. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref>, further comprising presenting an indication of the second payout ratio to the second prior to receiving the second bet.</claim-text>
</claim>
<claim id="CLM-00010" num="00010">
<claim-text>10. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref>, in which providing the indication of the second number of cards includes causing a mobile device to display the second number of cards.</claim-text>
</claim>
<claim id="CLM-00011" num="00011">
<claim-text>11. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref>, further comprising:
<claim-text>presenting an indication of the first payout ratio to the first player; and</claim-text>
<claim-text>presenting an indication of the second payout ratio to the second player.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00012" num="00012">
<claim-text>12. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref> further comprising:
<claim-text>presenting to the second player an indication of the hand of the first set of used cards prior to receiving the second bet.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00013" num="00013">
<claim-text>13. The method of <claim-ref idref="CLM-00001">claim 1</claim-ref>, further comprising maintaining the same ordering of the second set of unused cards as the first set of unused cards.</claim-text>
</claim>
<claim id="CLM-00014" num="00014">
<claim-text>14. A method comprising:
<claim-text>receiving an indication of a first bet from a first player;</claim-text>
<claim-text>receiving an indication of a second bet from a second player;</claim-text>
<claim-text>determining an ordering of at least one deck of cards;</claim-text>
<claim-text>providing an indication of a hand of a first set of cards from the deck, thereby yielding the hand of the first set of used cards and a first set of unused cards remaining in the deck;</claim-text>
<claim-text>determining, for at least one outcome, a first payout ratio for the first player;</claim-text>
<claim-text>determining, for the at least one outcome, a second payout ratio for the second player, in which the first payout ratio is different from the second payout ratio;</claim-text>
<claim-text>determining, by a processor, a second set of unused cards that includes the cards of the first set of unused cards;</claim-text>
<claim-text>receiving from the first player an indication of a first decision to receive at least one first additional card;</claim-text>
<claim-text>providing an indication of the at least one first additional card from the first set of unused cards, thereby yielding the at least one first additional card and a third set of unused cards;</claim-text>
<claim-text>receiving from the second player an indication of a second decision to receive at least one second additional card;</claim-text>
<claim-text>providing an indication of the at least one second additional card from the second set of unused cards, thereby yielding the at least one second additional card and a fourth set of unused cards;</claim-text>
<claim-text>determining a first payment based on the first bet, the hand of the first set of used cards, and the at least one first additional card;</claim-text>
<claim-text>determining a second payment based on the second bet, the hand of the first set of used cards, and the at least one second additional card;</claim-text>
<claim-text>providing an indication of the first payment to the first player; and</claim-text>
<claim-text>providing an indication of the second payment to the second player.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00015" num="00015">
<claim-text>15. The method of <claim-ref idref="CLM-00014">claim 14</claim-ref> further comprising:
<claim-text>prior to providing the indication of the at least one second additional card, determining an ordering of the second set unused cards that is different from the ordering of the first set of unused cards.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00016" num="00016">
<claim-text>16. The method of <claim-ref idref="CLM-00014">claim 14</claim-ref> in which a first number of additional cards of the at least one first additional card is different from a second number of additional cards of the at least one second additional card.</claim-text>
</claim>
<claim id="CLM-00017" num="00017">
<claim-text>17. The method of <claim-ref idref="CLM-00014">claim 14</claim-ref>, in which determining the first payment includes determining the first payment based on the first payout ratio, and in which determining the second payment includes determining the second payment based on the second payout ratio.</claim-text>
</claim>
<claim id="CLM-00018" num="00018">
<claim-text>18. The method of <claim-ref idref="CLM-00014">claim 14</claim-ref>, in which the outcome includes the hand of the first set of used cards and the first additional card, and the hand of the first set of used cards and the second additional card.</claim-text>
</claim>
<claim id="CLM-00019" num="00019">
<claim-text>19. The method of <claim-ref idref="CLM-00014">claim 14</claim-ref>, further comprising:
<claim-text>presenting an indication of the first payout ratio to the first player; and</claim-text>
<claim-text>presenting an indication of the second payout ratio to the second player.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00020" num="00020">
<claim-text>20. The method of <claim-ref idref="CLM-00014">claim 14</claim-ref> further comprising:
<claim-text>presenting to the second player an indication of the hand of the first set of used cards prior to receiving the second bet.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00021" num="00021">
<claim-text>21. The method of <claim-ref idref="CLM-00014">claim 14</claim-ref>, further comprising maintaining the same ordering of the second set of unused cards as the first set of unused cards.</claim-text>
</claim>
<claim id="CLM-00022" num="00022">
<claim-text>22. The method of <claim-ref idref="CLM-00014">claim 14</claim-ref>, further comprising: receiving from the first player a third decision to receive at least one third additional card; providing an indication of the at least one third additional card from the third set of unused cards, thereby yielding the at least one third additional card and a fourth set of unused cards; and in which determining the first payment includes determining the first payment based on the first bet, the hand of the first set of used cards, the at least one first additional card, and the at least one second additional card.</claim-text>
</claim>
<claim id="CLM-00023" num="00023">
<claim-text>23. An apparatus comprising:
<claim-text>a machine readable medium having stored thereon a plurality of instructions that when executed by a computing device cause the computing device to perform a method comprising:</claim-text>
<claim-text>receiving an indication of a first bet from a first player;</claim-text>
<claim-text>receiving an indication of a second bet from a second player;</claim-text>
<claim-text>providing an indication of at least one first set of symbols in a game involving a number of symbols being chosen that is greater than the number of the first set of symbols;</claim-text>
<claim-text>determining, for at least one outcome of the game, a first payout ratio for the first player;</claim-text>
<claim-text>determining, for the at least one outcome of the game, a second payout ratio for the second player, in which the first payout ratio is different from the second payout ratio;</claim-text>
<claim-text>providing an indication of at least one second set of symbols that in combination with the at least one first set of symbols yields the number of symbols;</claim-text>
<claim-text>providing an indication of at least one third set of symbols that in combination with the at least one first set of symbols yields the number of symbols;</claim-text>
<claim-text>determining a first payment based on the combination of the first set of symbols and the second set of symbols;</claim-text>
<claim-text>determining a second payment based on the combination of the first set of symbols and the third set of symbols;</claim-text>
<claim-text>providing an indication of the first payment to the first player; and</claim-text>
<claim-text>providing an indication of the second payment to the second player.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00024" num="00024">
<claim-text>24. The apparatus of <claim-ref idref="CLM-00023">claim 23</claim-ref>, in which determining the first payment includes determining the first payment based on the first payout ratio, and in which determining the second payment includes determining the second payment based on the second payout ratio.</claim-text>
</claim>
<claim id="CLM-00025" num="00025">
<claim-text>25. The apparatus of <claim-ref idref="CLM-00023">claim 23</claim-ref>, in which the outcome includes the combination of the first set of symbols and the second set of symbols, and the combination of the first set of symbols and the third set of symbols.</claim-text>
</claim>
<claim id="CLM-00026" num="00026">
<claim-text>26. The apparatus of <claim-ref idref="CLM-00023">claim 23</claim-ref>, in which the method further comprises:
<claim-text>presenting an indication of the first payout ratio to the first player; and</claim-text>
<claim-text>presenting an indication of the second payout ratio to the second player.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00027" num="00027">
<claim-text>27. The apparatus of <claim-ref idref="CLM-00023">claim 23</claim-ref>, in which the method further comprises:
<claim-text>presenting to the second player an indication of the first set of symbols prior to receiving the second bet.</claim-text>
</claim-text>
</claim>
<claim id="CLM-00028" num="00028">
<claim-text>28. The apparatus of <claim-ref idref="CLM-00023">claim 23</claim-ref>, further comprising the computing device. </claim-text>
</claim>
</claims>
</us-patent-grant>
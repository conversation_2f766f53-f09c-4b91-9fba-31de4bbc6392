# Document converter

This is an automatic generated API reference of the main components of Docling.

::: docling.document_converter
    handler: python
    options:
        members:
            - DocumentConverter
            - ConversionResult
            - ConversionStatus
            - FormatOption
            - InputFormat
            - PdfFormatOption
            - ImageFormatOption
            - StandardPdfPipeline
            - WordFormatOption
            - PowerpointFormatOption
            - MarkdownFormatOption
            - AsciiDocFormatOption
            - HTMLFormatOption
            - SimplePipeline
        show_if_no_docstring: true
        show_submodules: true
        docstring_section_style: list
        filters: ["!^_"]
        heading_level: 2
        inherited_members: true
        merge_init_into_class: true
        separate_signature: true
        show_root_heading: true
        show_root_full_path: false
        show_signature_annotations: true
        show_source: false
        show_symbol_type_heading: true
        show_symbol_type_toc: true
        signature_crossrefs: true
        summary: true

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Conversion of CSV files\n", "\n", "This example shows how to convert CSV files to a structured Docling Document.\n", "\n", "* Multiple delimiters are supported: `,` `;` `|` `[tab]`\n", "* Additional CSV dialect settings are detected automatically (e.g. quotes, line separator, escape character)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example Code"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from docling.document_converter import DocumentConverter\n", "\n", "# Convert CSV to Docling document\n", "converter = DocumentConverter()\n", "result = converter.convert(Path(\"../../tests/data/csv/csv-comma.csv\"))\n", "output = result.document.export_to_markdown()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This code generates the following output:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["|   Index | Customer Id     | First Name   | Last Name   | Company                         | City              | Country                    | Phone 1                | Phone 2               | Email                       | Subscription Date   | Website                     |\n", "|---------|-----------------|--------------|-------------|---------------------------------|-------------------|----------------------------|------------------------|-----------------------|-----------------------------|---------------------|-----------------------------|\n", "|       1 | DD37Cf93aecA6Dc | <PERSON><PERSON>       | Baxter      | Rasmussen Group                 | East Leonard      | Chile                      | ************           | 397.884.0519x718      | <EMAIL>    | 2020-08-24          | http://www.stephenson.com/  |\n", "|       2 | 1Ef7b82A4CAAD10 | Preston      | Lozano, Dr  | Vega-Gentry                     | East Jimmychester | Djibouti                   | 5153435776             | 686-620-1820x944      | <EMAIL>             | 2021-04-23          | http://www.hobbs.com/       |\n", "|       3 | 6F94879bDAfE5a6 | <PERSON>          | <PERSON>       | <PERSON><PERSON><PERSON><PERSON>                   | Isabelborough     | Antigua and Barbuda        | +1-539-402-0259        | (496)978-3969x58947   | <EMAIL>         | 2020-03-25          | http://www.lawrence.com/    |\n", "|       4 | 5Cef8BFA16c5e3c | <PERSON>        | <PERSON>       | <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON> | Bensonview        | Dominican Republic         | 001-808-617-6467x12895 | +1-813-324-8756       | <EMAIL> | 2020-06-02          | http://www.good-lyons.com/  |\n", "|       5 | 053d585Ab6b3159 | <PERSON>       | <PERSON><PERSON>      | <PERSON>, <PERSON> and <PERSON>        | West Priscilla    | Slovakia (Slovak Republic) | 001-234-203-0635x76146 | 001-199-446-3860x3486 | <EMAIL>     | 2021-04-17          | https://goodwin-ingram.com/ |"]}], "metadata": {"kernelspec": {"display_name": "docling-TtEIaPrw-py3.12", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}
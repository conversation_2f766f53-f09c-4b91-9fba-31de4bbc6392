{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "test_document", "origin": {"mimetype": "text/markdown", "binary_hash": 1572566412974386486, "filename": "test_document.md"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/7"}, {"$ref": "#/groups/1"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/texts/6"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "name": "group", "label": "inline"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "title", "prov": [], "orig": "测试文档", "text": "测试文档"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "这是一个用于测试Docling的简单文档。", "text": "这是一个用于测试Docling的简单文档。"}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [], "orig": "功能特性", "text": "功能特性", "level": 1}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "文档解析", "text": "文档解析", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "格式转换", "text": "格式转换", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "OCR支持", "text": "OCR支持", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "多种输出格式", "text": "多种输出格式", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [], "orig": "表格示例", "text": "表格示例", "level": 1}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [], "orig": "代码示例", "text": "代码示例", "level": 1}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "code", "prov": [], "orig": "from docling.document_converter import DocumentConverter\n\nconverter = DocumentConverter()\nresult = converter.convert(\"document.pdf\")\nprint(result.document.export_to_markdown())", "text": "from docling.document_converter import DocumentConverter\n\nconverter = DocumentConverter()\nresult = converter.convert(\"document.pdf\")\nprint(result.document.export_to_markdown())", "captions": [], "references": [], "footnotes": [], "code_language": "unknown"}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "这是一个测试文档的结尾。", "text": "这是一个测试文档的结尾。"}], "pictures": [], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "table", "prov": [], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "功能", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "描述", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PDF解析", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "支持复杂PDF文档解析", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "格式转换", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "支持多种输出格式", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OCR", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "支持图像文字识别", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "功能", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "描述", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PDF解析", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "支持复杂PDF文档解析", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "格式转换", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "支持多种输出格式", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OCR", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "支持图像文字识别", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 4, "num_cols": 2, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "功能", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "描述", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PDF解析", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "支持复杂PDF文档解析", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "格式转换", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "支持多种输出格式", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "OCR", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "支持图像文字识别", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {}}
item-0 at level 0: unspecified: group _root_
  item-1 at level 1: title: Introduction to parsing HTML files with Docling
    item-2 at level 2: picture
      item-2 at level 3: caption: Docling
    item-3 at level 2: text: Docling simplifies document proc ... ntegrations with the gen AI ecosystem.
    item-4 at level 2: section_header: Supported file formats
      item-5 at level 3: text: Docling supports multiple file formats..
      item-6 at level 3: list: group list
        item-7 at level 4: list_item: Advanced PDF understanding
        item-8 at level 4: picture
          item-8 at level 5: caption: PDF
        item-9 at level 4: list_item: Microsoft Office DOCX
        item-10 at level 4: picture
          item-10 at level 5: caption: DOCX
        item-11 at level 4: list_item: HTML files (with optional support for images)
        item-12 at level 4: picture
          item-12 at level 5: caption: HTML
      item-13 at level 3: section_header: Three backends for handling HTML files
        item-14 at level 4: text: <PERSON><PERSON> has three backends for parsing HTML files:
        item-15 at level 4: list: group ordered list
          item-16 at level 5: list_item: HTMLDocumentBackend Ignores images
          item-17 at level 5: list_item: HTMLDocumentBackendImagesInline Extracts images inline
          item-18 at level 5: list_item: HTMLDocumentBackendImagesReferenced Extracts images as references
  item-19 at level 1: caption: Docling
  item-20 at level 1: caption: PDF
  item-21 at level 1: caption: DOCX
  item-22 at level 1: caption: HTML
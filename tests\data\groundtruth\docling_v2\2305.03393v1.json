{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "2305.03393v1", "origin": {"mimetype": "application/pdf", "binary_hash": 8240558336632491037, "filename": "2305.03393v1.pdf"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/pictures/0"}, {"$ref": "#/texts/79"}, {"$ref": "#/texts/80"}, {"$ref": "#/texts/81"}, {"$ref": "#/texts/82"}, {"$ref": "#/texts/83"}, {"$ref": "#/texts/84"}, {"$ref": "#/texts/85"}, {"$ref": "#/texts/86"}, {"$ref": "#/texts/87"}, {"$ref": "#/texts/88"}, {"$ref": "#/texts/89"}, {"$ref": "#/texts/90"}, {"$ref": "#/texts/91"}, {"$ref": "#/texts/92"}, {"$ref": "#/texts/93"}, {"$ref": "#/texts/94"}, {"$ref": "#/texts/95"}, {"$ref": "#/texts/96"}, {"$ref": "#/texts/97"}, {"$ref": "#/pictures/1"}, {"$ref": "#/texts/99"}, {"$ref": "#/texts/100"}, {"$ref": "#/texts/101"}, {"$ref": "#/texts/102"}, {"$ref": "#/texts/103"}, {"$ref": "#/texts/104"}, {"$ref": "#/texts/105"}, {"$ref": "#/texts/106"}, {"$ref": "#/texts/107"}, {"$ref": "#/texts/108"}, {"$ref": "#/texts/109"}, {"$ref": "#/texts/110"}, {"$ref": "#/groups/1"}, {"$ref": "#/texts/116"}, {"$ref": "#/texts/117"}, {"$ref": "#/texts/118"}, {"$ref": "#/pictures/2"}, {"$ref": "#/texts/172"}, {"$ref": "#/texts/173"}, {"$ref": "#/groups/3"}, {"$ref": "#/texts/176"}, {"$ref": "#/groups/4"}, {"$ref": "#/texts/181"}, {"$ref": "#/texts/182"}, {"$ref": "#/texts/183"}, {"$ref": "#/texts/184"}, {"$ref": "#/texts/185"}, {"$ref": "#/texts/186"}, {"$ref": "#/texts/187"}, {"$ref": "#/texts/188"}, {"$ref": "#/texts/189"}, {"$ref": "#/pictures/3"}, {"$ref": "#/texts/254"}, {"$ref": "#/texts/255"}, {"$ref": "#/texts/256"}, {"$ref": "#/texts/257"}, {"$ref": "#/texts/258"}, {"$ref": "#/texts/259"}, {"$ref": "#/tables/0"}, {"$ref": "#/texts/261"}, {"$ref": "#/texts/262"}, {"$ref": "#/texts/263"}, {"$ref": "#/texts/264"}, {"$ref": "#/texts/265"}, {"$ref": "#/tables/1"}, {"$ref": "#/texts/267"}, {"$ref": "#/texts/268"}, {"$ref": "#/pictures/4"}, {"$ref": "#/texts/356"}, {"$ref": "#/texts/357"}, {"$ref": "#/texts/358"}, {"$ref": "#/texts/359"}, {"$ref": "#/pictures/5"}, {"$ref": "#/texts/372"}, {"$ref": "#/texts/373"}, {"$ref": "#/texts/374"}, {"$ref": "#/texts/375"}, {"$ref": "#/texts/376"}, {"$ref": "#/texts/377"}, {"$ref": "#/texts/378"}, {"$ref": "#/groups/5"}, {"$ref": "#/texts/383"}, {"$ref": "#/texts/384"}, {"$ref": "#/groups/6"}, {"$ref": "#/texts/398"}, {"$ref": "#/texts/399"}, {"$ref": "#/groups/7"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}], "content_layer": "body", "name": "group", "label": "key_value_area"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/111"}, {"$ref": "#/texts/112"}, {"$ref": "#/texts/113"}, {"$ref": "#/texts/114"}, {"$ref": "#/texts/115"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/pictures/2"}, "children": [{"$ref": "#/texts/158"}], "content_layer": "body", "name": "group", "label": "list"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/174"}, {"$ref": "#/texts/175"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/177"}, {"$ref": "#/texts/178"}, {"$ref": "#/texts/179"}, {"$ref": "#/texts/180"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/5", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/379"}, {"$ref": "#/texts/380"}, {"$ref": "#/texts/381"}, {"$ref": "#/texts/382"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/6", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/385"}, {"$ref": "#/texts/386"}, {"$ref": "#/texts/387"}, {"$ref": "#/texts/388"}, {"$ref": "#/texts/389"}, {"$ref": "#/texts/390"}, {"$ref": "#/texts/391"}, {"$ref": "#/texts/392"}, {"$ref": "#/texts/393"}, {"$ref": "#/texts/394"}, {"$ref": "#/texts/395"}, {"$ref": "#/texts/396"}, {"$ref": "#/texts/397"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/7", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/400"}, {"$ref": "#/texts/401"}, {"$ref": "#/texts/402"}, {"$ref": "#/texts/403"}, {"$ref": "#/texts/404"}, {"$ref": "#/texts/405"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 18.34, "t": 627.0, "r": 36.34, "b": 237.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 39]}], "orig": "arXiv:2305.03393v1  [cs.CV]  5 May 2023", "text": "arXiv:2305.03393v1  [cs.CV]  5 May 2023"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 676.1, "r": 480.6, "b": 645.49, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Optimized Table Tokenization for Table Structure Recognition", "text": "Optimized Table Tokenization for Table Structure Recognition", "level": 1}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 139.34, "t": 621.84, "r": 476.01, "b": 591.84, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 208]}], "orig": "<PERSON><PERSON><PERSON> [0000 - 0002 - 3723 - 6960] , <PERSON> [0000 - 0002 - 9468 - 0822] , <PERSON><PERSON> [0000 - 0001 - 8513 - 3491] , <PERSON> [0000 - 0001 - 5761 - 0422] , [0000 - 0002 - 8088 - 0823]", "text": "<PERSON><PERSON><PERSON> [0000 - 0002 - 3723 - 6960] , <PERSON> [0000 - 0002 - 9468 - 0822] , <PERSON><PERSON> [0000 - 0001 - 8513 - 3491] , <PERSON> [0000 - 0001 - 5761 - 0422] , [0000 - 0002 - 8088 - 0823]"}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 229.52, "t": 596.42, "r": 298.61, "b": 587.62, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "and <PERSON>", "text": "and <PERSON>"}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 279.11, "t": 577.07, "r": 336.25, "b": 565.78, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "IBM Research", "text": "IBM Research"}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 222.97, "t": 563.19, "r": 392.39, "b": 555.72, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "{mly,ahn,nli,cau,taa}@zurich.ibm.com", "text": "{mly,ahn,nli,cau,taa}@zurich.ibm.com"}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 163.11, "t": 523.91, "r": 452.25, "b": 326.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1198]}], "orig": "Abstract. Extracting tables from documents is a crucial task in any document conversion pipeline. Recently, transformer-based models have demonstrated that table-structure can be recognized with impressive accuracy using Image-to-Markup-Sequence (Im2Seq) approaches. Taking only the image of a table, such models predict a sequence of tokens (e.g. in HTML, LaTeX) which represent the structure of the table. Since the token representation of the table structure has a significant impact on the accuracy and run-time performance of any Im2Seq model, we investigate in this paper how table-structure representation can be optimised. We propose a new, optimised table-structure language (OTSL) with a minimized vocabulary and specific rules. The benefits of OTSL are that it reduces the number of tokens to 5 (HTML needs 28+) and shortens the sequence length to half of HTML on average. Consequently, model accuracy improves significantly, inference time is halved compared to HTML-based models, and the predicted table structures are always syntactically correct. This in turn eliminates most post-processing needs. Popular table structure data-sets will be published in OTSL format to the community.", "text": "Abstract. Extracting tables from documents is a crucial task in any document conversion pipeline. Recently, transformer-based models have demonstrated that table-structure can be recognized with impressive accuracy using Image-to-Markup-Sequence (Im2Seq) approaches. Taking only the image of a table, such models predict a sequence of tokens (e.g. in HTML, LaTeX) which represent the structure of the table. Since the token representation of the table structure has a significant impact on the accuracy and run-time performance of any Im2Seq model, we investigate in this paper how table-structure representation can be optimised. We propose a new, optimised table-structure language (OTSL) with a minimized vocabulary and specific rules. The benefits of OTSL are that it reduces the number of tokens to 5 (HTML needs 28+) and shortens the sequence length to half of HTML on average. Consequently, model accuracy improves significantly, inference time is halved compared to HTML-based models, and the predicted table structures are always syntactically correct. This in turn eliminates most post-processing needs. Popular table structure data-sets will be published in OTSL format to the community."}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 163.11, "t": 315.52, "r": 452.24, "b": 293.27, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 90]}], "orig": "Keywords: Table Structure Recognition · Data Representation · Transformers · Optimization.", "text": "Keywords: Table Structure Recognition · Data Representation · Transformers · Optimization."}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 269.88, "r": 228.93, "b": 259.31, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "1 Introduction", "text": "1 Introduction", "level": 1}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 243.71, "r": 480.6, "b": 163.18, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 500]}], "orig": "Tables are ubiquitous in documents such as scientific papers, patents, reports, manuals, specification sheets or marketing material. They often encode highly valuable information and therefore need to be extracted with high accuracy. Unfortunately, tables appear in documents in various sizes, styling and structure, making it difficult to recover their correct structure with simple analytical methods. Therefore, accurate table extraction is achieved these days with machine-learning based methods.", "text": "Tables are ubiquitous in documents such as scientific papers, patents, reports, manuals, specification sheets or marketing material. They often encode highly valuable information and therefore need to be extracted with high accuracy. Unfortunately, tables appear in documents in various sizes, styling and structure, making it difficult to recover their correct structure with simple analytical methods. Therefore, accurate table extraction is achieved these days with machine-learning based methods."}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.76, "t": 159.85, "r": 480.6, "b": 127.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 235]}], "orig": "In modern document understanding systems [1,15], table extraction is typically a two-step process. Firstly, every table on a page is located with a bounding box, and secondly, their logical row and column structure is recognized. As of", "text": "In modern document understanding systems [1,15], table extraction is typically a two-step process. Firstly, every table on a page is located with a bounding box, and secondly, their logical row and column structure is recognized. As of"}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 134.76, "t": 700.51, "r": 139.37, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 2, "bbox": {"l": 167.81, "t": 700.51, "r": 231.72, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "<PERSON><PERSON>, et al.", "text": "<PERSON><PERSON>, et al."}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 2, "bbox": {"l": 134.76, "t": 667.88, "r": 480.59, "b": 590.84, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 574]}], "orig": "Fig. 1. Comparison between HTML and OTSL table structure representation: (A) table-example with complex row and column headers, including a 2D empty span, (B) minimal graphical representation of table structure using rectangular layout, (C) HTML representation, (D) OTSL representation. This example demonstrates many of the key-features of OTSL, namely its reduced vocabulary size (12 versus 5 in this case), its reduced sequence length (55 versus 30) and a enhanced internal structure (variable token sequence length per row in HTML versus a fixed length of rows in OTSL).", "text": "Fig. 1. Comparison between HTML and OTSL table structure representation: (A) table-example with complex row and column headers, including a 2D empty span, (B) minimal graphical representation of table structure using rectangular layout, (C) HTML representation, (D) OTSL representation. This example demonstrates many of the key-features of OTSL, namely its reduced vocabulary size (12 versus 5 in this case), its reduced sequence length (55 versus 30) and a enhanced internal structure (variable token sequence length per row in HTML versus a fixed length of rows in OTSL)."}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 396.41, "t": 510.91, "r": 402.97, "b": 502.52, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 418.59, "t": 510.99, "r": 425.15, "b": 502.61, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 395.75, "t": 488.65, "r": 414.1, "b": 480.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C C", "text": "C C"}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 407.56, "t": 477.49, "r": 414.13, "b": 469.1, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 418.51, "t": 499.88, "r": 447.25, "b": 491.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 418.62, "t": 488.67, "r": 447.36, "b": 480.21, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 418.62, "t": 477.39, "r": 447.36, "b": 468.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 407.4, "t": 466.6, "r": 447.2, "b": 458.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "C C C C", "text": "C C C C"}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 451.89, "t": 511.73, "r": 463.51, "b": 503.35, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 452.16, "t": 500.29, "r": 463.77, "b": 491.91, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 452.18, "t": 489.05, "r": 463.8, "b": 480.66, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 452.1, "t": 477.77, "r": 463.72, "b": 469.38, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 452.3, "t": 466.42, "r": 463.92, "b": 458.04, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 396.1, "t": 477.4, "r": 402.66, "b": 469.01, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 396.0, "t": 466.5, "r": 402.56, "b": 458.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 396.27, "t": 499.62, "r": 402.84, "b": 491.24, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 408.55, "t": 510.92, "r": 413.6, "b": 502.54, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 430.59, "t": 511.39, "r": 435.64, "b": 503.01, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 441.08, "t": 511.51, "r": 446.13, "b": 503.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 407.97, "t": 499.76, "r": 414.04, "b": 491.37, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "X", "text": "X"}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 441.26, "t": 380.71, "r": 452.87, "b": 372.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 393.75, "t": 392.11, "r": 432.49, "b": 385.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "vocabulary:", "text": "vocabulary:"}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 434.59, "t": 392.11, "r": 438.8, "b": 385.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "5", "text": "5"}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 440.91, "t": 392.11, "r": 463.22, "b": 385.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "tokens", "text": "tokens"}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 384.12, "t": 533.36, "r": 413.99, "b": 526.37, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "D OTSL", "text": "D OTSL"}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 393.75, "t": 525.23, "r": 451.45, "b": 518.25, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "sequence length:", "text": "sequence length:"}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 453.55, "t": 525.23, "r": 461.98, "b": 518.25, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "30", "text": "30"}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 151.79, "t": 392.15, "r": 233.89, "b": 385.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 26]}], "orig": "vocabulary for this table:", "text": "vocabulary for this table:"}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 235.99, "t": 392.15, "r": 244.42, "b": 385.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "12", "text": "12"}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 246.52, "t": 392.15, "r": 268.84, "b": 385.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "tokens", "text": "tokens"}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 154.33, "t": 578.33, "r": 159.8, "b": 571.35, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "A", "text": "A"}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 321.07, "t": 578.33, "r": 326.54, "b": 571.35, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "B", "text": "B"}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 153.09, "t": 511.61, "r": 175.84, "b": 505.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "<table>", "text": "<table>"}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 504.8, "r": 172.8, "b": 498.51, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<tr>", "text": "<tr>"}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 168.25, "t": 497.98, "r": 351.47, "b": 491.69, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 59]}], "orig": "<td colspan='2' rowspan='2' > </td> <td colspan='3' > </td>", "text": "<td colspan='2' rowspan='2' > </td> <td colspan='3' > </td>"}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 491.16, "r": 174.69, "b": 484.88, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "</tr>", "text": "</tr>"}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 484.35, "r": 172.8, "b": 478.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<tr>", "text": "<tr>"}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 168.25, "t": 477.53, "r": 265.27, "b": 471.24, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "<td> </td> <td> </td> <td> </td>", "text": "<td> </td> <td> </td> <td> </td>"}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 470.71, "r": 174.69, "b": 464.43, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "</tr>", "text": "</tr>"}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 463.9, "r": 172.8, "b": 457.61, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<tr>", "text": "<tr>"}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 168.25, "t": 457.08, "r": 373.09, "b": 450.79, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 67]}], "orig": "<td rowspan='3' > </td> <td> </td> <td> </td> <td> </td> <td> </td>", "text": "<td rowspan='3' > </td> <td> </td> <td> </td> <td> </td> <td> </td>"}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 450.26, "r": 174.69, "b": 443.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "</tr>", "text": "</tr>"}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 443.45, "r": 172.8, "b": 437.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<tr>", "text": "<tr>"}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 168.25, "t": 436.63, "r": 298.25, "b": 430.34, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 43]}], "orig": "<td> </td> <td> </td> <td> </td> <td> </td>", "text": "<td> </td> <td> </td> <td> </td> <td> </td>"}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 429.81, "r": 174.69, "b": 423.52, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "</tr>", "text": "</tr>"}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 423.0, "r": 172.8, "b": 416.71, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<tr>", "text": "<tr>"}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 168.25, "t": 416.18, "r": 298.25, "b": 409.89, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 43]}], "orig": "<td> </td> <td> </td> <td> </td> <td> </td>", "text": "<td> </td> <td> </td> <td> </td> <td> </td>"}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 160.67, "t": 409.36, "r": 174.69, "b": 403.07, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "</tr>", "text": "</tr>"}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 153.09, "t": 402.55, "r": 177.73, "b": 396.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "</table>", "text": "</table>"}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 395.06, "t": 380.56, "r": 412.48, "b": 372.17, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C L", "text": "C L"}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 418.69, "t": 380.56, "r": 436.57, "b": 372.17, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "U X", "text": "U X"}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 152.36, "t": 382.14, "r": 175.11, "b": 375.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "<table>", "text": "<table>"}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 178.89, "t": 382.14, "r": 191.02, "b": 375.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<tr>", "text": "<tr>"}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 194.81, "t": 382.14, "r": 208.83, "b": 375.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "</tr>", "text": "</tr>"}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 212.61, "t": 382.14, "r": 226.26, "b": 375.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<td>", "text": "<td>"}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 230.05, "t": 382.14, "r": 245.59, "b": 375.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "</td>", "text": "</td>"}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 249.38, "t": 382.14, "r": 259.04, "b": 375.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "<td", "text": "<td"}, {"self_ref": "#/texts/71", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 262.83, "t": 382.14, "r": 298.94, "b": 375.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "colspan=\"2\"", "text": "colspan=\"2\""}, {"self_ref": "#/texts/72", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 302.72, "t": 382.14, "r": 338.83, "b": 375.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "colspan=\"3\"", "text": "colspan=\"3\""}, {"self_ref": "#/texts/73", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 152.36, "t": 373.81, "r": 190.74, "b": 367.52, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "rowspan=\"2\"", "text": "rowspan=\"2\""}, {"self_ref": "#/texts/74", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 194.53, "t": 373.81, "r": 232.91, "b": 367.52, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "rowspan=\"3\"", "text": "rowspan=\"3\""}, {"self_ref": "#/texts/75", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 236.69, "t": 373.81, "r": 269.1, "b": 367.52, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "> </table>", "text": "> </table>"}, {"self_ref": "#/texts/76", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 154.51, "t": 533.31, "r": 185.22, "b": 526.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "C HTML", "text": "C HTML"}, {"self_ref": "#/texts/77", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 164.35, "t": 525.41, "r": 222.05, "b": 518.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "sequence length:", "text": "sequence length:"}, {"self_ref": "#/texts/78", "parent": {"$ref": "#/pictures/0"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 224.15, "t": 525.41, "r": 232.58, "b": 518.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "55", "text": "55"}, {"self_ref": "#/texts/79", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 134.76, "t": 339.69, "r": 480.59, "b": 271.11, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 435]}], "orig": "today, table detection in documents is a well understood problem, and the latest state-of-the-art (SOTA) object detection methods provide an accuracy comparable to human observers [7,8,10,14,23]. On the other hand, the problem of table structure recognition (TSR) is a lot more challenging and remains a very active area of research, in which many novel machine learning algorithms are being explored [3,4,5,9,11,12,13,14,17,18,21,22].", "text": "today, table detection in documents is a well understood problem, and the latest state-of-the-art (SOTA) object detection methods provide an accuracy comparable to human observers [7,8,10,14,23]. On the other hand, the problem of table structure recognition (TSR) is a lot more challenging and remains a very active area of research, in which many novel machine learning algorithms are being explored [3,4,5,9,11,12,13,14,17,18,21,22]."}, {"self_ref": "#/texts/80", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 134.76, "t": 267.45, "r": 480.6, "b": 127.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 911]}], "orig": "Recently emerging SOTA methods for table structure recognition employ transformer-based models, in which an image of the table is provided to the network in order to predict the structure of the table as a sequence of tokens. These image-to-sequence (Im2Seq) models are extremely powerful, since they allow for a purely data-driven solution. The tokens of the sequence typically belong to a markup language such as HTML, Latex or Markdown, which allow to describe table structure as rows, columns and spanning cells in various configurations. In Figure 1, we illustrate how HTML is used to represent the table-structure of a particular example table. Public table-structure data sets such as PubTabNet [22], and FinTabNet [21], which were created in a semi-automated way from paired PDF and HTML sources (e.g. PubMed Central), popularized primarily the use of HTML as ground-truth representation format for TSR.", "text": "Recently emerging SOTA methods for table structure recognition employ transformer-based models, in which an image of the table is provided to the network in order to predict the structure of the table as a sequence of tokens. These image-to-sequence (Im2Seq) models are extremely powerful, since they allow for a purely data-driven solution. The tokens of the sequence typically belong to a markup language such as HTML, Latex or Markdown, which allow to describe table structure as rows, columns and spanning cells in various configurations. In Figure 1, we illustrate how HTML is used to represent the table-structure of a particular example table. Public table-structure data sets such as PubTabNet [22], and FinTabNet [21], which were created in a semi-automated way from paired PDF and HTML sources (e.g. PubMed Central), popularized primarily the use of HTML as ground-truth representation format for TSR."}, {"self_ref": "#/texts/81", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 3, "bbox": {"l": 194.48, "t": 700.51, "r": 447.54, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Optimized Table Tokenization for Table Structure Recognition", "text": "Optimized Table Tokenization for Table Structure Recognition"}, {"self_ref": "#/texts/82", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 3, "bbox": {"l": 475.98, "t": 700.51, "r": 480.59, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "3", "text": "3"}, {"self_ref": "#/texts/83", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 134.76, "t": 673.07, "r": 480.59, "b": 580.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 584]}], "orig": "While the majority of research in TSR is currently focused on the development and application of novel neural model architectures, the table structure representation language (e.g. HTML in PubTabNet and FinTabNet) is usually adopted as is for the sequence tokenization in Im2Seq models. In this paper, we aim for the opposite and investigate the impact of the table structure representation language with an otherwise unmodified Im2Seq transformer-based architecture. Since the current state-of-the-art Im2Seq model is TableFormer [9], we select this model to perform our experiments.", "text": "While the majority of research in TSR is currently focused on the development and application of novel neural model architectures, the table structure representation language (e.g. HTML in PubTabNet and FinTabNet) is usually adopted as is for the sequence tokenization in Im2Seq models. In this paper, we aim for the opposite and investigate the impact of the table structure representation language with an otherwise unmodified Im2Seq transformer-based architecture. Since the current state-of-the-art Im2Seq model is TableFormer [9], we select this model to perform our experiments."}, {"self_ref": "#/texts/84", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 134.76, "t": 577.16, "r": 480.6, "b": 460.77, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 721]}], "orig": "The main contribution of this paper is the introduction of a new optimised table structure language (OTSL), specifically designed to describe table-structure in an compact and structured way for Im2Seq models. OTSL has a number of key features, which make it very attractive to use in Im2Seq models. Specifically, compared to other languages such as HTML, OTSL has a minimized vocabulary which yields short sequence length, strong inherent structure (e.g. strict rectangular layout) and a strict syntax with rules that only look backwards. The latter allows for syntax validation during inference and ensures a syntactically correct table-structure. These OTSL features are illustrated in Figure 1, in comparison to HTML.", "text": "The main contribution of this paper is the introduction of a new optimised table structure language (OTSL), specifically designed to describe table-structure in an compact and structured way for Im2Seq models. OTSL has a number of key features, which make it very attractive to use in Im2Seq models. Specifically, compared to other languages such as HTML, OTSL has a minimized vocabulary which yields short sequence length, strong inherent structure (e.g. strict rectangular layout) and a strict syntax with rules that only look backwards. The latter allows for syntax validation during inference and ensures a syntactically correct table-structure. These OTSL features are illustrated in Figure 1, in comparison to HTML."}, {"self_ref": "#/texts/85", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 134.76, "t": 457.35, "r": 480.6, "b": 352.91, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 626]}], "orig": "The paper is structured as follows. In section 2, we give an overview of the latest developments in table-structure reconstruction. In section 3 we review the current HTML table encoding (popularised by PubTabNet and FinTabNet) and discuss its flaws. Subsequently, we introduce OTSL in section 4, which includes the language definition, syntax rules and error-correction procedures. In section 5, we apply OTSL on the TableFormer architecture, compare it to TableFormer models trained on HTML and ultimately demonstrate the advantages of using OTSL. Finally, in section 6 we conclude our work and outline next potential steps.", "text": "The paper is structured as follows. In section 2, we give an overview of the latest developments in table-structure reconstruction. In section 3 we review the current HTML table encoding (popularised by PubTabNet and FinTabNet) and discuss its flaws. Subsequently, we introduce OTSL in section 4, which includes the language definition, syntax rules and error-correction procedures. In section 5, we apply OTSL on the TableFormer architecture, compare it to TableFormer models trained on HTML and ultimately demonstrate the advantages of using OTSL. Finally, in section 6 we conclude our work and outline next potential steps."}, {"self_ref": "#/texts/86", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 3, "bbox": {"l": 134.76, "t": 329.91, "r": 236.77, "b": 319.34, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "2 Related Work", "text": "2 Related Work", "level": 1}, {"self_ref": "#/texts/87", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 134.76, "t": 303.31, "r": 484.12, "b": 127.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1161]}], "orig": "Approaches to formalize the logical structure and layout of tables in electronic documents date back more than two decades [16]. In the recent past, a wide variety of computer vision methods have been explored to tackle the problem of table structure recognition, i.e. the correct identification of columns, rows and spanning cells in a given table. Broadly speaking, the current deeplearning based approaches fall into three categories: object detection (OD) methods, Graph-Neural-Network (GNN) methods and Image-to-Markup-Sequence (Im2Seq) methods. Object-detection based methods [11,12,13,14,21] rely on tablestructure annotation using (overlapping) bounding boxes for training, and produce bounding-box predictions to define table cells, rows, and columns on a table image. Graph Neural Network (GNN) based methods [3,6,17,18], as the name suggests, represent tables as graph structures. The graph nodes represent the content of each table cell, an embedding vector from the table image, or geometric coordinates of the table cell. The edges of the graph define the relationship between the nodes, e.g. if they belong to the same column, row, or table cell.", "text": "Approaches to formalize the logical structure and layout of tables in electronic documents date back more than two decades [16]. In the recent past, a wide variety of computer vision methods have been explored to tackle the problem of table structure recognition, i.e. the correct identification of columns, rows and spanning cells in a given table. Broadly speaking, the current deeplearning based approaches fall into three categories: object detection (OD) methods, Graph-Neural-Network (GNN) methods and Image-to-Markup-Sequence (Im2Seq) methods. Object-detection based methods [11,12,13,14,21] rely on tablestructure annotation using (overlapping) bounding boxes for training, and produce bounding-box predictions to define table cells, rows, and columns on a table image. Graph Neural Network (GNN) based methods [3,6,17,18], as the name suggests, represent tables as graph structures. The graph nodes represent the content of each table cell, an embedding vector from the table image, or geometric coordinates of the table cell. The edges of the graph define the relationship between the nodes, e.g. if they belong to the same column, row, or table cell."}, {"self_ref": "#/texts/88", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 4, "bbox": {"l": 134.76, "t": 700.51, "r": 139.37, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "4", "text": "4"}, {"self_ref": "#/texts/89", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 4, "bbox": {"l": 167.81, "t": 700.51, "r": 231.72, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "<PERSON><PERSON>, et al.", "text": "<PERSON><PERSON>, et al."}, {"self_ref": "#/texts/90", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 134.76, "t": 673.07, "r": 480.6, "b": 532.76, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 939]}], "orig": "Other work [20] aims at predicting a grid for each table and deciding which cells must be merged using an attention network. Im2Seq methods cast the problem as a sequence generation task [4,5,9,22], and therefore need an internal tablestructure representation language, which is often implemented with standard markup languages (e.g. HTML, LaTeX, Markdown). In theory, Im2Seq methods have a natural advantage over the OD and GNN methods by virtue of directly predicting the table-structure. As such, no post-processing or rules are needed in order to obtain the table-structure, which is necessary with OD and GNN approaches. In practice, this is not entirely true, because a predicted sequence of table-structure markup does not necessarily have to be syntactically correct. Hence, depending on the quality of the predicted sequence, some post-processing needs to be performed to ensure a syntactically valid (let alone correct) sequence.", "text": "Other work [20] aims at predicting a grid for each table and deciding which cells must be merged using an attention network. Im2Seq methods cast the problem as a sequence generation task [4,5,9,22], and therefore need an internal tablestructure representation language, which is often implemented with standard markup languages (e.g. HTML, LaTeX, Markdown). In theory, Im2Seq methods have a natural advantage over the OD and GNN methods by virtue of directly predicting the table-structure. As such, no post-processing or rules are needed in order to obtain the table-structure, which is necessary with OD and GNN approaches. In practice, this is not entirely true, because a predicted sequence of table-structure markup does not necessarily have to be syntactically correct. Hence, depending on the quality of the predicted sequence, some post-processing needs to be performed to ensure a syntactically valid (let alone correct) sequence."}, {"self_ref": "#/texts/91", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 134.76, "t": 529.34, "r": 480.6, "b": 305.35, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1404]}], "orig": "Within the Im2Seq method, we find several popular models, namely the encoder-dual-decoder model (EDD) [22], TableFormer [9], <PERSON><PERSON><PERSON><PERSON><PERSON>[2] and <PERSON> et. al. [19]. EDD uses two consecutive long short-term memory (LSTM) decoders to predict a table in HTML representation. The tag decoder predicts a sequence of HTML tags. For each decoded table cell ( <td> ), the attention is passed to the cell decoder to predict the content with an embedded OCR approach. The latter makes it susceptible to transcription errors in the cell content of the table. TableFormer address this reliance on OCR and uses two transformer decoders for HTML structure and cell bounding box prediction in an end-to-end architecture. The predicted cell bounding box is then used to extract text tokens from an originating (digital) PDF page, circumventing any need for OCR. TabSplitter [2] proposes a compact double-matrix representation of table rows and columns to do error detection and error correction of HTML structure sequences based on predictions from [19]. This compact double-matrix representation can not be used directly by the Img2seq model training, so the model uses HTML as an intermediate form. <PERSON> et. al. [4] introduce a data set and a baseline method using bidirectional LSTMs to predict LaTeX code. <PERSON><PERSON> [5] introduces Gated ResNet transformers to predict LaTeX code, and a separate OCR module to extract content.", "text": "Within the Im2Seq method, we find several popular models, namely the encoder-dual-decoder model (EDD) [22], TableFormer [9], <PERSON><PERSON><PERSON><PERSON><PERSON>[2] and <PERSON> et. al. [19]. EDD uses two consecutive long short-term memory (LSTM) decoders to predict a table in HTML representation. The tag decoder predicts a sequence of HTML tags. For each decoded table cell ( <td> ), the attention is passed to the cell decoder to predict the content with an embedded OCR approach. The latter makes it susceptible to transcription errors in the cell content of the table. TableFormer address this reliance on OCR and uses two transformer decoders for HTML structure and cell bounding box prediction in an end-to-end architecture. The predicted cell bounding box is then used to extract text tokens from an originating (digital) PDF page, circumventing any need for OCR. TabSplitter [2] proposes a compact double-matrix representation of table rows and columns to do error detection and error correction of HTML structure sequences based on predictions from [19]. This compact double-matrix representation can not be used directly by the Img2seq model training, so the model uses HTML as an intermediate form. <PERSON> et. al. [4] introduce a data set and a baseline method using bidirectional LSTMs to predict LaTeX code. <PERSON><PERSON> [5] introduces Gated ResNet transformers to predict LaTeX code, and a separate OCR module to extract content."}, {"self_ref": "#/texts/92", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 134.76, "t": 301.93, "r": 480.59, "b": 209.45, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 572]}], "orig": "Im2Seq approaches have shown to be well-suited for the TSR task and allow a full end-to-end network design that can output the final table structure without pre- or post-processing logic. Furthermore, Im2Seq models have demonstrated to deliver state-of-the-art prediction accuracy [9]. This motivated the authors to investigate if the performance (both in accuracy and inference time) can be further improved by optimising the table structure representation language. We believe this is a necessary step before further improving neural network architectures for this task.", "text": "Im2Seq approaches have shown to be well-suited for the TSR task and allow a full end-to-end network design that can output the final table structure without pre- or post-processing logic. Furthermore, Im2Seq models have demonstrated to deliver state-of-the-art prediction accuracy [9]. This motivated the authors to investigate if the performance (both in accuracy and inference time) can be further improved by optimising the table structure representation language. We believe this is a necessary step before further improving neural network architectures for this task."}, {"self_ref": "#/texts/93", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 4, "bbox": {"l": 134.76, "t": 186.45, "r": 269.62, "b": 175.88, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 19]}], "orig": "3 Problem Statement", "text": "3 Problem Statement", "level": 1}, {"self_ref": "#/texts/94", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 4, "bbox": {"l": 134.76, "t": 159.85, "r": 480.59, "b": 127.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 233]}], "orig": "All known Im2Seq based models for TSR fundamentally work in similar ways. Given an image of a table, the Im2Seq model predicts the structure of the table by generating a sequence of tokens. These tokens originate from a finite vocab-", "text": "All known Im2Seq based models for TSR fundamentally work in similar ways. Given an image of a table, the Im2Seq model predicts the structure of the table by generating a sequence of tokens. These tokens originate from a finite vocab-"}, {"self_ref": "#/texts/95", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 5, "bbox": {"l": 194.48, "t": 700.51, "r": 447.54, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Optimized Table Tokenization for Table Structure Recognition", "text": "Optimized Table Tokenization for Table Structure Recognition"}, {"self_ref": "#/texts/96", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 5, "bbox": {"l": 475.98, "t": 700.51, "r": 480.59, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "5", "text": "5"}, {"self_ref": "#/texts/97", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 134.76, "t": 673.07, "r": 480.59, "b": 604.49, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 422]}], "orig": "ulary and can be interpreted as a table structure. For example, with the HTML tokens <table> , </table> , <tr> , </tr> , <td> and </td> , one can construct simple table structures without any spanning cells. In reality though, one needs at least 28 HTML tokens to describe the most common complex tables observed in real-world documents [21,22], due to a variety of spanning cells definitions in the HTML token vocabulary.", "text": "ulary and can be interpreted as a table structure. For example, with the HTML tokens <table> , </table> , <tr> , </tr> , <td> and </td> , one can construct simple table structures without any spanning cells. In reality though, one needs at least 28 HTML tokens to describe the most common complex tables observed in real-world documents [21,22], due to a variety of spanning cells definitions in the HTML token vocabulary."}, {"self_ref": "#/texts/98", "parent": {"$ref": "#/pictures/1"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 5, "bbox": {"l": 145.61, "t": 573.14, "r": 469.75, "b": 561.85, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 73]}], "orig": "Fig. 2. Frequency of tokens in HTML and OTSL as they appear in PubTabNet.", "text": "Fig. 2. Frequency of tokens in HTML and OTSL as they appear in PubTabNet."}, {"self_ref": "#/texts/99", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 134.76, "t": 423.79, "r": 480.6, "b": 259.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1021]}], "orig": "Obviously, HTML and other general-purpose markup languages were not designed for Im2Seq models. As such, they have some serious drawbacks. First, the token vocabulary needs to be artificially large in order to describe all plausible tabular structures. Since most Im2Seq models use an autoregressive approach, they generate the sequence token by token. Therefore, to reduce inference time, a shorter sequence length is critical. Every table-cell is represented by at least two tokens ( <td> and </td> ). Furthermore, when tokenizing the HTML structure, one needs to explicitly enumerate possible column-spans and row-spans as words. In practice, this ends up requiring 28 different HTML tokens (when including column- and row-spans up to 10 cells) just to describe every table in the PubTabNet dataset. Clearly, not every token is equally represented, as is depicted in Figure 2. This skewed distribution of tokens in combination with variable token row-length makes it challenging for models to learn the HTML structure.", "text": "Obviously, HTML and other general-purpose markup languages were not designed for Im2Seq models. As such, they have some serious drawbacks. First, the token vocabulary needs to be artificially large in order to describe all plausible tabular structures. Since most Im2Seq models use an autoregressive approach, they generate the sequence token by token. Therefore, to reduce inference time, a shorter sequence length is critical. Every table-cell is represented by at least two tokens ( <td> and </td> ). Furthermore, when tokenizing the HTML structure, one needs to explicitly enumerate possible column-spans and row-spans as words. In practice, this ends up requiring 28 different HTML tokens (when including column- and row-spans up to 10 cells) just to describe every table in the PubTabNet dataset. Clearly, not every token is equally represented, as is depicted in Figure 2. This skewed distribution of tokens in combination with variable token row-length makes it challenging for models to learn the HTML structure."}, {"self_ref": "#/texts/100", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 134.76, "t": 255.96, "r": 480.59, "b": 211.29, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 313]}], "orig": "Additionally, it would be desirable if the representation would easily allow an early detection of invalid sequences on-the-go, before the prediction of the entire table structure is completed. HTML is not well-suited for this purpose as the verification of incomplete sequences is non-trivial or even impossible.", "text": "Additionally, it would be desirable if the representation would easily allow an early detection of invalid sequences on-the-go, before the prediction of the entire table structure is completed. HTML is not well-suited for this purpose as the verification of incomplete sequences is non-trivial or even impossible."}, {"self_ref": "#/texts/101", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 5, "bbox": {"l": 134.76, "t": 207.67, "r": 480.6, "b": 127.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 542]}], "orig": "In a valid HTML table, the token sequence must describe a 2D grid of table cells, serialised in row-major ordering, where each row and each column have the same length (while considering row- and column-spans). Furthermore, every opening tag in HTML needs to be matched by a closing tag in a correct hierarchical manner. Since the number of tokens for each table row and column can vary significantly, especially for large tables with many row- and column-spans, it is complex to verify the consistency of predicted structures during sequence", "text": "In a valid HTML table, the token sequence must describe a 2D grid of table cells, serialised in row-major ordering, where each row and each column have the same length (while considering row- and column-spans). Furthermore, every opening tag in HTML needs to be matched by a closing tag in a correct hierarchical manner. Since the number of tokens for each table row and column can vary significantly, especially for large tables with many row- and column-spans, it is complex to verify the consistency of predicted structures during sequence"}, {"self_ref": "#/texts/102", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 6, "bbox": {"l": 134.76, "t": 700.51, "r": 139.37, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "6", "text": "6"}, {"self_ref": "#/texts/103", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 6, "bbox": {"l": 167.81, "t": 700.51, "r": 231.72, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "<PERSON><PERSON>, et al.", "text": "<PERSON><PERSON>, et al."}, {"self_ref": "#/texts/104", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 134.76, "t": 673.07, "r": 480.6, "b": 652.31, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 132]}], "orig": "generation. Implicitly, this also means that Im2Seq models need to learn these complex syntax rules, simply to deliver valid output.", "text": "generation. Implicitly, this also means that Im2Seq models need to learn these complex syntax rules, simply to deliver valid output."}, {"self_ref": "#/texts/105", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 134.76, "t": 648.52, "r": 480.6, "b": 496.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 977]}], "orig": "In practice, we observe two major issues with prediction quality when training Im2Seq models on HTML table structure generation from images. On the one hand, we find that on large tables, the visual attention of the model often starts to drift and is not accurately moving forward cell by cell anymore. This manifests itself in either in an increasing location drift for proposed table-cells in later rows on the same column or even complete loss of vertical alignment, as illustrated in Figure 5. Addressing this with post-processing is partially possible, but clearly undesired. On the other hand, we find many instances of predictions with structural inconsistencies or plain invalid HTML output, as shown in Figure 6, which are nearly impossible to properly correct. Both problems seriously impact the TSR model performance, since they reflect not only in the task of pure structure recognition but also in the equally crucial recognition or matching of table cell content.", "text": "In practice, we observe two major issues with prediction quality when training Im2Seq models on HTML table structure generation from images. On the one hand, we find that on large tables, the visual attention of the model often starts to drift and is not accurately moving forward cell by cell anymore. This manifests itself in either in an increasing location drift for proposed table-cells in later rows on the same column or even complete loss of vertical alignment, as illustrated in Figure 5. Addressing this with post-processing is partially possible, but clearly undesired. On the other hand, we find many instances of predictions with structural inconsistencies or plain invalid HTML output, as shown in Figure 6, which are nearly impossible to properly correct. Both problems seriously impact the TSR model performance, since they reflect not only in the task of pure structure recognition but also in the equally crucial recognition or matching of table cell content."}, {"self_ref": "#/texts/106", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 134.76, "t": 471.37, "r": 372.51, "b": 460.8, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 36]}], "orig": "4 Optimised Table Structure Language", "text": "4 Optimised Table Structure Language", "level": 1}, {"self_ref": "#/texts/107", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 134.76, "t": 442.88, "r": 480.6, "b": 350.4, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 563]}], "orig": "To mitigate the issues with HTML in Im2Seq-based TSR models laid out before, we propose here our Optimised Table Structure Language (OTSL). OTSL is designed to express table structure with a minimized vocabulary and a simple set of rules, which are both significantly reduced compared to HTML. At the same time, OTSL enables easy error detection and correction during sequence generation. We further demonstrate how the compact structure representation and minimized sequence length improves prediction accuracy and inference time in the TableFormer architecture.", "text": "To mitigate the issues with HTML in Im2Seq-based TSR models laid out before, we propose here our Optimised Table Structure Language (OTSL). OTSL is designed to express table structure with a minimized vocabulary and a simple set of rules, which are both significantly reduced compared to HTML. At the same time, OTSL enables easy error detection and correction during sequence generation. We further demonstrate how the compact structure representation and minimized sequence length improves prediction accuracy and inference time in the TableFormer architecture."}, {"self_ref": "#/texts/108", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 6, "bbox": {"l": 134.76, "t": 326.13, "r": 261.8, "b": 317.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "4.1 Language Definition", "text": "4.1 Language Definition", "level": 1}, {"self_ref": "#/texts/109", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 134.76, "t": 303.0, "r": 480.59, "b": 270.29, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 165]}], "orig": "In Figure 3, we illustrate how the OTSL is defined. In essence, the OTSL defines only 5 tokens that directly describe a tabular structure based on an atomic 2D grid.", "text": "In Figure 3, we illustrate how the OTSL is defined. In essence, the OTSL defines only 5 tokens that directly describe a tabular structure based on an atomic 2D grid."}, {"self_ref": "#/texts/110", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 149.71, "t": 266.5, "r": 409.31, "b": 257.7, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 57]}], "orig": "The OTSL vocabulary is comprised of the following tokens:", "text": "The OTSL vocabulary is comprised of the following tokens:"}, {"self_ref": "#/texts/111", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 140.99, "t": 244.03, "r": 460.54, "b": 235.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 72]}], "orig": "-\"C\" cell a new table cell that either has or does not have cell content", "text": "-\"C\" cell a new table cell that either has or does not have cell content", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/112", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 140.99, "t": 231.44, "r": 480.59, "b": 210.67, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 82]}], "orig": "-\"L\" cell left-looking cell , merging with the left neighbor cell to create a span", "text": "-\"L\" cell left-looking cell , merging with the left neighbor cell to create a span", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/113", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 140.99, "t": 206.89, "r": 480.59, "b": 186.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 81]}], "orig": "-\"U\" cell up-looking cell , merging with the upper neighbor cell to create a span", "text": "-\"U\" cell up-looking cell , merging with the upper neighbor cell to create a span", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/114", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 140.99, "t": 182.34, "r": 454.56, "b": 173.53, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 71]}], "orig": "-\"X\" cell cross cell , to merge with both left and upper neighbor cells", "text": "-\"X\" cell cross cell , to merge with both left and upper neighbor cells", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/115", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 6, "bbox": {"l": 140.99, "t": 169.75, "r": 328.62, "b": 160.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "-\"NL\" new-line , switch to the next row.", "text": "-\"NL\" new-line , switch to the next row.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/116", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 6, "bbox": {"l": 134.76, "t": 147.9, "r": 480.59, "b": 127.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 99]}], "orig": "A notable attribute of OTSL is that it has the capability of achieving lossless conversion to HTML.", "text": "A notable attribute of OTSL is that it has the capability of achieving lossless conversion to HTML."}, {"self_ref": "#/texts/117", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 7, "bbox": {"l": 194.48, "t": 700.51, "r": 447.54, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Optimized Table Tokenization for Table Structure Recognition", "text": "Optimized Table Tokenization for Table Structure Recognition"}, {"self_ref": "#/texts/118", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 7, "bbox": {"l": 475.98, "t": 700.51, "r": 480.59, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "7", "text": "7"}, {"self_ref": "#/texts/119", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 7, "bbox": {"l": 134.76, "t": 668.42, "r": 480.59, "b": 635.21, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 207]}], "orig": "Fig. 3. OTSL description of table structure: A - table example; B - graphical representation of table structure; C - mapping structure on a grid; D - OTSL structure encoding; E - explanation on cell encoding", "text": "Fig. 3. OTSL description of table structure: A - table example; B - graphical representation of table structure; C - mapping structure on a grid; D - OTSL structure encoding; E - explanation on cell encoding"}, {"self_ref": "#/texts/120", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 374.49, "t": 623.29, "r": 381.67, "b": 614.12, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/121", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 398.74, "t": 623.38, "r": 405.92, "b": 614.21, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/122", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 373.77, "t": 598.96, "r": 393.84, "b": 589.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C C", "text": "C C"}, {"self_ref": "#/texts/123", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 386.69, "t": 586.74, "r": 393.86, "b": 577.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/124", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 398.66, "t": 611.23, "r": 430.08, "b": 601.98, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/125", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 398.78, "t": 598.97, "r": 430.2, "b": 589.72, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/126", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 398.78, "t": 586.64, "r": 430.2, "b": 577.4, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/127", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 386.51, "t": 574.84, "r": 430.02, "b": 565.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "C C C C", "text": "C C C C"}, {"self_ref": "#/texts/128", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 435.16, "t": 624.19, "r": 447.86, "b": 615.02, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/129", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 435.44, "t": 611.68, "r": 448.15, "b": 602.51, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/130", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 435.47, "t": 599.39, "r": 448.17, "b": 590.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/131", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 435.38, "t": 587.05, "r": 448.08, "b": 577.88, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/132", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 435.6, "t": 574.65, "r": 448.3, "b": 565.48, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/133", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 374.15, "t": 586.65, "r": 381.32, "b": 577.48, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/134", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 374.04, "t": 574.73, "r": 381.22, "b": 565.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/135", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 374.34, "t": 610.95, "r": 381.52, "b": 601.78, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/136", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 387.76, "t": 623.3, "r": 393.29, "b": 614.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/137", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 411.86, "t": 623.82, "r": 417.39, "b": 614.65, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/138", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 423.34, "t": 623.95, "r": 428.86, "b": 614.78, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/139", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 387.14, "t": 611.1, "r": 393.76, "b": 601.93, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "X", "text": "X"}, {"self_ref": "#/texts/140", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 282.26, "t": 547.37, "r": 289.44, "b": 538.2, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/141", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 282.11, "t": 535.03, "r": 289.29, "b": 525.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/142", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 282.41, "t": 522.75, "r": 289.58, "b": 513.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/143", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 295.53, "t": 547.39, "r": 301.06, "b": 538.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/144", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 307.47, "t": 547.31, "r": 312.99, "b": 538.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/145", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 318.77, "t": 547.44, "r": 324.29, "b": 538.27, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/146", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 294.9, "t": 535.58, "r": 325.59, "b": 526.01, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "X X X", "text": "X X X"}, {"self_ref": "#/texts/147", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 294.79, "t": 523.03, "r": 325.48, "b": 513.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "X X X", "text": "X X X"}, {"self_ref": "#/texts/148", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 195.94, "t": 523.15, "r": 214.73, "b": 513.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C L", "text": "C L"}, {"self_ref": "#/texts/149", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 221.15, "t": 523.07, "r": 226.67, "b": 513.9, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/150", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 232.45, "t": 523.2, "r": 237.97, "b": 514.03, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/151", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 196.22, "t": 547.34, "r": 203.39, "b": 538.17, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/152", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 250.32, "t": 547.78, "r": 257.5, "b": 538.62, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/153", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 250.17, "t": 535.44, "r": 257.35, "b": 526.27, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/154", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 250.47, "t": 523.16, "r": 257.65, "b": 513.99, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "U", "text": "U"}, {"self_ref": "#/texts/155", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 334.51, "t": 548.93, "r": 391.5, "b": 542.81, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "1 - simple cells: \"C\"", "text": "1 - simple cells: \"C\""}, {"self_ref": "#/texts/156", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 334.51, "t": 538.99, "r": 421.99, "b": 532.88, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "2 - horizontal merges: \"C\", \"L\"", "text": "2 - horizontal merges: \"C\", \"L\""}, {"self_ref": "#/texts/157", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 334.51, "t": 529.05, "r": 415.34, "b": 522.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 29]}], "orig": "3 - vertical merges: \"C\", \"U\"", "text": "3 - vertical merges: \"C\", \"U\""}, {"self_ref": "#/texts/158", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 334.51, "t": 519.11, "r": 426.6, "b": 513.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 33]}], "orig": "4 - 2d merges: \"C\", \"L\", \"U\", \"X\"", "text": "4 - 2d merges: \"C\", \"L\", \"U\", \"X\"", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/159", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 185.67, "t": 547.88, "r": 189.35, "b": 541.77, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/160", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 185.97, "t": 523.57, "r": 189.65, "b": 517.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/161", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 239.34, "t": 548.29, "r": 243.03, "b": 542.18, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "3", "text": "3"}, {"self_ref": "#/texts/162", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 271.33, "t": 548.43, "r": 275.01, "b": 542.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "4", "text": "4"}, {"self_ref": "#/texts/163", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 229.82, "t": 625.41, "r": 233.5, "b": 619.29, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/164", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 257.24, "t": 601.96, "r": 260.93, "b": 595.85, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/165", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 186.88, "t": 613.94, "r": 190.56, "b": 607.83, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "3", "text": "3"}, {"self_ref": "#/texts/166", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 196.49, "t": 622.9, "r": 200.17, "b": 616.79, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "4", "text": "4"}, {"self_ref": "#/texts/167", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 169.75, "t": 624.02, "r": 175.73, "b": 616.38, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "A", "text": "A"}, {"self_ref": "#/texts/168", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 169.75, "t": 585.06, "r": 175.73, "b": 577.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "B", "text": "B"}, {"self_ref": "#/texts/169", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 274.29, "t": 623.62, "r": 280.27, "b": 615.98, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/170", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 359.56, "t": 623.62, "r": 365.54, "b": 615.98, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "D", "text": "D"}, {"self_ref": "#/texts/171", "parent": {"$ref": "#/pictures/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 169.75, "t": 548.69, "r": 175.27, "b": 541.05, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "E", "text": "E"}, {"self_ref": "#/texts/172", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 7, "bbox": {"l": 134.76, "t": 486.7, "r": 246.65, "b": 477.9, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 19]}], "orig": "4.2 Language Syntax", "text": "4.2 Language Syntax", "level": 1}, {"self_ref": "#/texts/173", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 134.76, "t": 466.75, "r": 363.8, "b": 457.95, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 51]}], "orig": "The OTSL representation follows these syntax rules:", "text": "The OTSL representation follows these syntax rules:"}, {"self_ref": "#/texts/174", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 138.97, "t": 444.83, "r": 480.59, "b": 424.07, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 108]}], "orig": "1. Left-looking cell rule : The left neighbour of an \"L\" cell must be either another \"L\" cell or a \"C\" cell.", "text": "Left-looking cell rule : The left neighbour of an \"L\" cell must be either another \"L\" cell or a \"C\" cell.", "enumerated": true, "marker": "1."}, {"self_ref": "#/texts/175", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 138.97, "t": 420.92, "r": 480.59, "b": 400.15, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 106]}], "orig": "2. Up-looking cell rule : The upper neighbour of a \"U\" cell must be either another \"U\" cell or a \"C\" cell.", "text": "Up-looking cell rule : The upper neighbour of a \"U\" cell must be either another \"U\" cell or a \"C\" cell.", "enumerated": true, "marker": "2."}, {"self_ref": "#/texts/176", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 7, "bbox": {"l": 138.97, "t": 397.0, "r": 226.07, "b": 388.19, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "3. Cross cell rule :", "text": "3. Cross cell rule :", "level": 1}, {"self_ref": "#/texts/177", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 151.7, "t": 385.03, "r": 480.59, "b": 352.33, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 167]}], "orig": "The left neighbour of an \"X\" cell must be either another \"X\" cell or a \"U\" cell, and the upper neighbour of an \"X\" cell must be either another \"X\" cell or an \"L\" cell.", "text": "The left neighbour of an \"X\" cell must be either another \"X\" cell or a \"U\" cell, and the upper neighbour of an \"X\" cell must be either another \"X\" cell or an \"L\" cell.", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/178", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 138.97, "t": 349.17, "r": 474.59, "b": 340.37, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 78]}], "orig": "4. First row rule : Only \"L\" cells and \"C\" cells are allowed in the first row.", "text": "First row rule : Only \"L\" cells and \"C\" cells are allowed in the first row.", "enumerated": true, "marker": "4."}, {"self_ref": "#/texts/179", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 138.97, "t": 337.22, "r": 480.59, "b": 316.45, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 84]}], "orig": "5. First column rule : Only \"U\" cells and \"C\" cells are allowed in the first column.", "text": "First column rule : Only \"U\" cells and \"C\" cells are allowed in the first column.", "enumerated": true, "marker": "5."}, {"self_ref": "#/texts/180", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 7, "bbox": {"l": 138.97, "t": 313.3, "r": 480.6, "b": 292.54, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 144]}], "orig": "6. Rectangular rule : The table representation is always rectangular - all rows must have an equal number of tokens, terminated with \"NL\" token.", "text": "Rectangular rule : The table representation is always rectangular - all rows must have an equal number of tokens, terminated with \"NL\" token.", "enumerated": true, "marker": "6."}, {"self_ref": "#/texts/181", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 134.76, "t": 279.41, "r": 480.6, "b": 151.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 848]}], "orig": "The application of these rules gives OTSL a set of unique properties. First of all, the OTSL enforces a strictly rectangular structure representation, where every new-line token starts a new row. As a consequence, all rows and all columns have exactly the same number of tokens, irrespective of cell spans. Secondly, the OTSL representation is unambiguous: Every table structure is represented in one way. In this representation every table cell corresponds to a \"C\"-cell token, which in case of spans is always located in the top-left corner of the table cell definition. Third, OTSL syntax rules are only backward-looking. As a consequence, every predicted token can be validated straight during sequence generation by looking at the previously predicted sequence. As such, OTSL can guarantee that every predicted sequence is syntactically valid.", "text": "The application of these rules gives OTSL a set of unique properties. First of all, the OTSL enforces a strictly rectangular structure representation, where every new-line token starts a new row. As a consequence, all rows and all columns have exactly the same number of tokens, irrespective of cell spans. Secondly, the OTSL representation is unambiguous: Every table structure is represented in one way. In this representation every table cell corresponds to a \"C\"-cell token, which in case of spans is always located in the top-left corner of the table cell definition. Third, OTSL syntax rules are only backward-looking. As a consequence, every predicted token can be validated straight during sequence generation by looking at the previously predicted sequence. As such, OTSL can guarantee that every predicted sequence is syntactically valid."}, {"self_ref": "#/texts/182", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 7, "bbox": {"l": 134.76, "t": 147.9, "r": 480.59, "b": 127.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 153]}], "orig": "These characteristics can be easily learned by sequence generator networks, as we demonstrate further below. We find strong indications that this pattern", "text": "These characteristics can be easily learned by sequence generator networks, as we demonstrate further below. We find strong indications that this pattern"}, {"self_ref": "#/texts/183", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 8, "bbox": {"l": 134.76, "t": 700.51, "r": 139.37, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "8", "text": "8"}, {"self_ref": "#/texts/184", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 8, "bbox": {"l": 167.81, "t": 700.51, "r": 231.72, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "<PERSON><PERSON>, et al.", "text": "<PERSON><PERSON>, et al."}, {"self_ref": "#/texts/185", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 134.76, "t": 673.07, "r": 480.59, "b": 652.31, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 84]}], "orig": "reduces significantly the column drift seen in the HTML based models (see Figure 5).", "text": "reduces significantly the column drift seen in the HTML based models (see Figure 5)."}, {"self_ref": "#/texts/186", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 8, "bbox": {"l": 134.76, "t": 630.44, "r": 319.35, "b": 621.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 35]}], "orig": "4.3 Error-detection and -mitigation", "text": "4.3 Error-detection and -mitigation", "level": 1}, {"self_ref": "#/texts/187", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 134.76, "t": 609.72, "r": 480.6, "b": 493.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 797]}], "orig": "The design of OTSL allows to validate a table structure easily on an unfinished sequence. The detection of an invalid sequence token is a clear indication of a prediction mistake, however a valid sequence by itself does not guarantee prediction correctness. Different heuristics can be used to correct token errors in an invalid sequence and thus increase the chances for accurate predictions. Such heuristics can be applied either after the prediction of each token, or at the end on the entire predicted sequence. For example a simple heuristic which can correct the predicted OTSL sequence on-the-fly is to verify if the token with the highest prediction confidence invalidates the predicted sequence, and replace it by the token with the next highest confidence until OTSL rules are satisfied.", "text": "The design of OTSL allows to validate a table structure easily on an unfinished sequence. The detection of an invalid sequence token is a clear indication of a prediction mistake, however a valid sequence by itself does not guarantee prediction correctness. Different heuristics can be used to correct token errors in an invalid sequence and thus increase the chances for accurate predictions. Such heuristics can be applied either after the prediction of each token, or at the end on the entire predicted sequence. For example a simple heuristic which can correct the predicted OTSL sequence on-the-fly is to verify if the token with the highest prediction confidence invalidates the predicted sequence, and replace it by the token with the next highest confidence until OTSL rules are satisfied."}, {"self_ref": "#/texts/188", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 8, "bbox": {"l": 134.76, "t": 470.84, "r": 229.03, "b": 460.27, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "5 Experiments", "text": "5 Experiments", "level": 1}, {"self_ref": "#/texts/189", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 134.76, "t": 444.75, "r": 480.6, "b": 340.31, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 684]}], "orig": "To evaluate the impact of OTSL on prediction accuracy and inference times, we conducted a series of experiments based on the TableFormer model (Figure 4) with two objectives: Firstly we evaluate the prediction quality and performance of OTSL vs. HTML after performing Hyper Parameter Optimization (HPO) on the canonical PubTabNet data set. Secondly we pick the best hyper-parameters found in the first step and evaluate how OTSL impacts the performance of TableFormer after training on other publicly available data sets (FinTabNet, PubTables-1M [14]). The ground truth (GT) from all data sets has been converted into OTSL format for this purpose, and will be made publicly available.", "text": "To evaluate the impact of OTSL on prediction accuracy and inference times, we conducted a series of experiments based on the TableFormer model (Figure 4) with two objectives: Firstly we evaluate the prediction quality and performance of OTSL vs. HTML after performing Hyper Parameter Optimization (HPO) on the canonical PubTabNet data set. Secondly we pick the best hyper-parameters found in the first step and evaluate how OTSL impacts the performance of TableFormer after training on other publicly available data sets (FinTabNet, PubTables-1M [14]). The ground truth (GT) from all data sets has been converted into OTSL format for this purpose, and will be made publicly available."}, {"self_ref": "#/texts/190", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 8, "bbox": {"l": 134.76, "t": 309.57, "r": 480.59, "b": 287.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 104]}], "orig": "Fig. 4. Architecture sketch of the TableFormer model, which is a representative for the Im2Seq approach.", "text": "Fig. 4. Architecture sketch of the TableFormer model, which is a representative for the Im2Seq approach."}, {"self_ref": "#/texts/191", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 147.3, "t": 251.24, "r": 149.41, "b": 248.91, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "1.", "text": "1."}, {"self_ref": "#/texts/192", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 150.81, "t": 251.24, "r": 155.72, "b": 248.91, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>"}, {"self_ref": "#/texts/193", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 162.76, "t": 256.58, "r": 172.3, "b": 254.25, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "Amount", "text": "Amount"}, {"self_ref": "#/texts/194", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 147.64, "t": 256.6, "r": 155.92, "b": 254.27, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "Names", "text": "Names"}, {"self_ref": "#/texts/195", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 158.49, "t": 251.24, "r": 164.1, "b": 248.91, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "1000", "text": "1000"}, {"self_ref": "#/texts/196", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 158.49, "t": 247.3, "r": 162.7, "b": 244.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "500", "text": "500"}, {"self_ref": "#/texts/197", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 158.49, "t": 243.06, "r": 164.1, "b": 240.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "3500", "text": "3500"}, {"self_ref": "#/texts/198", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 158.49, "t": 238.82, "r": 162.7, "b": 236.49, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "150", "text": "150"}, {"self_ref": "#/texts/199", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 168.82, "t": 251.24, "r": 172.89, "b": 248.91, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "unit", "text": "unit"}, {"self_ref": "#/texts/200", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 168.82, "t": 247.3, "r": 172.89, "b": 244.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "unit", "text": "unit"}, {"self_ref": "#/texts/201", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 168.82, "t": 243.06, "r": 172.89, "b": 240.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "unit", "text": "unit"}, {"self_ref": "#/texts/202", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 168.82, "t": 238.82, "r": 172.89, "b": 236.49, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "unit", "text": "unit"}, {"self_ref": "#/texts/203", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 147.3, "t": 247.3, "r": 149.41, "b": 244.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "2.", "text": "2."}, {"self_ref": "#/texts/204", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 150.81, "t": 247.3, "r": 155.72, "b": 244.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>"}, {"self_ref": "#/texts/205", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 147.3, "t": 243.06, "r": 149.41, "b": 240.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "3.", "text": "3."}, {"self_ref": "#/texts/206", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 150.81, "t": 243.06, "r": 155.72, "b": 240.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>"}, {"self_ref": "#/texts/207", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 147.3, "t": 238.82, "r": 149.41, "b": 236.49, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "4.", "text": "4."}, {"self_ref": "#/texts/208", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 150.81, "t": 238.82, "r": 155.72, "b": 236.49, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>"}, {"self_ref": "#/texts/209", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 152.05, "t": 274.94, "r": 171.25, "b": 270.74, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "Extracted", "text": "Extracted"}, {"self_ref": "#/texts/210", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 148.13, "t": 269.63, "r": 175.17, "b": 265.44, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "Table Images", "text": "Table Images"}, {"self_ref": "#/texts/211", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 193.53, "t": 267.43, "r": 220.32, "b": 263.24, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "Standardized", "text": "Standardized"}, {"self_ref": "#/texts/212", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 199.47, "t": 262.13, "r": 214.38, "b": 257.94, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "Images", "text": "Images"}, {"self_ref": "#/texts/213", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 273.61, "t": 282.04, "r": 284.47, "b": 277.85, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "BBox", "text": "BBox"}, {"self_ref": "#/texts/214", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 270.45, "t": 278.25, "r": 287.63, "b": 274.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "Decoder", "text": "Decoder"}, {"self_ref": "#/texts/215", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 332.48, "t": 283.8, "r": 348.14, "b": 279.61, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 6]}], "orig": "BBoxes", "text": "BBoxes"}, {"self_ref": "#/texts/216", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 376.69, "t": 270.83, "r": 407.25, "b": 266.63, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "BBoxes can be", "text": "BBoxes can be"}, {"self_ref": "#/texts/217", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 373.91, "t": 266.28, "r": 410.04, "b": 262.09, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "traced back to the", "text": "traced back to the"}, {"self_ref": "#/texts/218", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 375.3, "t": 261.74, "r": 408.65, "b": 257.54, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 17]}], "orig": "original image to", "text": "original image to"}, {"self_ref": "#/texts/219", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 377.07, "t": 257.19, "r": 406.88, "b": 253.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 15]}], "orig": "extract content", "text": "extract content"}, {"self_ref": "#/texts/220", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 383.57, "t": 228.7, "r": 433.76, "b": 224.51, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "Structure Tags sequence", "text": "Structure Tags sequence"}, {"self_ref": "#/texts/221", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 383.53, "t": 224.16, "r": 433.81, "b": 219.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 27]}], "orig": "provide full description of", "text": "provide full description of"}, {"self_ref": "#/texts/222", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 390.48, "t": 219.61, "r": 426.86, "b": 215.42, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 19]}], "orig": "the table structure", "text": "the table structure"}, {"self_ref": "#/texts/223", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 293.95, "t": 214.05, "r": 323.17, "b": 209.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "Structure Tags", "text": "Structure Tags"}, {"self_ref": "#/texts/224", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 293.95, "t": 209.51, "r": 324.59, "b": 205.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "in OTSL format", "text": "in OTSL format"}, {"self_ref": "#/texts/225", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 333.08, "t": 250.12, "r": 364.15, "b": 245.93, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 14]}], "orig": "BBoxes in sync", "text": "BBoxes in sync"}, {"self_ref": "#/texts/226", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 333.08, "t": 246.34, "r": 369.71, "b": 242.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 17]}], "orig": "with tag sequence", "text": "with tag sequence"}, {"self_ref": "#/texts/227", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 232.66, "t": 276.7, "r": 249.59, "b": 272.51, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "Encoder", "text": "Encoder"}, {"self_ref": "#/texts/228", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 269.82, "t": 245.97, "r": 288.26, "b": 241.78, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "Structure", "text": "Structure"}, {"self_ref": "#/texts/229", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 270.45, "t": 242.19, "r": 287.63, "b": 237.99, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "Decoder", "text": "Decoder"}, {"self_ref": "#/texts/230", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 332.18, "t": 276.03, "r": 358.11, "b": 271.84, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "[x1, y2, x2, y2]", "text": "[x1, y2, x2, y2]"}, {"self_ref": "#/texts/231", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 332.18, "t": 269.97, "r": 361.58, "b": 265.78, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "[x1', y2', x2', y2']", "text": "[x1', y2', x2', y2']"}, {"self_ref": "#/texts/232", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 332.18, "t": 263.91, "r": 364.76, "b": 259.72, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "[x1'', y2'', x2'', y2'']", "text": "[x1'', y2'', x2'', y2'']"}, {"self_ref": "#/texts/233", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 332.18, "t": 257.85, "r": 335.96, "b": 253.66, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "...", "text": "..."}, {"self_ref": "#/texts/234", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 326.89, "t": 275.55, "r": 329.42, "b": 271.36, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/235", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 327.04, "t": 269.52, "r": 329.57, "b": 265.33, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/236", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 327.04, "t": 263.43, "r": 329.57, "b": 259.24, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "3", "text": "3"}, {"self_ref": "#/texts/237", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 424.14, "t": 264.5, "r": 426.67, "b": 260.31, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "3", "text": "3"}, {"self_ref": "#/texts/238", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 453.0, "t": 274.49, "r": 455.53, "b": 270.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/239", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 423.86, "t": 274.88, "r": 426.38, "b": 270.69, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/240", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 333.43, "t": 234.62, "r": 344.19, "b": 229.66, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "C C", "text": "C C"}, {"self_ref": "#/texts/241", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 333.6, "t": 228.11, "r": 350.64, "b": 223.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/242", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 333.66, "t": 221.55, "r": 350.71, "b": 216.6, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/243", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 333.66, "t": 214.95, "r": 350.71, "b": 210.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/244", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 333.57, "t": 208.58, "r": 350.61, "b": 203.63, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 5]}], "orig": "C C C", "text": "C C C"}, {"self_ref": "#/texts/245", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 353.03, "t": 235.05, "r": 359.83, "b": 230.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/246", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 353.19, "t": 228.36, "r": 359.99, "b": 223.45, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/247", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 353.2, "t": 221.77, "r": 360.0, "b": 216.87, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/248", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 353.15, "t": 215.17, "r": 359.95, "b": 210.26, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/249", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 353.27, "t": 208.53, "r": 360.07, "b": 203.62, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "NL", "text": "NL"}, {"self_ref": "#/texts/250", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 347.38, "t": 234.85, "r": 350.34, "b": 229.95, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "L", "text": "L"}, {"self_ref": "#/texts/251", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 331.14, "t": 227.65, "r": 333.67, "b": 223.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "3", "text": "3"}, {"self_ref": "#/texts/252", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 340.81, "t": 237.35, "r": 343.34, "b": 233.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/253", "parent": {"$ref": "#/pictures/3"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 330.98, "t": 237.12, "r": 333.51, "b": 232.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/254", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 8, "bbox": {"l": 134.76, "t": 171.81, "r": 480.59, "b": 127.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 299]}], "orig": "We rely on standard metrics such as Tree Edit Distance score (TEDs) for table structure prediction, and Mean Average Precision (mAP) with 0.75 Intersection Over Union (IOU) threshold for the bounding-box predictions of table cells. The predicted OTSL structures were converted back to HTML format in", "text": "We rely on standard metrics such as Tree Edit Distance score (TEDs) for table structure prediction, and Mean Average Precision (mAP) with 0.75 Intersection Over Union (IOU) threshold for the bounding-box predictions of table cells. The predicted OTSL structures were converted back to HTML format in"}, {"self_ref": "#/texts/255", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 9, "bbox": {"l": 194.48, "t": 700.51, "r": 447.54, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Optimized Table Tokenization for Table Structure Recognition", "text": "Optimized Table Tokenization for Table Structure Recognition"}, {"self_ref": "#/texts/256", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 9, "bbox": {"l": 475.98, "t": 700.51, "r": 480.59, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "9", "text": "9"}, {"self_ref": "#/texts/257", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 134.76, "t": 673.07, "r": 480.6, "b": 640.36, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 163]}], "orig": "order to compute the TED score. Inference timing results for all experiments were obtained from the same machine on a single core with AMD EPYC 7763 CPU @2.45 GHz.", "text": "order to compute the TED score. Inference timing results for all experiments were obtained from the same machine on a single core with AMD EPYC 7763 CPU @2.45 GHz."}, {"self_ref": "#/texts/258", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 9, "bbox": {"l": 134.76, "t": 622.81, "r": 318.45, "b": 614.01, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "5.1 Hyper Parameter Optimization", "text": "5.1 Hyper Parameter Optimization", "level": 1}, {"self_ref": "#/texts/259", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 134.76, "t": 606.41, "r": 480.59, "b": 537.84, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 423]}], "orig": "We have chosen the PubTabNet data set to perform HPO, since it includes a highly diverse set of tables. Also we report TED scores separately for simple and complex tables (tables with cell spans). Results are presented in Table. 1. It is evident that with OTSL, our model achieves the same TED score and slightly better mAP scores in comparison to HTML. However OTSL yields a 2x speed up in the inference runtime over HTML.", "text": "We have chosen the PubTabNet data set to perform HPO, since it includes a highly diverse set of tables. Also we report TED scores separately for simple and complex tables (tables with cell spans). Results are presented in Table. 1. It is evident that with OTSL, our model achieves the same TED score and slightly better mAP scores in comparison to HTML. However OTSL yields a 2x speed up in the inference runtime over HTML."}, {"self_ref": "#/texts/260", "parent": {"$ref": "#/tables/0"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 9, "bbox": {"l": 134.76, "t": 519.14, "r": 480.6, "b": 464.02, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 398]}], "orig": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart.", "text": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart."}, {"self_ref": "#/texts/261", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 9, "bbox": {"l": 134.76, "t": 283.85, "r": 264.4, "b": 275.04, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "5.2 Quantitative Results", "text": "5.2 Quantitative Results", "level": 1}, {"self_ref": "#/texts/262", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 134.76, "t": 267.45, "r": 480.6, "b": 174.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 555]}], "orig": "We picked the model parameter configuration that produced the best prediction quality (enc=6, dec=6, heads=8) with PubTabNet alone, then independently trained and evaluated it on three publicly available data sets: PubTabNet (395k samples), FinTabNet (113k samples) and PubTables-1M (about 1M samples). Performance results are presented in Table. 2. It is clearly evident that the model trained on OTSL outperforms HTML across the board, keeping high TEDs and mAP scores even on difficult financial tables (FinTabNet) that contain sparse and large tables.", "text": "We picked the model parameter configuration that produced the best prediction quality (enc=6, dec=6, heads=8) with PubTabNet alone, then independently trained and evaluated it on three publicly available data sets: PubTabNet (395k samples), FinTabNet (113k samples) and PubTables-1M (about 1M samples). Performance results are presented in Table. 2. It is clearly evident that the model trained on OTSL outperforms HTML across the board, keeping high TEDs and mAP scores even on difficult financial tables (FinTabNet) that contain sparse and large tables."}, {"self_ref": "#/texts/263", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 9, "bbox": {"l": 134.76, "t": 171.81, "r": 480.6, "b": 127.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 289]}], "orig": "Additionally, the results show that OTSL has an advantage over HTML when applied on a bigger data set like PubTables-1M and achieves significantly improved scores. Finally, OTSL achieves faster inference due to fewer decoding steps which is a result of the reduced sequence representation.", "text": "Additionally, the results show that OTSL has an advantage over HTML when applied on a bigger data set like PubTables-1M and achieves significantly improved scores. Finally, OTSL achieves faster inference due to fewer decoding steps which is a result of the reduced sequence representation."}, {"self_ref": "#/texts/264", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 10, "bbox": {"l": 134.76, "t": 700.51, "r": 143.98, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "10", "text": "10"}, {"self_ref": "#/texts/265", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 10, "bbox": {"l": 167.82, "t": 700.51, "r": 231.72, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "<PERSON><PERSON>, et al.", "text": "<PERSON><PERSON>, et al."}, {"self_ref": "#/texts/266", "parent": {"$ref": "#/tables/1"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 10, "bbox": {"l": 134.76, "t": 678.38, "r": 480.59, "b": 645.17, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 192]}], "orig": "Table 2. TSR and cell detection results compared between OTSL and HTML on the PubTabNet [22], FinTabNet [21] and PubTables-1M [14] data sets using TableFormer [9] (with enc=6, dec=6, heads=8).", "text": "Table 2. TSR and cell detection results compared between OTSL and HTML on the PubTabNet [22], FinTabNet [21] and PubTables-1M [14] data sets using TableFormer [9] (with enc=6, dec=6, heads=8)."}, {"self_ref": "#/texts/267", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 10, "bbox": {"l": 134.76, "t": 503.08, "r": 257.09, "b": 494.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 23]}], "orig": "5.3 Qualitative Results", "text": "5.3 Qualitative Results", "level": 1}, {"self_ref": "#/texts/268", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 134.76, "t": 482.14, "r": 480.59, "b": 425.52, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 309]}], "orig": "To illustrate the qualitative differences between OTSL and HTML, Figure 5 demonstrates less overlap and more accurate bounding boxes with OTSL. In Figure 6, OTSL proves to be more effective in handling tables with longer token sequences, resulting in even more precise structure prediction and bounding boxes.", "text": "To illustrate the qualitative differences between OTSL and HTML, Figure 5 demonstrates less overlap and more accurate bounding boxes with OTSL. In Figure 6, OTSL proves to be more effective in handling tables with longer token sequences, resulting in even more precise structure prediction and bounding boxes."}, {"self_ref": "#/texts/269", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 10, "bbox": {"l": 134.76, "t": 396.62, "r": 480.59, "b": 352.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 270]}], "orig": "Fig. 5. The OTSL model produces more accurate bounding boxes with less overlap (E) than the HTML model (D), when predicting the structure of a sparse table (A), at twice the inference speed because of shorter sequence length (B),(C). \"PMC2807444_006_00.png\" PubTabNet. μ", "text": "Fig. 5. The OTSL model produces more accurate bounding boxes with less overlap (E) than the HTML model (D), when predicting the structure of a sparse table (A), at twice the inference speed because of shorter sequence length (B),(C). \"PMC2807444_006_00.png\" PubTabNet. μ"}, {"self_ref": "#/texts/270", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 180.12, "t": 275.7, "r": 190.62, "b": 273.07, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 7]}], "orig": "<table>", "text": "<table>"}, {"self_ref": "#/texts/271", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.24, "t": 271.81, "r": 304.55, "b": 269.17, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 81]}], "orig": "<tr><td></td><td colspan=\"4\"></td><td colspan=\"6\"></td><td colspan=\"3\"></td></tr>", "text": "<tr><td></td><td colspan=\"4\"></td><td colspan=\"6\"></td><td colspan=\"3\"></td></tr>"}, {"self_ref": "#/texts/272", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.24, "t": 267.91, "r": 388.42, "b": 265.27, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>", "text": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>"}, {"self_ref": "#/texts/273", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.24, "t": 264.01, "r": 388.42, "b": 261.37, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>", "text": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>"}, {"self_ref": "#/texts/274", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.24, "t": 260.11, "r": 388.42, "b": 257.47, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>", "text": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>"}, {"self_ref": "#/texts/275", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.24, "t": 256.21, "r": 388.42, "b": 253.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>", "text": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>"}, {"self_ref": "#/texts/276", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.24, "t": 252.31, "r": 388.42, "b": 249.67, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>", "text": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>"}, {"self_ref": "#/texts/277", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.24, "t": 248.41, "r": 388.42, "b": 245.77, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>", "text": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>"}, {"self_ref": "#/texts/278", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.24, "t": 244.51, "r": 388.42, "b": 241.88, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>", "text": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>"}, {"self_ref": "#/texts/279", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.24, "t": 240.62, "r": 388.42, "b": 237.98, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 135]}], "orig": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>", "text": "<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>"}, {"self_ref": "#/texts/280", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 180.12, "t": 236.72, "r": 191.87, "b": 234.08, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 8]}], "orig": "</table>", "text": "</table>"}, {"self_ref": "#/texts/281", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 407.38, "t": 273.64, "r": 450.49, "b": 271.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "C C L L L C L L L L L C L L NL", "text": "C C L L L C L L L L L C L L NL"}, {"self_ref": "#/texts/282", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 407.38, "t": 269.74, "r": 450.49, "b": 267.1, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "C C C C C C C C C C C C C C NL", "text": "C C C C C C C C C C C C C C NL"}, {"self_ref": "#/texts/283", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 407.38, "t": 265.84, "r": 450.49, "b": 263.2, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "C C C C C C C C C C C C C C NL", "text": "C C C C C C C C C C C C C C NL"}, {"self_ref": "#/texts/284", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 407.38, "t": 261.94, "r": 450.49, "b": 259.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "C C C C C C C C C C C C C C NL", "text": "C C C C C C C C C C C C C C NL"}, {"self_ref": "#/texts/285", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 407.38, "t": 258.04, "r": 450.49, "b": 255.4, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "C C C C C C C C C C C C C C NL", "text": "C C C C C C C C C C C C C C NL"}, {"self_ref": "#/texts/286", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 407.38, "t": 254.14, "r": 450.49, "b": 251.5, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "C C C C C C C C C C C C C C NL", "text": "C C C C C C C C C C C C C C NL"}, {"self_ref": "#/texts/287", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 407.38, "t": 250.24, "r": 450.49, "b": 247.61, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "C C C C C C C C C C C C C C NL", "text": "C C C C C C C C C C C C C C NL"}, {"self_ref": "#/texts/288", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 407.38, "t": 246.35, "r": 450.49, "b": 243.71, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "C C C C C C C C C C C C C C NL", "text": "C C C C C C C C C C C C C C NL"}, {"self_ref": "#/texts/289", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 407.38, "t": 242.45, "r": 450.49, "b": 239.81, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "C C C C C C C C C C C C C C NL", "text": "C C C C C C C C C C C C C C NL"}, {"self_ref": "#/texts/290", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 164.53, "t": 282.47, "r": 181.85, "b": 276.71, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "HTML", "text": "HTML"}, {"self_ref": "#/texts/291", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 183.58, "t": 282.47, "r": 208.9, "b": 276.71, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "# tokens:", "text": "# tokens:"}, {"self_ref": "#/texts/292", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 210.63, "t": 282.47, "r": 221.04, "b": 276.71, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "258", "text": "258"}, {"self_ref": "#/texts/293", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 390.2, "t": 282.32, "r": 406.84, "b": 276.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "OTSL", "text": "OTSL"}, {"self_ref": "#/texts/294", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 408.57, "t": 282.32, "r": 433.89, "b": 276.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "# tokens:", "text": "# tokens:"}, {"self_ref": "#/texts/295", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 435.62, "t": 282.32, "r": 446.02, "b": 276.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "135", "text": "135"}, {"self_ref": "#/texts/296", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 167.19, "t": 272.83, "r": 172.82, "b": 265.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "B", "text": "B"}, {"self_ref": "#/texts/297", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 187.34, "t": 343.28, "r": 192.97, "b": 336.09, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "A", "text": "A"}, {"self_ref": "#/texts/298", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 167.39, "t": 225.9, "r": 173.02, "b": 218.71, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "D", "text": "D"}, {"self_ref": "#/texts/299", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 248.46, "t": 170.13, "r": 253.66, "b": 162.93, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "E", "text": "E"}, {"self_ref": "#/texts/300", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 395.9, "t": 272.71, "r": 401.53, "b": 265.51, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/301", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 171.7, "t": 211.71, "r": 177.46, "b": 194.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "HTML", "text": "HTML"}, {"self_ref": "#/texts/302", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 251.13, "t": 158.37, "r": 256.89, "b": 142.08, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "OTSL", "text": "OTSL"}, {"self_ref": "#/texts/303", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 372.15, "t": 190.47, "r": 427.04, "b": 184.71, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "HTML model shows", "text": "HTML model shows"}, {"self_ref": "#/texts/304", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 372.15, "t": 184.03, "r": 430.07, "b": 178.27, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 21]}], "orig": "bounding box drifting", "text": "bounding box drifting"}, {"self_ref": "#/texts/305", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 176.88, "t": 149.05, "r": 231.08, "b": 143.3, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "OTSL model shows", "text": "OTSL model shows"}, {"self_ref": "#/texts/306", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 176.88, "t": 142.61, "r": 230.99, "b": 136.86, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "clean bounding box", "text": "clean bounding box"}, {"self_ref": "#/texts/307", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 176.88, "t": 136.17, "r": 203.93, "b": 130.41, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "alignment", "text": "alignment"}, {"self_ref": "#/texts/308", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 215.93, "t": 234.44, "r": 218.47, "b": 222.84, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "≤", "text": "≤"}, {"self_ref": "#/texts/309", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 229.06, "t": 234.44, "r": 231.72, "b": 222.84, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "μ", "text": "μ"}, {"self_ref": "#/texts/310", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 261.21, "t": 343.54, "r": 263.57, "b": 340.8, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "S", "text": "S"}, {"self_ref": "#/texts/311", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 312.33, "t": 343.54, "r": 313.64, "b": 340.8, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "I", "text": "I"}, {"self_ref": "#/texts/312", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 377.41, "t": 343.54, "r": 380.06, "b": 340.8, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "R", "text": "R"}, {"self_ref": "#/texts/313", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 200.64, "t": 338.66, "r": 205.82, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "ST", "text": "ST"}, {"self_ref": "#/texts/314", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 222.21, "t": 338.66, "r": 229.76, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "0.03", "text": "0.03"}, {"self_ref": "#/texts/315", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 243.27, "t": 338.66, "r": 250.82, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "0.06", "text": "0.06"}, {"self_ref": "#/texts/316", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 264.3, "t": 338.66, "r": 271.84, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "0.12", "text": "0.12"}, {"self_ref": "#/texts/317", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 285.32, "t": 338.66, "r": 292.87, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "0.25", "text": "0.25"}, {"self_ref": "#/texts/318", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 306.38, "t": 338.66, "r": 311.77, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "0.5", "text": "0.5"}, {"self_ref": "#/texts/319", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 323.42, "t": 338.66, "r": 325.57, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/320", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 334.46, "t": 338.66, "r": 336.62, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/321", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 345.53, "t": 338.66, "r": 347.69, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "4", "text": "4"}, {"self_ref": "#/texts/322", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 356.57, "t": 338.66, "r": 358.73, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "8", "text": "8"}, {"self_ref": "#/texts/323", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 367.64, "t": 338.66, "r": 371.96, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "16", "text": "16"}, {"self_ref": "#/texts/324", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 382.67, "t": 338.66, "r": 387.0, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "32", "text": "32"}, {"self_ref": "#/texts/325", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 397.74, "t": 338.66, "r": 402.06, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "64", "text": "64"}, {"self_ref": "#/texts/326", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 412.79, "t": 344.01, "r": 414.94, "b": 334.2, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "≥", "text": "≥"}, {"self_ref": "#/texts/327", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 414.96, "t": 338.66, "r": 422.54, "b": 335.92, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "128", "text": "128"}, {"self_ref": "#/texts/328", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 200.64, "t": 328.08, "r": 204.56, "b": 325.34, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "63", "text": "63"}, {"self_ref": "#/texts/329", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 367.63, "t": 328.08, "r": 369.58, "b": 325.34, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/330", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 382.66, "t": 328.08, "r": 384.62, "b": 325.34, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/331", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 397.73, "t": 328.08, "r": 399.68, "b": 325.34, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "3", "text": "3"}, {"self_ref": "#/texts/332", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 200.64, "t": 323.2, "r": 206.5, "b": 320.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "199", "text": "199"}, {"self_ref": "#/texts/333", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 264.29, "t": 323.2, "r": 266.25, "b": 320.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "5", "text": "5"}, {"self_ref": "#/texts/334", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 306.37, "t": 323.2, "r": 308.33, "b": 320.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/335", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 345.51, "t": 323.2, "r": 347.47, "b": 320.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/336", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 356.56, "t": 323.2, "r": 358.51, "b": 320.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "4", "text": "4"}, {"self_ref": "#/texts/337", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 367.63, "t": 323.2, "r": 369.58, "b": 320.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/338", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 382.66, "t": 323.2, "r": 384.62, "b": 320.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/339", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 200.64, "t": 318.32, "r": 206.5, "b": 315.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "416", "text": "416"}, {"self_ref": "#/texts/340", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 264.29, "t": 318.32, "r": 266.25, "b": 315.58, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "4", "text": "4"}, {"self_ref": "#/texts/341", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 200.64, "t": 313.47, "r": 206.5, "b": 310.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "230", "text": "230"}, {"self_ref": "#/texts/342", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 243.26, "t": 313.47, "r": 245.22, "b": 310.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/343", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 264.29, "t": 313.47, "r": 266.25, "b": 310.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "9", "text": "9"}, {"self_ref": "#/texts/344", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 323.4, "t": 313.47, "r": 325.36, "b": 310.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/345", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 397.73, "t": 313.47, "r": 399.68, "b": 310.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/346", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 200.64, "t": 308.59, "r": 206.5, "b": 305.85, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "276", "text": "276"}, {"self_ref": "#/texts/347", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 382.66, "t": 308.59, "r": 384.62, "b": 305.85, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "2", "text": "2"}, {"self_ref": "#/texts/348", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 397.73, "t": 308.59, "r": 401.65, "b": 305.85, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "12", "text": "12"}, {"self_ref": "#/texts/349", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 412.79, "t": 308.59, "r": 414.74, "b": 305.85, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/350", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 200.64, "t": 303.71, "r": 207.13, "b": 300.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 3]}], "orig": "320", "text": "320"}, {"self_ref": "#/texts/351", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 367.63, "t": 303.71, "r": 369.78, "b": 300.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "1", "text": "1"}, {"self_ref": "#/texts/352", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 382.66, "t": 303.71, "r": 384.82, "b": 300.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "4", "text": "4"}, {"self_ref": "#/texts/353", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 397.73, "t": 303.71, "r": 402.05, "b": 300.97, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "20", "text": "20"}, {"self_ref": "#/texts/354", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 200.64, "t": 298.83, "r": 208.47, "b": 296.1, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "2013", "text": "2013"}, {"self_ref": "#/texts/355", "parent": {"$ref": "#/pictures/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 264.29, "t": 298.83, "r": 266.25, "b": 296.1, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "3", "text": "3"}, {"self_ref": "#/texts/356", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 227.91, "t": 126.17, "r": 230.1, "b": 116.65, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "μ", "text": "μ"}, {"self_ref": "#/texts/357", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 10, "bbox": {"l": 300.58, "t": 108.38, "r": 302.73, "b": 98.57, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "≥", "text": "≥"}, {"self_ref": "#/texts/358", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 11, "bbox": {"l": 194.48, "t": 700.51, "r": 447.54, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Optimized Table Tokenization for Table Structure Recognition", "text": "Optimized Table Tokenization for Table Structure Recognition"}, {"self_ref": "#/texts/359", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 11, "bbox": {"l": 471.38, "t": 700.51, "r": 480.59, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "11", "text": "11"}, {"self_ref": "#/texts/360", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 11, "bbox": {"l": 134.76, "t": 668.42, "r": 480.59, "b": 613.29, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 390]}], "orig": "Fig. 6. Visualization of predicted structure and detected bounding boxes on a complex table with many rows. The OTSL model (B) captured repeating pattern of horizontally merged cells from the GT (A), unlike the HTML model (C). The HTML model also didn't complete the HTML sequence correctly and displayed a lot more of drift and overlap of bounding boxes. \"PMC5406406_003_01.png\" PubTabNet.", "text": "Fig. 6. Visualization of predicted structure and detected bounding boxes on a complex table with many rows. The OTSL model (B) captured repeating pattern of horizontally merged cells from the GT (A), unlike the HTML model (C). The HTML model also didn't complete the HTML sequence correctly and displayed a lot more of drift and overlap of bounding boxes. \"PMC5406406_003_01.png\" PubTabNet."}, {"self_ref": "#/texts/361", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 171.5, "t": 479.45, "r": 177.6, "b": 471.67, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "B", "text": "B"}, {"self_ref": "#/texts/362", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 171.06, "t": 299.25, "r": 177.15, "b": 291.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "C", "text": "C"}, {"self_ref": "#/texts/363", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 283.05, "t": 164.44, "r": 374.96, "b": 158.61, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 30]}], "orig": "Incorrect end of HTML sequence", "text": "Incorrect end of HTML sequence"}, {"self_ref": "#/texts/364", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 283.05, "t": 174.57, "r": 398.06, "b": 168.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 41]}], "orig": "Horizontally merged cells are not present", "text": "Horizontally merged cells are not present"}, {"self_ref": "#/texts/365", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 293.64, "t": 326.33, "r": 437.51, "b": 320.49, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 52]}], "orig": "Repeating pattern is well represented in predictions", "text": "Repeating pattern is well represented in predictions"}, {"self_ref": "#/texts/366", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 181.89, "t": 503.56, "r": 239.24, "b": 497.73, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 20]}], "orig": "Repeating pattern of", "text": "Repeating pattern of"}, {"self_ref": "#/texts/367", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 181.89, "t": 497.03, "r": 251.53, "b": 491.19, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 25]}], "orig": "horizontally merged cells", "text": "horizontally merged cells"}, {"self_ref": "#/texts/368", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 247.83, "t": 605.04, "r": 253.61, "b": 597.67, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "A", "text": "A"}, {"self_ref": "#/texts/369", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 292.19, "t": 184.12, "r": 381.55, "b": 178.28, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "Bounding box drifting at the end", "text": "Bounding box drifting at the end"}, {"self_ref": "#/texts/370", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 172.37, "t": 410.64, "r": 180.17, "b": 388.6, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "OTSL", "text": "OTSL"}, {"self_ref": "#/texts/371", "parent": {"$ref": "#/pictures/5"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 11, "bbox": {"l": 172.37, "t": 236.22, "r": 180.17, "b": 213.25, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 4]}], "orig": "HTML", "text": "HTML"}, {"self_ref": "#/texts/372", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 12, "bbox": {"l": 134.76, "t": 700.51, "r": 143.98, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "12", "text": "12"}, {"self_ref": "#/texts/373", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 12, "bbox": {"l": 167.82, "t": 700.51, "r": 231.72, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "<PERSON><PERSON>, et al.", "text": "<PERSON><PERSON>, et al."}, {"self_ref": "#/texts/374", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 12, "bbox": {"l": 134.76, "t": 674.45, "r": 219.25, "b": 663.88, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 12]}], "orig": "6 Conclusion", "text": "6 Conclusion", "level": 1}, {"self_ref": "#/texts/375", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 12, "bbox": {"l": 134.76, "t": 645.14, "r": 480.6, "b": 588.52, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 330]}], "orig": "We demonstrated that representing tables in HTML for the task of table structure recognition with Im2Seq models is ill-suited and has serious limitations. Furthermore, we presented in this paper an Optimized Table Structure Language (OTSL) which, when compared to commonly used general purpose languages, has several key benefits.", "text": "We demonstrated that representing tables in HTML for the task of table structure recognition with Im2Seq models is ill-suited and has serious limitations. Furthermore, we presented in this paper an Optimized Table Structure Language (OTSL) which, when compared to commonly used general purpose languages, has several key benefits."}, {"self_ref": "#/texts/376", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 12, "bbox": {"l": 134.76, "t": 584.56, "r": 480.6, "b": 468.16, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 724]}], "orig": "First and foremost, given the same network configuration, inference time for a table-structure prediction is about 2 times faster compared to the conventional HTML approach. This is primarily owed to the shorter sequence length of the OTSL representation. Additional performance benefits can be obtained with HPO (hyper parameter optimization). As we demonstrate in our experiments, models trained on OTSL can be significantly smaller, e.g. by reducing the number of encoder and decoder layers, while preserving comparatively good prediction quality. This can further improve inference performance, yielding 5-6 times faster inference speed in OTSL with prediction quality comparable to models trained on HTML (see Table 1).", "text": "First and foremost, given the same network configuration, inference time for a table-structure prediction is about 2 times faster compared to the conventional HTML approach. This is primarily owed to the shorter sequence length of the OTSL representation. Additional performance benefits can be obtained with HPO (hyper parameter optimization). As we demonstrate in our experiments, models trained on OTSL can be significantly smaller, e.g. by reducing the number of encoder and decoder layers, while preserving comparatively good prediction quality. This can further improve inference performance, yielding 5-6 times faster inference speed in OTSL with prediction quality comparable to models trained on HTML (see Table 1)."}, {"self_ref": "#/texts/377", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 12, "bbox": {"l": 134.76, "t": 464.2, "r": 480.6, "b": 323.9, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 926]}], "orig": "Secondly, OTSL has more inherent structure and a significantly restricted vocabulary size. This allows autoregressive models to perform better in the TED metric, but especially with regards to prediction accuracy of the table-cell bounding boxes (see Table 2). As shown in Figure 5, we observe that the OTSL drastically reduces the drift for table cell bounding boxes at high row count and in sparse tables. This leads to more accurate predictions and a significant reduction in post-processing complexity, which is an undesired necessity in HTML-based Im2Seq models. Significant novelty lies in OTSL syntactical rules, which are few, simple and always backwards looking. Each new token can be validated only by analyzing the sequence of previous tokens, without requiring the entire sequence to detect mistakes. This in return allows to perform structural error detection and correction on-the-fly during sequence generation.", "text": "Secondly, OTSL has more inherent structure and a significantly restricted vocabulary size. This allows autoregressive models to perform better in the TED metric, but especially with regards to prediction accuracy of the table-cell bounding boxes (see Table 2). As shown in Figure 5, we observe that the OTSL drastically reduces the drift for table cell bounding boxes at high row count and in sparse tables. This leads to more accurate predictions and a significant reduction in post-processing complexity, which is an undesired necessity in HTML-based Im2Seq models. Significant novelty lies in OTSL syntactical rules, which are few, simple and always backwards looking. Each new token can be validated only by analyzing the sequence of previous tokens, without requiring the entire sequence to detect mistakes. This in return allows to perform structural error detection and correction on-the-fly during sequence generation."}, {"self_ref": "#/texts/378", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 12, "bbox": {"l": 134.76, "t": 298.18, "r": 197.69, "b": 287.61, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 10]}], "orig": "References", "text": "References", "level": 1}, {"self_ref": "#/texts/379", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 139.37, "t": 271.4, "r": 480.59, "b": 228.13, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 270]}], "orig": "<PERSON><PERSON> <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C.B., Staar, P.W.J.: Delivering document conversion as a cloud service with high throughput and responsiveness. CoRR abs/2206.00785 (2022). https://doi.org/10.48550/arXiv.2206.00785 , https://doi.org/10.48550/arXiv.2206.00785", "text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Ram<PERSON>, C.B., Staar, P.W.J.: Delivering document conversion as a cloud service with high throughput and responsiveness. CoRR abs/2206.00785 (2022). https://doi.org/10.48550/arXiv.2206.00785 , https://doi.org/10.48550/arXiv.2206.00785", "enumerated": true, "marker": "1."}, {"self_ref": "#/texts/380", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 139.37, "t": 226.76, "r": 480.59, "b": 182.59, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 301]}], "orig": "2. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>: Complex table structure recognition in the wild using transformer and identity matrix-based augmentation. In: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (eds.) Frontiers in Handwriting Recognition. pp. 545561. Springer International Publishing, Cham (2022)", "text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>: Complex table structure recognition in the wild using transformer and identity matrix-based augmentation. In: <PERSON><PERSON><PERSON>, U<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (eds.) Frontiers in Handwriting Recognition. pp. 545561. Springer International Publishing, Cham (2022)", "enumerated": true, "marker": "2."}, {"self_ref": "#/texts/381", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 139.37, "t": 182.12, "r": 480.59, "b": 159.87, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 140]}], "orig": "3. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, X.L.: Complicated table structure recognition. arXiv preprint arXiv:1908.04729 (2019)", "text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>.<PERSON>: Complicated table structure recognition. arXiv preprint arXiv:1908.04729 (2019)", "enumerated": true, "marker": "3."}, {"self_ref": "#/texts/382", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 12, "bbox": {"l": 139.37, "t": 159.4, "r": 480.59, "b": 126.19, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 204]}], "orig": "4. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>: Challenges in end-to-end neural scientific table recognition. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 894-901. IEEE (2019)", "text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>: Challenges in end-to-end neural scientific table recognition. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 894-901. IEEE (2019)", "enumerated": true, "marker": "4."}, {"self_ref": "#/texts/383", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 13, "bbox": {"l": 194.48, "t": 700.51, "r": 447.54, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Optimized Table Tokenization for Table Structure Recognition", "text": "Optimized Table Tokenization for Table Structure Recognition"}, {"self_ref": "#/texts/384", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 13, "bbox": {"l": 471.38, "t": 700.51, "r": 480.59, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "13", "text": "13"}, {"self_ref": "#/texts/385", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 139.37, "t": 674.6, "r": 480.6, "b": 641.4, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 203]}], "orig": "5. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>: Tables to latex: structure and content extraction from scientific tables. International Journal on Document Analysis and Recognition (IJDAR) pp. 1-10 (2022)", "text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>: Tables to latex: structure and content extraction from scientific tables. International Journal on Document Analysis and Recognition (IJDAR) pp. 1-10 (2022)", "enumerated": true, "marker": "5."}, {"self_ref": "#/texts/386", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 139.37, "t": 597.87, "r": 480.59, "b": 575.62, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 131]}], "orig": "7. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Tablebank: A benchmark dataset for table detection and recognition (2019)", "text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Tablebank: A benchmark dataset for table detection and recognition (2019)", "enumerated": true, "marker": "7."}, {"self_ref": "#/texts/387", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 139.37, "t": 641.72, "r": 480.59, "b": 597.55, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 264]}], "orig": "6. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N.I.: Table structure recognition based on grid shape graph. In: 2022 Asia-Pacific Signal and Information Processing Association Annual Summit and Conference (APSIPA ASC). pp. ********. IEEE (2022)", "text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N.I.: Table structure recognition based on grid shape graph. In: 2022 Asia-Pacific Signal and Information Processing Association Annual Summit and Conference (APSIPA ASC). pp. ********. IEEE (2022)", "enumerated": true, "marker": "6."}, {"self_ref": "#/texts/388", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 139.37, "t": 575.94, "r": 480.6, "b": 521.71, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 345]}], "orig": "8. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>kla, K., Staar, P.: Robust pdf document conversion using recurrent neural networks. Proceedings of the AAAI Conference on Artificial Intelligence 35 (17), 15137-15145 (May 2021), https://ojs.aaai.org/index.php/ AAAI/article/view/17777", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, K., Staar, P.: Robust pdf document conversion using recurrent neural networks. Proceedings of the AAAI Conference on Artificial Intelligence 35 (17), 15137-15145 (May 2021), https://ojs.aaai.org/index.php/ AAAI/article/view/17777", "enumerated": true, "marker": "8."}, {"self_ref": "#/texts/389", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 139.37, "t": 521.13, "r": 480.59, "b": 487.93, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 234]}], "orig": "9. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, P.: Tableformer: Table structure understanding with transformers. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 4614-4623 (June 2022)", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, P.: Tableformer: Table structure understanding with transformers. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 4614-4623 (June 2022)", "enumerated": true, "marker": "9."}, {"self_ref": "#/texts/390", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 134.76, "t": 488.25, "r": 480.59, "b": 423.06, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 413]}], "orig": "10. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A.<PERSON>, Staar, P.W.J.: Doclaynet: A large human-annotated dataset for document-layout segmentation. In: <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (eds.) KDD '22: The 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, Washington, DC, USA, August 14 - 18, 2022. pp. 3743-3751. ACM (2022). https://doi.org/10.1145/3534678.3539043 , https:// doi.org/10.1145/3534678.3539043", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, A.<PERSON>, Staar, P.W.J.: Doclaynet: A large human-annotated dataset for document-layout segmentation. In: <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (eds.) KDD '22: The 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, Washington, DC, USA, August 14 - 18, 2022. pp. 3743-3751. ACM (2022). https://doi.org/10.1145/3534678.3539043 , https:// doi.org/10.1145/3534678.3539043", "enumerated": true, "marker": "10."}, {"self_ref": "#/texts/391", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 134.76, "t": 422.48, "r": 480.59, "b": 378.31, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 295]}], "orig": "11. <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Visave, M., Sultanpure, K.: Cascadetabnet: An approach for end to end table detection and structure recognition from imagebased documents. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition workshops. pp. 572-573 (2020)", "text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Visa<PERSON>, M., Sultanpure, K.: Cascadetabnet: An approach for end to end table detection and structure recognition from imagebased documents. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition workshops. pp. 572-573 (2020)", "enumerated": true, "marker": "11."}, {"self_ref": "#/texts/392", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 134.76, "t": 334.79, "r": 480.59, "b": 291.52, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 275]}], "orig": "13. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S.: Deeptabstr: Deep learning based table structure recognition. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 1403-1409 (2019). https:// doi.org/10.1109/ICDAR.2019.00226", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S.: Deeptabstr: Deep learning based table structure recognition. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 1403-1409 (2019). https:// doi.org/10.1109/ICDAR.2019.00226", "enumerated": true, "marker": "13."}, {"self_ref": "#/texts/393", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 134.76, "t": 378.63, "r": 480.6, "b": 334.47, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 281]}], "orig": "12. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S.: Deepdesrt: Deep learning for detection and structure recognition of tables in document images. In: 2017 14th IAPR international conference on document analysis and recognition (ICDAR). vol. 1, pp. 1162-1167. IEEE (2017)", "text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S.: Deepdesrt: Deep learning for detection and structure recognition of tables in document images. In: 2017 14th IAPR international conference on document analysis and recognition (ICDAR). vol. 1, pp. 1162-1167. IEEE (2017)", "enumerated": true, "marker": "12."}, {"self_ref": "#/texts/394", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 134.76, "t": 290.94, "r": 480.59, "b": 246.77, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 241]}], "orig": "14. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: PubTables-1M: Towards comprehensive table extraction from unstructured documents. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 4634-4642 (June 2022)", "text": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: PubTables-1M: Towards comprehensive table extraction from unstructured documents. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 4634-4642 (June 2022)", "enumerated": true, "marker": "14."}, {"self_ref": "#/texts/395", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 134.76, "t": 247.09, "r": 480.6, "b": 181.9, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 405]}], "orig": "15. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C.: Corpus conversion service: A machine learning platform to ingest documents at scale. In: Proceedings of the 24th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining. pp. 774-782. KDD '18, Association for Computing Machinery, New York, NY, USA (2018). https://doi.org/10.1145/3219819.3219834 , https://doi.org/10. 1145/3219819.3219834", "text": "<PERSON><PERSON>, P.<PERSON>.<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C.: Corpus conversion service: A machine learning platform to ingest documents at scale. In: Proceedings of the 24th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining. pp. 774-782. KDD '18, Association for Computing Machinery, New York, NY, USA (2018). https://doi.org/10.1145/3219819.3219834 , https://doi.org/10. 1145/3219819.3219834", "enumerated": true, "marker": "15."}, {"self_ref": "#/texts/396", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 134.76, "t": 181.33, "r": 480.6, "b": 159.08, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 96]}], "orig": "16. <PERSON>, <PERSON><PERSON>: Tabular Abstraction, Editing, and Formatting. Ph.D. thesis, CAN (1996), aAINN09397", "text": "<PERSON>, <PERSON><PERSON>: Tabular Abstraction, Editing, and Formatting. Ph.D. thesis, CAN (1996), aAINN09397", "enumerated": true, "marker": "16."}, {"self_ref": "#/texts/397", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 13, "bbox": {"l": 134.76, "t": 159.4, "r": 480.59, "b": 126.19, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 195]}], "orig": "17. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, D.: Res2tim: Reconstruct syntactic structures from table images. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 749-755. IEEE (2019)", "text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, D.: Res2tim: Reconstruct syntactic structures from table images. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 749-755. IEEE (2019)", "enumerated": true, "marker": "17."}, {"self_ref": "#/texts/398", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 14, "bbox": {"l": 134.76, "t": 700.51, "r": 143.98, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 2]}], "orig": "14", "text": "14"}, {"self_ref": "#/texts/399", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 14, "bbox": {"l": 167.82, "t": 700.51, "r": 231.72, "b": 689.22, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "<PERSON><PERSON>, et al.", "text": "<PERSON><PERSON>, et al."}, {"self_ref": "#/texts/400", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 134.76, "t": 674.6, "r": 480.59, "b": 641.4, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 223]}], "orig": "18. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>.: Tgrnet: A table graph reconstruction network for table structure recognition. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 1295-1304 (2021)", "text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>.: Tgrnet: A table graph reconstruction network for table structure recognition. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 1295-1304 (2021)", "enumerated": true, "marker": "18."}, {"self_ref": "#/texts/401", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 134.76, "t": 641.73, "r": 480.6, "b": 598.46, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 269]}], "orig": "19. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Pingan-vcgroup's solution for icdar 2021 competition on scientific literature parsing task b: Table recognition to html (2021). https://doi.org/10.48550/ARXIV.2105.01848 , https://arxiv.org/abs/2105.01848", "text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Pingan-vcgroup's solution for icdar 2021 competition on scientific literature parsing task b: Table recognition to html (2021). https://doi.org/10.48550/ARXIV.2105.01848 , https://arxiv.org/abs/2105.01848", "enumerated": true, "marker": "19."}, {"self_ref": "#/texts/402", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 134.76, "t": 597.89, "r": 480.59, "b": 575.64, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 147]}], "orig": "20. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Split, embed and merge: An accurate table structure recognizer. Pattern Recognition 126 , 108565 (2022)", "text": "<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Split, embed and merge: An accurate table structure recognizer. Pattern Recognition 126 , 108565 (2022)", "enumerated": true, "marker": "20."}, {"self_ref": "#/texts/403", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 134.76, "t": 575.97, "r": 480.59, "b": 521.75, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 329]}], "orig": "21. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>.X.<PERSON>.: Global table extractor (gte): A framework for joint table identification and cell structure recognition using visual context. In: 2021 IEEE Winter Conference on Applications of Computer Vision (WACV). pp. 697-706 (2021). https://doi.org/10.1109/WACV48630.2021. 00074", "text": "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N.X.<PERSON>.: Global table extractor (gte): A framework for joint table identification and cell structure recognition using visual context. In: 2021 IEEE Winter Conference on Applications of Computer Vision (WACV). pp. 697-706 (2021). https://doi.org/10.1109/WACV48630.2021. 00074", "enumerated": true, "marker": "21."}, {"self_ref": "#/texts/404", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 134.76, "t": 521.18, "r": 480.6, "b": 477.01, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 259]}], "orig": "22. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>: Image-based table recognition: Data, model, and evaluation. In: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> (eds.) Computer Vision - ECCV 2020. pp. 564-580. Springer International Publishing, Cham (2020)", "text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, A.: Image-based table recognition: Data, model, and evaluation. In: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> (eds.) Computer Vision - ECCV 2020. pp. 564-580. Springer International Publishing, Cham (2020)", "enumerated": true, "marker": "22."}, {"self_ref": "#/texts/405", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 14, "bbox": {"l": 134.76, "t": 477.34, "r": 480.6, "b": 444.14, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 206]}], "orig": "23. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, A.J.: Publaynet: largest dataset ever for document layout analysis. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 1015-1022. IEEE (2019)", "text": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, A.J.: Publaynet: largest dataset ever for document layout analysis. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 1015-1022. IEEE (2019)", "enumerated": true, "marker": "23."}], "pictures": [{"self_ref": "#/pictures/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}, {"$ref": "#/texts/17"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}, {"$ref": "#/texts/34"}, {"$ref": "#/texts/35"}, {"$ref": "#/texts/36"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}, {"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}, {"$ref": "#/texts/54"}, {"$ref": "#/texts/55"}, {"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/texts/60"}, {"$ref": "#/texts/61"}, {"$ref": "#/texts/62"}, {"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}, {"$ref": "#/texts/66"}, {"$ref": "#/texts/67"}, {"$ref": "#/texts/68"}, {"$ref": "#/texts/69"}, {"$ref": "#/texts/70"}, {"$ref": "#/texts/71"}, {"$ref": "#/texts/72"}, {"$ref": "#/texts/73"}, {"$ref": "#/texts/74"}, {"$ref": "#/texts/75"}, {"$ref": "#/texts/76"}, {"$ref": "#/texts/77"}, {"$ref": "#/texts/78"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 2, "bbox": {"l": 148.45, "t": 583.63, "r": 464.36, "b": 366.15, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/13"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/98"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 5, "bbox": {"l": 137.41, "t": 558.49, "r": 476.56, "b": 451.77, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/98"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/119"}, {"$ref": "#/texts/120"}, {"$ref": "#/texts/121"}, {"$ref": "#/texts/122"}, {"$ref": "#/texts/123"}, {"$ref": "#/texts/124"}, {"$ref": "#/texts/125"}, {"$ref": "#/texts/126"}, {"$ref": "#/texts/127"}, {"$ref": "#/texts/128"}, {"$ref": "#/texts/129"}, {"$ref": "#/texts/130"}, {"$ref": "#/texts/131"}, {"$ref": "#/texts/132"}, {"$ref": "#/texts/133"}, {"$ref": "#/texts/134"}, {"$ref": "#/texts/135"}, {"$ref": "#/texts/136"}, {"$ref": "#/texts/137"}, {"$ref": "#/texts/138"}, {"$ref": "#/texts/139"}, {"$ref": "#/texts/140"}, {"$ref": "#/texts/141"}, {"$ref": "#/texts/142"}, {"$ref": "#/texts/143"}, {"$ref": "#/texts/144"}, {"$ref": "#/texts/145"}, {"$ref": "#/texts/146"}, {"$ref": "#/texts/147"}, {"$ref": "#/texts/148"}, {"$ref": "#/texts/149"}, {"$ref": "#/texts/150"}, {"$ref": "#/texts/151"}, {"$ref": "#/texts/152"}, {"$ref": "#/texts/153"}, {"$ref": "#/texts/154"}, {"$ref": "#/texts/155"}, {"$ref": "#/texts/156"}, {"$ref": "#/texts/157"}, {"$ref": "#/groups/2"}, {"$ref": "#/texts/159"}, {"$ref": "#/texts/160"}, {"$ref": "#/texts/161"}, {"$ref": "#/texts/162"}, {"$ref": "#/texts/163"}, {"$ref": "#/texts/164"}, {"$ref": "#/texts/165"}, {"$ref": "#/texts/166"}, {"$ref": "#/texts/167"}, {"$ref": "#/texts/168"}, {"$ref": "#/texts/169"}, {"$ref": "#/texts/170"}, {"$ref": "#/texts/171"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 7, "bbox": {"l": 164.65, "t": 628.2, "r": 449.55, "b": 511.66, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/119"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/190"}, {"$ref": "#/texts/191"}, {"$ref": "#/texts/192"}, {"$ref": "#/texts/193"}, {"$ref": "#/texts/194"}, {"$ref": "#/texts/195"}, {"$ref": "#/texts/196"}, {"$ref": "#/texts/197"}, {"$ref": "#/texts/198"}, {"$ref": "#/texts/199"}, {"$ref": "#/texts/200"}, {"$ref": "#/texts/201"}, {"$ref": "#/texts/202"}, {"$ref": "#/texts/203"}, {"$ref": "#/texts/204"}, {"$ref": "#/texts/205"}, {"$ref": "#/texts/206"}, {"$ref": "#/texts/207"}, {"$ref": "#/texts/208"}, {"$ref": "#/texts/209"}, {"$ref": "#/texts/210"}, {"$ref": "#/texts/211"}, {"$ref": "#/texts/212"}, {"$ref": "#/texts/213"}, {"$ref": "#/texts/214"}, {"$ref": "#/texts/215"}, {"$ref": "#/texts/216"}, {"$ref": "#/texts/217"}, {"$ref": "#/texts/218"}, {"$ref": "#/texts/219"}, {"$ref": "#/texts/220"}, {"$ref": "#/texts/221"}, {"$ref": "#/texts/222"}, {"$ref": "#/texts/223"}, {"$ref": "#/texts/224"}, {"$ref": "#/texts/225"}, {"$ref": "#/texts/226"}, {"$ref": "#/texts/227"}, {"$ref": "#/texts/228"}, {"$ref": "#/texts/229"}, {"$ref": "#/texts/230"}, {"$ref": "#/texts/231"}, {"$ref": "#/texts/232"}, {"$ref": "#/texts/233"}, {"$ref": "#/texts/234"}, {"$ref": "#/texts/235"}, {"$ref": "#/texts/236"}, {"$ref": "#/texts/237"}, {"$ref": "#/texts/238"}, {"$ref": "#/texts/239"}, {"$ref": "#/texts/240"}, {"$ref": "#/texts/241"}, {"$ref": "#/texts/242"}, {"$ref": "#/texts/243"}, {"$ref": "#/texts/244"}, {"$ref": "#/texts/245"}, {"$ref": "#/texts/246"}, {"$ref": "#/texts/247"}, {"$ref": "#/texts/248"}, {"$ref": "#/texts/249"}, {"$ref": "#/texts/250"}, {"$ref": "#/texts/251"}, {"$ref": "#/texts/252"}, {"$ref": "#/texts/253"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 8, "bbox": {"l": 140.71, "t": 283.94, "r": 472.73, "b": 198.32, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/190"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/269"}, {"$ref": "#/texts/270"}, {"$ref": "#/texts/271"}, {"$ref": "#/texts/272"}, {"$ref": "#/texts/273"}, {"$ref": "#/texts/274"}, {"$ref": "#/texts/275"}, {"$ref": "#/texts/276"}, {"$ref": "#/texts/277"}, {"$ref": "#/texts/278"}, {"$ref": "#/texts/279"}, {"$ref": "#/texts/280"}, {"$ref": "#/texts/281"}, {"$ref": "#/texts/282"}, {"$ref": "#/texts/283"}, {"$ref": "#/texts/284"}, {"$ref": "#/texts/285"}, {"$ref": "#/texts/286"}, {"$ref": "#/texts/287"}, {"$ref": "#/texts/288"}, {"$ref": "#/texts/289"}, {"$ref": "#/texts/290"}, {"$ref": "#/texts/291"}, {"$ref": "#/texts/292"}, {"$ref": "#/texts/293"}, {"$ref": "#/texts/294"}, {"$ref": "#/texts/295"}, {"$ref": "#/texts/296"}, {"$ref": "#/texts/297"}, {"$ref": "#/texts/298"}, {"$ref": "#/texts/299"}, {"$ref": "#/texts/300"}, {"$ref": "#/texts/301"}, {"$ref": "#/texts/302"}, {"$ref": "#/texts/303"}, {"$ref": "#/texts/304"}, {"$ref": "#/texts/305"}, {"$ref": "#/texts/306"}, {"$ref": "#/texts/307"}, {"$ref": "#/texts/308"}, {"$ref": "#/texts/309"}, {"$ref": "#/texts/310"}, {"$ref": "#/texts/311"}, {"$ref": "#/texts/312"}, {"$ref": "#/texts/313"}, {"$ref": "#/texts/314"}, {"$ref": "#/texts/315"}, {"$ref": "#/texts/316"}, {"$ref": "#/texts/317"}, {"$ref": "#/texts/318"}, {"$ref": "#/texts/319"}, {"$ref": "#/texts/320"}, {"$ref": "#/texts/321"}, {"$ref": "#/texts/322"}, {"$ref": "#/texts/323"}, {"$ref": "#/texts/324"}, {"$ref": "#/texts/325"}, {"$ref": "#/texts/326"}, {"$ref": "#/texts/327"}, {"$ref": "#/texts/328"}, {"$ref": "#/texts/329"}, {"$ref": "#/texts/330"}, {"$ref": "#/texts/331"}, {"$ref": "#/texts/332"}, {"$ref": "#/texts/333"}, {"$ref": "#/texts/334"}, {"$ref": "#/texts/335"}, {"$ref": "#/texts/336"}, {"$ref": "#/texts/337"}, {"$ref": "#/texts/338"}, {"$ref": "#/texts/339"}, {"$ref": "#/texts/340"}, {"$ref": "#/texts/341"}, {"$ref": "#/texts/342"}, {"$ref": "#/texts/343"}, {"$ref": "#/texts/344"}, {"$ref": "#/texts/345"}, {"$ref": "#/texts/346"}, {"$ref": "#/texts/347"}, {"$ref": "#/texts/348"}, {"$ref": "#/texts/349"}, {"$ref": "#/texts/350"}, {"$ref": "#/texts/351"}, {"$ref": "#/texts/352"}, {"$ref": "#/texts/353"}, {"$ref": "#/texts/354"}, {"$ref": "#/texts/355"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 10, "bbox": {"l": 162.67, "t": 347.38, "r": 451.7, "b": 128.79, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/269"}], "references": [], "footnotes": [], "annotations": []}, {"self_ref": "#/pictures/5", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/360"}, {"$ref": "#/texts/361"}, {"$ref": "#/texts/362"}, {"$ref": "#/texts/363"}, {"$ref": "#/texts/364"}, {"$ref": "#/texts/365"}, {"$ref": "#/texts/366"}, {"$ref": "#/texts/367"}, {"$ref": "#/texts/368"}, {"$ref": "#/texts/369"}, {"$ref": "#/texts/370"}, {"$ref": "#/texts/371"}], "content_layer": "body", "label": "picture", "prov": [{"page_no": 11, "bbox": {"l": 168.39, "t": 610.03, "r": 447.35, "b": 157.99, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/360"}], "references": [], "footnotes": [], "annotations": []}], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/260"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 9, "bbox": {"l": 139.67, "t": 454.43, "r": 475.0, "b": 322.53, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/260"}], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 160.37, "t": 339.46, "r": 168.05, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "# enc-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 207.97, "t": 339.46, "r": 215.65, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "# dec-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 239.8, "t": 344.94, "r": 278.32, "b": 356.23, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67, "t": 339.46, "r": 348.26, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 396.27, "t": 339.46, "r": 417.13, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "mAP", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 394.93, "t": 350.42, "r": 418.47, "b": 361.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "(0.75)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 430.77, "t": 339.46, "r": 467.14, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "Inference", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 427.15, "t": 350.42, "r": 445.07, "b": 361.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "time (secs)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 286.69, "t": 352.41, "r": 312.33, "b": 363.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "simple", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 320.7, "t": 352.41, "r": 353.72, "b": 363.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "complex", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 369.31, "t": 352.41, "r": 379.03, "b": 363.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "all", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 161.91, "t": 371.24, "r": 166.51, "b": 382.53, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 371.24, "r": 214.12, "b": 382.53, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 365.76, "r": 271.4, "b": 377.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 365.76, "r": 310.0, "b": 377.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.965 0.969", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 365.76, "r": 347.7, "b": 377.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.934 0.927", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 365.76, "r": 384.66, "b": 377.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.955 0.955", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 397.27, "t": 367.97, "r": 416.13, "b": 375.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.88 0.857", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 367.97, "r": 458.38, "b": 375.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "2.73 5.39", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 161.91, "t": 397.54, "r": 166.51, "b": 408.83, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 397.54, "r": 214.12, "b": 408.83, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 392.06, "r": 271.4, "b": 403.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 392.06, "r": 310.0, "b": 403.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.938 0.952", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 392.06, "r": 347.7, "b": 403.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.904 0.909", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 392.06, "r": 384.66, "b": 403.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.927 0.938", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 394.27, "r": 418.78, "b": 402.2, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.853 0.843", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 394.27, "r": 458.38, "b": 402.2, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.97 3.77", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 161.91, "t": 423.84, "r": 166.51, "b": 435.13, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 423.84, "r": 214.12, "b": 435.13, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 418.36, "r": 271.4, "b": 429.65, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 418.36, "r": 310.0, "b": 429.65, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.923 0.945", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 418.36, "r": 347.7, "b": 429.65, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.897 0.901", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 418.36, "r": 384.66, "b": 429.65, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.915 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 420.58, "r": 418.78, "b": 428.5, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.859 0.834", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 420.58, "r": 458.38, "b": 428.5, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.91 3.81", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 161.91, "t": 450.14, "r": 166.51, "b": 461.43, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 450.14, "r": 214.12, "b": 461.43, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 444.66, "r": 271.4, "b": 455.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 444.66, "r": 310.0, "b": 455.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.952 0.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 329.02, "t": 444.66, "r": 345.4, "b": 455.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.92 0.903", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 362.09, "t": 446.88, "r": 386.25, "b": 454.8, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.942 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 446.88, "r": 418.78, "b": 454.8, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.857 0.824", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 446.88, "r": 458.38, "b": 454.8, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.22 2", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 6, "num_cols": 8, "grid": [[{"bbox": {"l": 160.37, "t": 339.46, "r": 168.05, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "# enc-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 207.97, "t": 339.46, "r": 215.65, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "# dec-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 239.8, "t": 344.94, "r": 278.32, "b": 356.23, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67, "t": 339.46, "r": 348.26, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67, "t": 339.46, "r": 348.26, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67, "t": 339.46, "r": 348.26, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 396.27, "t": 339.46, "r": 417.13, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "mAP", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 430.77, "t": 339.46, "r": 467.14, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "Inference", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 160.37, "t": 339.46, "r": 168.05, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "# enc-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 207.97, "t": 339.46, "r": 215.65, "b": 350.75, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "# dec-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 239.8, "t": 344.94, "r": 278.32, "b": 356.23, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 286.69, "t": 352.41, "r": 312.33, "b": 363.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "simple", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 320.7, "t": 352.41, "r": 353.72, "b": 363.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "complex", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 369.31, "t": 352.41, "r": 379.03, "b": 363.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "all", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 394.93, "t": 350.42, "r": 418.47, "b": 361.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "(0.75)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 427.15, "t": 350.42, "r": 445.07, "b": 361.7, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "time (secs)", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.91, "t": 371.24, "r": 166.51, "b": 382.53, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 371.24, "r": 214.12, "b": 382.53, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 365.76, "r": 271.4, "b": 377.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 365.76, "r": 310.0, "b": 377.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.965 0.969", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 365.76, "r": 347.7, "b": 377.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.934 0.927", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 365.76, "r": 384.66, "b": 377.05, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.955 0.955", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 397.27, "t": 367.97, "r": 416.13, "b": 375.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.88 0.857", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 367.97, "r": 458.38, "b": 375.9, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "2.73 5.39", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.91, "t": 397.54, "r": 166.51, "b": 408.83, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 397.54, "r": 214.12, "b": 408.83, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 392.06, "r": 271.4, "b": 403.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 392.06, "r": 310.0, "b": 403.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.938 0.952", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 392.06, "r": 347.7, "b": 403.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.904 0.909", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 392.06, "r": 384.66, "b": 403.35, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.927 0.938", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 394.27, "r": 418.78, "b": 402.2, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.853 0.843", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 394.27, "r": 458.38, "b": 402.2, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.97 3.77", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.91, "t": 423.84, "r": 166.51, "b": 435.13, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 423.84, "r": 214.12, "b": 435.13, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 418.36, "r": 271.4, "b": 429.65, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 418.36, "r": 310.0, "b": 429.65, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.923 0.945", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.72, "t": 418.36, "r": 347.7, "b": 429.65, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.897 0.901", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.68, "t": 418.36, "r": 384.66, "b": 429.65, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.915 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 420.58, "r": 418.78, "b": 428.5, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.859 0.834", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 420.58, "r": 458.38, "b": 428.5, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.91 3.81", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.91, "t": 450.14, "r": 166.51, "b": 461.43, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.51, "t": 450.14, "r": 214.12, "b": 461.43, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71, "t": 444.66, "r": 271.4, "b": 455.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.02, "t": 444.66, "r": 310.0, "b": 455.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.952 0.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 329.02, "t": 444.66, "r": 345.4, "b": 455.95, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.92 0.903", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 362.09, "t": 446.88, "r": 386.25, "b": 454.8, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.942 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.62, "t": 446.88, "r": 418.78, "b": 454.8, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.857 0.824", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.53, "t": 446.88, "r": 458.38, "b": 454.8, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.22 2", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}, {"self_ref": "#/tables/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/266"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 10, "bbox": {"l": 143.64, "t": 635.65, "r": 470.85, "b": 528.74, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"$ref": "#/texts/266"}], "references": [], "footnotes": [], "data": {"table_cells": [{"bbox": {"l": 160.78, "t": 164.28, "r": 180.62, "b": 175.57, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Data set", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 215.53, "t": 164.26, "r": 254.04, "b": 175.54, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 300.4, "t": 158.8, "r": 323.99, "b": 170.09, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 5, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 370.35, "t": 164.28, "r": 414.75, "b": 175.57, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "mAP(0.75)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 426.74, "t": 158.8, "r": 463.11, "b": 170.09, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "Inference time (secs)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 262.41, "t": 171.75, "r": 288.06, "b": 183.04, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "simple", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 296.43, "t": 171.75, "r": 329.45, "b": 183.04, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "complex", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 345.03, "t": 171.75, "r": 354.76, "b": 183.04, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "all", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 154.54, "t": 190.58, "r": 201.24, "b": 201.87, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PubTabNet", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 222.44, "t": 185.1, "r": 247.13, "b": 196.39, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 264.74, "t": 185.1, "r": 285.73, "b": 196.39, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.965 0.969", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 302.44, "t": 185.1, "r": 323.43, "b": 196.39, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.934 0.927", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 339.4, "t": 185.1, "r": 360.39, "b": 196.39, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.955 0.955", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 383.12, "t": 187.32, "r": 401.97, "b": 195.24, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.88 0.857", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 435.49, "t": 187.32, "r": 454.35, "b": 195.24, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "2.73 5.39", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 155.94, "t": 216.88, "r": 199.83, "b": 228.17, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "FinTabNet", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 222.44, "t": 211.4, "r": 247.13, "b": 222.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 264.74, "t": 211.4, "r": 285.73, "b": 222.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.955 0.917", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 302.44, "t": 211.4, "r": 323.43, "b": 222.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.961 0.922", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 337.81, "t": 213.62, "r": 361.98, "b": 221.54, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.959 0.92", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 380.46, "t": 213.62, "r": 404.62, "b": 221.54, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.862 0.722", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 435.49, "t": 213.62, "r": 454.35, "b": 221.54, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.85 3.26", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 148.63, "t": 243.18, "r": 207.15, "b": 254.47, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PubTables-1M", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 222.44, "t": 237.71, "r": 247.13, "b": 248.99, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 264.74, "t": 237.71, "r": 285.73, "b": 248.99, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.987 0.983", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 302.44, "t": 237.71, "r": 323.43, "b": 248.99, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.964 0.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 337.81, "t": 239.92, "r": 361.98, "b": 247.85, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.977 0.966", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 380.46, "t": 239.92, "r": 404.62, "b": 247.85, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.896 0.889", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 435.49, "t": 239.92, "r": 454.35, "b": 247.85, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.79 3.26", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 5, "num_cols": 7, "grid": [[{"bbox": {"l": 160.78, "t": 164.28, "r": 180.62, "b": 175.57, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Data set", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 215.53, "t": 164.26, "r": 254.04, "b": 175.54, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 300.4, "t": 158.8, "r": 323.99, "b": 170.09, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 5, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 300.4, "t": 158.8, "r": 323.99, "b": 170.09, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 5, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 300.4, "t": 158.8, "r": 323.99, "b": 170.09, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 2, "end_col_offset_idx": 5, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 370.35, "t": 164.28, "r": 414.75, "b": 175.57, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "mAP(0.75)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 426.74, "t": 158.8, "r": 463.11, "b": 170.09, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "Inference time (secs)", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 160.78, "t": 164.28, "r": 180.62, "b": 175.57, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "Data set", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 215.53, "t": 164.26, "r": 254.04, "b": 175.54, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 262.41, "t": 171.75, "r": 288.06, "b": 183.04, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "simple", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 296.43, "t": 171.75, "r": 329.45, "b": 183.04, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "complex", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 345.03, "t": 171.75, "r": 354.76, "b": 183.04, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "all", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 370.35, "t": 164.28, "r": 414.75, "b": 175.57, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "mAP(0.75)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 426.74, "t": 158.8, "r": 463.11, "b": 170.09, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "Inference time (secs)", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 154.54, "t": 190.58, "r": 201.24, "b": 201.87, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PubTabNet", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 222.44, "t": 185.1, "r": 247.13, "b": 196.39, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 264.74, "t": 185.1, "r": 285.73, "b": 196.39, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.965 0.969", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 302.44, "t": 185.1, "r": 323.43, "b": 196.39, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.934 0.927", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 339.4, "t": 185.1, "r": 360.39, "b": 196.39, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.955 0.955", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 383.12, "t": 187.32, "r": 401.97, "b": 195.24, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.88 0.857", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 435.49, "t": 187.32, "r": 454.35, "b": 195.24, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "2.73 5.39", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 155.94, "t": 216.88, "r": 199.83, "b": 228.17, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "FinTabNet", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 222.44, "t": 211.4, "r": 247.13, "b": 222.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 264.74, "t": 211.4, "r": 285.73, "b": 222.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.955 0.917", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 302.44, "t": 211.4, "r": 323.43, "b": 222.69, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.961 0.922", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 337.81, "t": 213.62, "r": 361.98, "b": 221.54, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.959 0.92", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 380.46, "t": 213.62, "r": 404.62, "b": 221.54, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.862 0.722", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 435.49, "t": 213.62, "r": 454.35, "b": 221.54, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.85 3.26", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 148.63, "t": 243.18, "r": 207.15, "b": 254.47, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "PubTables-1M", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 222.44, "t": 237.71, "r": 247.13, "b": 248.99, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 264.74, "t": 237.71, "r": 285.73, "b": 248.99, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "0.987 0.983", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 302.44, "t": 237.71, "r": 323.43, "b": 248.99, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.964 0.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 337.81, "t": 239.92, "r": 361.98, "b": 247.85, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.977 0.966", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 380.46, "t": 239.92, "r": 404.62, "b": 247.85, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.896 0.889", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 435.49, "t": 239.92, "r": 454.35, "b": 247.85, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "1.79 3.26", "column_header": false, "row_header": false, "row_section": false}]]}, "annotations": []}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 612.0, "height": 792.0}, "page_no": 1}, "2": {"size": {"width": 612.0, "height": 792.0}, "page_no": 2}, "3": {"size": {"width": 612.0, "height": 792.0}, "page_no": 3}, "4": {"size": {"width": 612.0, "height": 792.0}, "page_no": 4}, "5": {"size": {"width": 612.0, "height": 792.0}, "page_no": 5}, "6": {"size": {"width": 612.0, "height": 792.0}, "page_no": 6}, "7": {"size": {"width": 612.0, "height": 792.0}, "page_no": 7}, "8": {"size": {"width": 612.0, "height": 792.0}, "page_no": 8}, "9": {"size": {"width": 612.0, "height": 792.0}, "page_no": 9}, "10": {"size": {"width": 612.0, "height": 792.0}, "page_no": 10}, "11": {"size": {"width": 612.0, "height": 792.0}, "page_no": 11}, "12": {"size": {"width": 612.0, "height": 792.0}, "page_no": 12}, "13": {"size": {"width": 612.0, "height": 792.0}, "page_no": 13}, "14": {"size": {"width": 612.0, "height": 792.0}, "page_no": 14}}}
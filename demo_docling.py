#!/usr/bin/env python3
"""
Docling功能演示脚本
展示Docling的主要功能和用法
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_banner():
    """打印Docling横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                        🚀 DOCLING 演示                        ║
    ║                   文档处理与转换工具                           ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def test_basic_functionality():
    """测试基本功能"""
    print("📋 测试基本功能...")
    
    try:
        from docling.document_converter import DocumentConverter
        from docling.datamodel.base_models import InputFormat, OutputFormat
        
        print("✅ 成功导入Docling核心模块")
        
        # 显示支持的格式
        print("\n📄 支持的输入格式:")
        for fmt in InputFormat:
            print(f"   • {fmt.value}")
            
        print("\n📤 支持的输出格式:")
        for fmt in OutputFormat:
            print(f"   • {fmt.value}")
            
        # 创建转换器
        converter = DocumentConverter()
        print("\n✅ 成功创建DocumentConverter实例")
        
        return converter
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return None
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None

def test_markdown_conversion(converter):
    """测试Markdown转换"""
    print("\n📝 测试Markdown文档转换...")
    
    # 创建一个示例Markdown文档
    sample_md = """# Docling演示文档

## 简介
Docling是一个强大的文档处理工具，支持多种格式的文档解析和转换。

## 主要特性
- 📄 支持PDF、DOCX、PPTX等多种格式
- 🔍 高级PDF理解能力
- 🤖 AI集成支持
- 🔒 本地执行保护隐私

## 使用示例

```python
from docling.document_converter import DocumentConverter

converter = DocumentConverter()
result = converter.convert("document.pdf")
markdown = result.document.export_to_markdown()
```

## 表格示例

| 功能 | 描述 | 状态 |
|------|------|------|
| PDF解析 | 解析复杂PDF文档 | ✅ |
| 格式转换 | 多种输出格式 | ✅ |
| OCR支持 | 图像文字识别 | ✅ |

这是一个演示文档。
"""
    
    # 保存示例文档
    demo_file = Path("demo_document.md")
    with open(demo_file, "w", encoding="utf-8") as f:
        f.write(sample_md)
    
    try:
        # 转换文档
        result = converter.convert(str(demo_file))
        print("✅ Markdown文档转换成功")
        
        # 创建输出目录
        output_dir = Path("demo_output")
        output_dir.mkdir(exist_ok=True)
        
        # 保存为不同格式
        formats = [
            ("json", lambda f: result.document.save_as_json(f)),
            ("md", lambda f: result.document.save_as_markdown(f)),
            ("html", lambda f: result.document.save_as_html(f)),
        ]
        
        for ext, save_func in formats:
            output_file = output_dir / f"demo_document.{ext}"
            save_func(output_file)
            print(f"💾 {ext.upper()}格式已保存到: {output_file}")
        
        # 显示转换结果预览
        markdown_output = result.document.export_to_markdown()
        print(f"\n📊 转换统计:")
        print(f"   • 字符数: {len(markdown_output):,}")
        print(f"   • 转换状态: {result.status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False
    finally:
        # 清理临时文件
        if demo_file.exists():
            demo_file.unlink()

def show_cli_usage():
    """显示CLI使用方法"""
    print("\n🖥️  CLI使用方法:")
    print("=" * 60)
    
    cli_examples = [
        ("转换单个文档", "poetry run docling document.pdf"),
        ("转换为JSON格式", "poetry run docling document.pdf --to json"),
        ("转换为HTML格式", "poetry run docling document.pdf --to html"),
        ("使用VLM模型", "poetry run docling --pipeline vlm document.pdf"),
        ("启用OCR", "poetry run docling --ocr document.pdf"),
        ("指定输出目录", "poetry run docling document.pdf --output ./results"),
        ("显示帮助", "poetry run docling --help"),
        ("显示版本", "poetry run docling --version"),
    ]
    
    for desc, cmd in cli_examples:
        print(f"📌 {desc}:")
        print(f"   {cmd}")
        print()

def main():
    """主函数"""
    print_banner()
    
    # 测试基本功能
    converter = test_basic_functionality()
    if not converter:
        print("❌ 基本功能测试失败，退出演示")
        return
    
    # 测试Markdown转换
    if test_markdown_conversion(converter):
        print("\n✅ Markdown转换测试成功")
    else:
        print("\n❌ Markdown转换测试失败")
    
    # 显示CLI使用方法
    show_cli_usage()
    
    print("🎉 Docling演示完成!")
    print("\n💡 提示:")
    print("   • 使用 'poetry run docling --help' 查看完整CLI选项")
    print("   • 查看 output/ 目录中的转换结果")
    print("   • 访问 https://docling-project.github.io/docling/ 获取更多文档")

if __name__ == "__main__":
    main()

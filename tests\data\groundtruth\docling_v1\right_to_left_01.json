{"_name": "", "type": "pdf-document", "description": {"title": null, "abstract": null, "authors": null, "affiliations": null, "subjects": null, "keywords": null, "publication_date": null, "languages": null, "license": null, "publishers": null, "url_refs": null, "references": null, "publication": null, "reference_count": null, "citation_count": null, "citation_date": null, "advanced": null, "analytics": null, "logs": [], "collection": null, "acquisition": null}, "file-info": {"filename": "right_to_left_01.pdf", "filename-prov": null, "document-hash": "85c9c0772fa51fd26f16eaae6abd522c96a4d169ceb7b72cbcfe3444ce22db79", "#-pages": 1, "collection-name": null, "description": null, "page-hashes": [{"hash": "6400df9d1750f707e1e0b310224d0b988ed99457bd230029715def0a6030dd06", "model": "default", "page": 1}]}, "main-text": [{"prov": [{"bbox": [223.85000999999997, 704.4510500000001, 521.98181, 719.4619800000002], "page": 1, "span": [0, 59], "__ref_s3_data": null}], "text": "Pythonو R ةغلب ةجمربلا للاخ نم تلاكشملا لحو ةيجاتنلإا نيسحت", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [90.744003, 635.30804, 522.19, 689.992], "page": 1, "span": [0, 345], "__ref_s3_data": null}], "text": "Python و R ةغلب ةجمربلا ربتعت ةلاعف لولح داجيإ يف دعاستو ةيجاتنلإا ززعت نأ نكمي يتلا ةيوقلا تاودلأا نم ءاملعلاو نيللحملا ىلع لهسي امم ،تانايبلا ليلحتل ةيلاثم اهلعجت ةديرف تازيمPython و R نم لك كلتمي .تلاكشملل ناك اذإ .ةلاعفو ةعيرس ةقيرطب ةدقعم تلايلحت ءارجإ مهسي نأ نكمي تاغللا هذه مادختسا نإف ،ةيليلحت ةيلقع كيدل .لمعلا جئاتن نيسحت يف ريبك لكشب", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [208.10402, 579.38806, 208.10402, 592.67206], "page": 1, "span": [0, 1], "__ref_s3_data": null}], "text": "ً", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [509.34990999999997, 564.74799, 509.34990999999997, 578.03198], "page": 1, "span": [0, 1], "__ref_s3_data": null}], "text": "ً", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [99.863998, 566.06799, 522.23792, 620.75201], "page": 1, "span": [0, 348], "__ref_s3_data": null}], "text": "جارختساو تانايبلا نم ةلئاه تايمك ةجلاعم نكمملا نم حبصي ،ةجمربلا تاراهم عم يليلحتلا ريكفتلا عمتجي امدنع ذيفنتلPython و R مادختسا نيجمربملل نكمي .اهنم تاهجوتلاو طامنلأا ةجذمنلا لثم ،ةمدقتم ةيليلحت تايلمع ةقد رثكأ تارارق ذاختا ىلإ ا ضيأ يدؤي نأ نكمي لب ،تقولا رفوي طقف سيل اذه .ةريبكلا تانايبلا ليلحتو ةيئاصحلإا تانايبلا ىلع ةمئاق تاجاتنتسا ىلع ءانب .", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [92.903999, 496.91799999999995, 522.10596, 551.63202], "page": 1, "span": [0, 375], "__ref_s3_data": null}], "text": "ليلحتلا نم ،تاقيبطتلا نم ةعساو ةعومجم معدت ةينغ تاودأو تابتكمPython و R نم لك رفوت ،كلذ ىلع ةولاع ىلع .ةفلتخملا تلاكشملل ةركتبم لولح ريوطتل تابتكملا هذه نم ةدافتسلاا نيمدختسملل نكمي .يللآا ملعتلا ىلإ ينايبلا R رفوت امنيب ،ةءافكب تانايبلا ةرادلإ Python يف pandas ةبتكم مادختسا نكمي ،لاثملا ليبس مسرلل ةيوق تاودأ .نيللحملاو نيثحابلل ةيلاثم اهلعجي امم ،يئاصحلإا ليلحتلاو ينايبلا", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [96.863998, 441.478, 522.07404, 482.362], "page": 1, "span": [0, 267], "__ref_s3_data": null}], "text": "Python و R ةغلب ةجمربلا يدؤت نأ نكمي ،ةياهنلا يف ةركتبم لولح ريفوتو ةيجاتنلإا نيسحت ىلإ ةيليلحت ةيلقع عم اهل نوكت نأ نكمي ةبسانملا ةيجمربلا بيلاسلأا قيبطتو لاعف لكشب تانايبلا ليلحت ىلع ةردقلا نإ .ةدقعملا تلاكشملل .ينهملاو يصخشلا ءادلأا ىلع ىدملا ةديعب ةيباجيإ تاريثأت", "type": "paragraph", "payload": null, "name": "Text", "font": null}], "figures": [], "tables": [], "bitmaps": null, "equations": [], "footnotes": [], "page-dimensions": [{"height": 792.0, "page": 1, "width": 612.0}], "page-footers": [], "page-headers": [], "_s3_data": null, "identifiers": null}
#!/usr/bin/env python3
"""
测试Docling PDF转换功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from docling.document_converter import DocumentConverter
    print("✅ 成功导入Docling模块")
    
    # 创建文档转换器
    converter = DocumentConverter()
    print("✅ 成功创建DocumentConverter实例")
    
    # 测试转换在线PDF文档（Docling技术报告）
    pdf_url = "https://arxiv.org/pdf/2408.09869"
    print(f"📄 开始转换PDF文档: {pdf_url}")
    
    try:
        result = converter.convert(pdf_url)
        print("✅ PDF文档转换成功")
        
        # 输出转换结果的前500个字符
        print("\n📋 转换结果预览:")
        print("=" * 50)
        markdown_output = result.document.export_to_markdown()
        preview = markdown_output[:1000] + "..." if len(markdown_output) > 1000 else markdown_output
        print(preview)
        print("=" * 50)
        
        # 保存为多种格式
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        # 保存为JSON
        json_file = output_dir / "docling_paper.json"
        result.document.save_as_json(json_file)
        print(f"💾 JSON输出已保存到: {json_file}")
        
        # 保存为Markdown
        md_file = output_dir / "docling_paper.md"
        result.document.save_as_markdown(md_file)
        print(f"💾 Markdown输出已保存到: {md_file}")
        
        # 保存为HTML
        html_file = output_dir / "docling_paper.html"
        result.document.save_as_html(html_file)
        print(f"💾 HTML输出已保存到: {html_file}")
        
        print(f"\n📊 文档统计:")
        print(f"   - 总字符数: {len(markdown_output):,}")
        print(f"   - 转换状态: {result.status}")
        
    except Exception as e:
        print(f"❌ PDF转换失败: {e}")
        print("这可能是由于网络连接问题或PDF文档访问限制")
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已正确安装Docling依赖")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()

print("\n🎉 测试完成!")

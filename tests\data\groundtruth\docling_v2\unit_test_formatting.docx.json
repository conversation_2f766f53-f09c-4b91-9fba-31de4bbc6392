{"schema_name": "DoclingDocument", "version": "1.5.0", "name": "unit_test_formatting", "origin": {"mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "binary_hash": 16380079676357958448, "filename": "unit_test_formatting.docx"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/12"}, {"$ref": "#/groups/1"}, {"$ref": "#/texts/25"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/5"}, {"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}], "content_layer": "body", "name": "group", "label": "inline"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}, {"$ref": "#/texts/16"}, {"$ref": "#/groups/3"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/texts/16"}, "children": [{"$ref": "#/texts/17"}, {"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}], "content_layer": "body", "name": "group", "label": "inline"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/groups/1"}, "children": [{"$ref": "#/texts/21"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/texts/21"}, "children": [{"$ref": "#/texts/22"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}], "content_layer": "body", "name": "group", "label": "inline"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "italic", "text": "italic", "formatting": {"bold": false, "italic": true, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "bold", "text": "bold", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "underline", "text": "underline", "formatting": {"bold": false, "italic": false, "underline": true, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "hyperlink", "text": "hyperlink", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}, "hyperlink": "https:/github.com/DS4SD/docling"}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "italic and bold hyperlink", "text": "italic and bold hyperlink", "formatting": {"bold": true, "italic": true, "underline": false, "strikethrough": false, "script": "baseline"}, "hyperlink": "https:/github.com/DS4SD/docling"}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Normal", "text": "Normal", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "italic", "text": "italic", "formatting": {"bold": false, "italic": true, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "bold", "text": "bold", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "underline", "text": "underline", "formatting": {"bold": false, "italic": false, "underline": true, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "and", "text": "and", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "hyperlink", "text": "hyperlink", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}, "hyperlink": "https:/github.com/DS4SD/docling"}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "on the same line", "text": "on the same line", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Italic bullet 1", "text": "Italic bullet 1", "formatting": {"bold": false, "italic": true, "underline": false, "strikethrough": false, "script": "baseline"}, "enumerated": false, "marker": ""}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Bold bullet 2", "text": "Bold bullet 2", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}, "enumerated": false, "marker": ""}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Underline bullet 3", "text": "Underline bullet 3", "formatting": {"bold": false, "italic": false, "underline": true, "strikethrough": false, "script": "baseline"}, "enumerated": false, "marker": ""}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/groups/1"}, "children": [{"$ref": "#/groups/2"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "", "text": "", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Some", "text": "Some", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "italic", "text": "italic", "formatting": {"bold": false, "italic": true, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "bold", "text": "bold", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "underline", "text": "underline", "formatting": {"bold": false, "italic": false, "underline": true, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/groups/3"}, "children": [{"$ref": "#/groups/4"}], "content_layer": "body", "label": "list_item", "prov": [], "orig": "", "text": "", "enumerated": false, "marker": ""}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "Nested", "text": "Nested", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "italic", "text": "italic", "formatting": {"bold": false, "italic": true, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "text", "prov": [], "orig": "bold", "text": "bold", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false, "script": "baseline"}}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}], "pictures": [], "tables": [], "key_value_items": [], "form_items": [], "pages": {}}
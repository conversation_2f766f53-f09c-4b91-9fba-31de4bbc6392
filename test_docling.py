#!/usr/bin/env python3
"""
测试Docling功能的简单脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from docling.document_converter import DocumentConverter
    print("✅ 成功导入Docling模块")
    
    # 创建文档转换器
    converter = DocumentConverter()
    print("✅ 成功创建DocumentConverter实例")
    
    # 测试转换Markdown文档
    test_file = "test_document.md"
    if os.path.exists(test_file):
        print(f"📄 开始转换文档: {test_file}")
        result = converter.convert(test_file)
        print("✅ 文档转换成功")
        
        # 输出转换结果
        print("\n📋 转换结果:")
        print("=" * 50)
        markdown_output = result.document.export_to_markdown()
        print(markdown_output[:500] + "..." if len(markdown_output) > 500 else markdown_output)
        print("=" * 50)
        
        # 保存为JSON格式
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        json_file = output_dir / "test_document.json"
        result.document.save_as_json(json_file)
        print(f"💾 JSON输出已保存到: {json_file}")
        
    else:
        print(f"❌ 测试文件 {test_file} 不存在")
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已正确安装Docling依赖")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()

print("\n🎉 测试完成!")

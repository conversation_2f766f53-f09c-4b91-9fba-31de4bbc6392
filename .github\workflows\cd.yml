name: "Run CD"

on:
  workflow_dispatch:

env:
  UV_FROZEN: "1"
    
jobs:
  code-checks:
    uses: ./.github/workflows/checks.yml
    with:
      push_coverage: false
  pre-release-check:
    runs-on: ubuntu-latest
    outputs:
      TARGET_TAG_V: ${{ steps.version_check.outputs.TRGT_VERSION }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # for fetching tags, required for semantic-release
      - name: Install uv and set the python version
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - name: Install dependencies
        run: uv sync --only-dev
      - name: Check version of potential release
        id: version_check
        run: |
          TRGT_VERSION=$(uv run --no-sync semantic-release print-version)
          echo "TRGT_VERSION=${TRGT_VERSION}" >> "$GITHUB_OUTPUT"
          echo "${TRGT_VERSION}"
      - name: Check notes of potential release
        run: uv run --no-sync semantic-release changelog --unreleased
  release:
    needs: [code-checks, pre-release-check]
    if: needs.pre-release-check.outputs.TARGET_TAG_V != ''
    environment: auto-release
    runs-on: ubuntu-latest
    concurrency: release
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.CI_APP_ID }}
          private-key: ${{ secrets.CI_PRIVATE_KEY }}
      - uses: actions/checkout@v4
        with:
          token: ${{ steps.app-token.outputs.token }}
          fetch-depth: 0  # for fetching tags, required for semantic-release
      - name: Install uv and set the python version
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
      - name: Install dependencies
        run: uv sync --only-dev
      - name: Run release script
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
          TARGET_VERSION: ${{ needs.pre-release-check.outputs.TARGET_TAG_V }}
          CHGLOG_FILE: CHANGELOG.md
        run: ./.github/scripts/release.sh
        shell: bash

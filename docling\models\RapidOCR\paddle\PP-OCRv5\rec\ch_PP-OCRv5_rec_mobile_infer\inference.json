{"base_code": {"magic": "pir", "trainable": true, "version": 1}, "program": {"regions": [{"#": "region_0", "blocks": [{"#": "block_0", "args": [], "ops": [{"#": "p", "A": [0, 1, 1, "linear_8.b_0"], "DA": [], "O": {"%": 1, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [18385], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_8.w_0"], "DA": [], "O": {"%": 2, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 18385], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_150.w_2"], "DA": [], "O": {"%": 3, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_150.w_1"], "DA": [], "O": {"%": 4, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_150.b_0"], "DA": [], "O": {"%": 5, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_150.w_0"], "DA": [], "O": {"%": 6, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_135.w_0"], "DA": [], "O": {"%": 7, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 60, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_149.w_2"], "DA": [], "O": {"%": 8, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_149.w_1"], "DA": [], "O": {"%": 9, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_149.b_0"], "DA": [], "O": {"%": 10, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_149.w_0"], "DA": [], "O": {"%": 11, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_134.w_0"], "DA": [], "O": {"%": 12, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60, 960, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_148.w_2"], "DA": [], "O": {"%": 13, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_148.w_1"], "DA": [], "O": {"%": 14, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_148.b_0"], "DA": [], "O": {"%": 15, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_148.w_0"], "DA": [], "O": {"%": 16, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_133.w_0"], "DA": [], "O": {"%": 17, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480, 120, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_4.b_0"], "DA": [], "O": {"%": 18, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_4.w_0"], "DA": [], "O": {"%": 19, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_7.b_0"], "DA": [], "O": {"%": 20, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_7.w_0"], "DA": [], "O": {"%": 21, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_6.b_0"], "DA": [], "O": {"%": 22, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_6.w_0"], "DA": [], "O": {"%": 23, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_3.b_0"], "DA": [], "O": {"%": 24, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_3.w_0"], "DA": [], "O": {"%": 25, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_5.b_0"], "DA": [], "O": {"%": 26, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_5.w_0"], "DA": [], "O": {"%": 27, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_4.b_0"], "DA": [], "O": {"%": 28, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_4.w_0"], "DA": [], "O": {"%": 29, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_2.b_0"], "DA": [], "O": {"%": 30, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_2.w_0"], "DA": [], "O": {"%": 31, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_3.b_0"], "DA": [], "O": {"%": 32, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_3.w_0"], "DA": [], "O": {"%": 33, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_2.b_0"], "DA": [], "O": {"%": 34, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_2.w_0"], "DA": [], "O": {"%": 35, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_1.b_0"], "DA": [], "O": {"%": 36, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_1.w_0"], "DA": [], "O": {"%": 37, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_1.b_0"], "DA": [], "O": {"%": 38, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_1.w_0"], "DA": [], "O": {"%": 39, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_0.b_0"], "DA": [], "O": {"%": 40, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "linear_0.w_0"], "DA": [], "O": {"%": 41, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 360], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_0.b_0"], "DA": [], "O": {"%": 42, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "layer_norm_0.w_0"], "DA": [], "O": {"%": 43, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_147.w_2"], "DA": [], "O": {"%": 44, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_147.w_1"], "DA": [], "O": {"%": 45, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_147.b_0"], "DA": [], "O": {"%": 46, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_147.w_0"], "DA": [], "O": {"%": 47, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_132.w_0"], "DA": [], "O": {"%": 48, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 60, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_146.w_2"], "DA": [], "O": {"%": 49, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_146.w_1"], "DA": [], "O": {"%": 50, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_146.b_0"], "DA": [], "O": {"%": 51, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_146.w_0"], "DA": [], "O": {"%": 52, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_131.w_0"], "DA": [], "O": {"%": 53, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60, 480, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_163.b_0"], "DA": [], "O": {"%": 54, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_163.w_0"], "DA": [], "O": {"%": 55, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480, 480, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_55.w_1"], "DA": [], "O": {"%": 56, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_55.w_0"], "DA": [], "O": {"%": 57, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_54.w_1"], "DA": [], "O": {"%": 58, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_54.w_0"], "DA": [], "O": {"%": 59, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_162.b_0"], "DA": [], "O": {"%": 60, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_162.w_0"], "DA": [], "O": {"%": 61, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_53.w_1"], "DA": [], "O": {"%": 62, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_53.w_0"], "DA": [], "O": {"%": 63, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_52.w_1"], "DA": [], "O": {"%": 64, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_52.w_0"], "DA": [], "O": {"%": 65, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_161.b_0"], "DA": [], "O": {"%": 66, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_161.w_0"], "DA": [], "O": {"%": 67, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480, 480, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_51.w_1"], "DA": [], "O": {"%": 68, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_51.w_0"], "DA": [], "O": {"%": 69, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_50.w_1"], "DA": [], "O": {"%": 70, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_50.w_0"], "DA": [], "O": {"%": 71, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_160.b_0"], "DA": [], "O": {"%": 72, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_160.w_0"], "DA": [], "O": {"%": 73, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_49.w_1"], "DA": [], "O": {"%": 74, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_49.w_0"], "DA": [], "O": {"%": 75, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_48.w_1"], "DA": [], "O": {"%": 76, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_48.w_0"], "DA": [], "O": {"%": 77, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_159.b_0"], "DA": [], "O": {"%": 78, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_159.w_0"], "DA": [], "O": {"%": 79, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480, 480, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_47.w_1"], "DA": [], "O": {"%": 80, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_47.w_0"], "DA": [], "O": {"%": 81, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_46.w_1"], "DA": [], "O": {"%": 82, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_46.w_0"], "DA": [], "O": {"%": 83, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_108.b_0"], "DA": [], "O": {"%": 84, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_108.w_0"], "DA": [], "O": {"%": 85, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480, 120, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_107.b_0"], "DA": [], "O": {"%": 86, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_107.w_0"], "DA": [], "O": {"%": 87, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120, 480, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_158.b_0"], "DA": [], "O": {"%": 88, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_158.w_0"], "DA": [], "O": {"%": 89, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_45.w_1"], "DA": [], "O": {"%": 90, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_45.w_0"], "DA": [], "O": {"%": 91, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_44.w_1"], "DA": [], "O": {"%": 92, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_44.w_0"], "DA": [], "O": {"%": 93, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_157.b_0"], "DA": [], "O": {"%": 94, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_157.w_0"], "DA": [], "O": {"%": 95, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480, 240, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_43.w_1"], "DA": [], "O": {"%": 96, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_43.w_0"], "DA": [], "O": {"%": 97, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_42.w_1"], "DA": [], "O": {"%": 98, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_42.w_0"], "DA": [], "O": {"%": 99, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_97.b_0"], "DA": [], "O": {"%": 100, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_97.w_0"], "DA": [], "O": {"%": 101, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 60, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_96.b_0"], "DA": [], "O": {"%": 102, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_96.w_0"], "DA": [], "O": {"%": 103, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60, 240, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_156.b_0"], "DA": [], "O": {"%": 104, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_156.w_0"], "DA": [], "O": {"%": 105, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_41.w_1"], "DA": [], "O": {"%": 106, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_41.w_0"], "DA": [], "O": {"%": 107, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_40.w_1"], "DA": [], "O": {"%": 108, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_40.w_0"], "DA": [], "O": {"%": 109, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_155.b_0"], "DA": [], "O": {"%": 110, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_155.w_0"], "DA": [], "O": {"%": 111, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 240, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_39.w_1"], "DA": [], "O": {"%": 112, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_39.w_0"], "DA": [], "O": {"%": 113, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_38.w_1"], "DA": [], "O": {"%": 114, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_38.w_0"], "DA": [], "O": {"%": 115, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_154.b_0"], "DA": [], "O": {"%": 116, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_154.w_0"], "DA": [], "O": {"%": 117, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_37.w_1"], "DA": [], "O": {"%": 118, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_37.w_0"], "DA": [], "O": {"%": 119, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_36.w_1"], "DA": [], "O": {"%": 120, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_36.w_0"], "DA": [], "O": {"%": 121, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_153.b_0"], "DA": [], "O": {"%": 122, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_153.w_0"], "DA": [], "O": {"%": 123, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 240, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_35.w_1"], "DA": [], "O": {"%": 124, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_35.w_0"], "DA": [], "O": {"%": 125, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_34.w_1"], "DA": [], "O": {"%": 126, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_34.w_0"], "DA": [], "O": {"%": 127, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_152.b_0"], "DA": [], "O": {"%": 128, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_152.w_0"], "DA": [], "O": {"%": 129, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_33.w_1"], "DA": [], "O": {"%": 130, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_33.w_0"], "DA": [], "O": {"%": 131, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_32.w_1"], "DA": [], "O": {"%": 132, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_32.w_0"], "DA": [], "O": {"%": 133, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_151.b_0"], "DA": [], "O": {"%": 134, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_151.w_0"], "DA": [], "O": {"%": 135, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 240, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_31.w_1"], "DA": [], "O": {"%": 136, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_31.w_0"], "DA": [], "O": {"%": 137, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_30.w_1"], "DA": [], "O": {"%": 138, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_30.w_0"], "DA": [], "O": {"%": 139, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_150.b_0"], "DA": [], "O": {"%": 140, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_150.w_0"], "DA": [], "O": {"%": 141, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_29.w_1"], "DA": [], "O": {"%": 142, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_29.w_0"], "DA": [], "O": {"%": 143, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_28.w_1"], "DA": [], "O": {"%": 144, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_28.w_0"], "DA": [], "O": {"%": 145, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_149.b_0"], "DA": [], "O": {"%": 146, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_149.w_0"], "DA": [], "O": {"%": 147, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 240, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_27.w_1"], "DA": [], "O": {"%": 148, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_27.w_0"], "DA": [], "O": {"%": 149, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_26.w_1"], "DA": [], "O": {"%": 150, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_26.w_0"], "DA": [], "O": {"%": 151, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_148.b_0"], "DA": [], "O": {"%": 152, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_148.w_0"], "DA": [], "O": {"%": 153, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_25.w_1"], "DA": [], "O": {"%": 154, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_25.w_0"], "DA": [], "O": {"%": 155, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_24.w_1"], "DA": [], "O": {"%": 156, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_24.w_0"], "DA": [], "O": {"%": 157, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_147.b_0"], "DA": [], "O": {"%": 158, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_147.w_0"], "DA": [], "O": {"%": 159, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [240, 128, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_23.w_1"], "DA": [], "O": {"%": 160, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_23.w_0"], "DA": [], "O": {"%": 161, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_22.w_1"], "DA": [], "O": {"%": 162, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_22.w_0"], "DA": [], "O": {"%": 163, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_146.b_0"], "DA": [], "O": {"%": 164, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_146.w_0"], "DA": [], "O": {"%": 165, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_21.w_1"], "DA": [], "O": {"%": 166, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_21.w_0"], "DA": [], "O": {"%": 167, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_20.w_1"], "DA": [], "O": {"%": 168, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_20.w_0"], "DA": [], "O": {"%": 169, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_145.b_0"], "DA": [], "O": {"%": 170, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_145.w_0"], "DA": [], "O": {"%": 171, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_19.w_1"], "DA": [], "O": {"%": 172, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_19.w_0"], "DA": [], "O": {"%": 173, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_18.w_1"], "DA": [], "O": {"%": 174, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_18.w_0"], "DA": [], "O": {"%": 175, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_144.b_0"], "DA": [], "O": {"%": 176, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_144.w_0"], "DA": [], "O": {"%": 177, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_17.w_1"], "DA": [], "O": {"%": 178, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_17.w_0"], "DA": [], "O": {"%": 179, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_16.w_1"], "DA": [], "O": {"%": 180, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_16.w_0"], "DA": [], "O": {"%": 181, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_143.b_0"], "DA": [], "O": {"%": 182, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_143.w_0"], "DA": [], "O": {"%": 183, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_15.w_1"], "DA": [], "O": {"%": 184, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_15.w_0"], "DA": [], "O": {"%": 185, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_14.w_1"], "DA": [], "O": {"%": 186, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_14.w_0"], "DA": [], "O": {"%": 187, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_142.b_0"], "DA": [], "O": {"%": 188, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_142.w_0"], "DA": [], "O": {"%": 189, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_13.w_1"], "DA": [], "O": {"%": 190, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_13.w_0"], "DA": [], "O": {"%": 191, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_12.w_1"], "DA": [], "O": {"%": 192, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_12.w_0"], "DA": [], "O": {"%": 193, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_141.b_0"], "DA": [], "O": {"%": 194, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_141.w_0"], "DA": [], "O": {"%": 195, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_11.w_1"], "DA": [], "O": {"%": 196, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_11.w_0"], "DA": [], "O": {"%": 197, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_10.w_1"], "DA": [], "O": {"%": 198, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_10.w_0"], "DA": [], "O": {"%": 199, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_140.b_0"], "DA": [], "O": {"%": 200, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_140.w_0"], "DA": [], "O": {"%": 201, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_9.w_1"], "DA": [], "O": {"%": 202, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_9.w_0"], "DA": [], "O": {"%": 203, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_8.w_1"], "DA": [], "O": {"%": 204, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_8.w_0"], "DA": [], "O": {"%": 205, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_139.b_0"], "DA": [], "O": {"%": 206, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_139.w_0"], "DA": [], "O": {"%": 207, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_7.w_1"], "DA": [], "O": {"%": 208, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_7.w_0"], "DA": [], "O": {"%": 209, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_6.w_1"], "DA": [], "O": {"%": 210, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_6.w_0"], "DA": [], "O": {"%": 211, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_138.b_0"], "DA": [], "O": {"%": 212, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_138.w_0"], "DA": [], "O": {"%": 213, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_5.w_1"], "DA": [], "O": {"%": 214, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_5.w_0"], "DA": [], "O": {"%": 215, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_4.w_1"], "DA": [], "O": {"%": 216, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_4.w_0"], "DA": [], "O": {"%": 217, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_137.b_0"], "DA": [], "O": {"%": 218, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_137.w_0"], "DA": [], "O": {"%": 219, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 16, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_3.w_1"], "DA": [], "O": {"%": 220, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_3.w_0"], "DA": [], "O": {"%": 221, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_2.w_1"], "DA": [], "O": {"%": 222, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_2.w_0"], "DA": [], "O": {"%": 223, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_136.b_0"], "DA": [], "O": {"%": 224, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_136.w_0"], "DA": [], "O": {"%": 225, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_1.w_1"], "DA": [], "O": {"%": 226, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_1.w_0"], "DA": [], "O": {"%": 227, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_0.w_1"], "DA": [], "O": {"%": 228, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "learnable_affine_block_0.w_0"], "DA": [], "O": {"%": 229, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_2"], "DA": [], "O": {"%": 230, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_1"], "DA": [], "O": {"%": 231, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.b_0"], "DA": [], "O": {"%": 232, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_0"], "DA": [], "O": {"%": 233, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_0.w_0"], "DA": [], "O": {"%": 234, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 3, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "1.data", "A": [{"AT": {"#": "0.a_str", "D": "x"}, "N": "name"}, {"AT": {"#": "1.a_intarray", "D": [-1, 3, 48, -1]}, "N": "shape"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [0, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 235, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, 48, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 235}, {"%": 234}], "O": [{"%": 236, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 236}, {"%": 231}, {"%": 230}, {"%": 233}, {"%": 232}], "O": [{"%": 237, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}, {"%": 238, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 239, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 240, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 241, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 242, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 16}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 237}, {"%": 225}], "O": [{"%": 243, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 244, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 224}, {"%": 244}], "O": [{"%": 245, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 16, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 243}, {"%": 245}], "O": [{"%": 246, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 229}, {"%": 246}], "O": [{"%": 247, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 247}, {"%": 228}], "O": [{"%": 248, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 248}], "O": [{"%": 249, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 227}, {"%": 249}], "O": [{"%": 250, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 250}, {"%": 226}], "O": [{"%": 251, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 251}, {"%": 219}], "O": [{"%": 252, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 253, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 218}, {"%": 253}], "O": [{"%": 254, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 252}, {"%": 254}], "O": [{"%": 255, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 223}, {"%": 255}], "O": [{"%": 256, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 256}, {"%": 222}], "O": [{"%": 257, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 257}], "O": [{"%": 258, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 221}, {"%": 258}], "O": [{"%": 259, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 259}, {"%": 220}], "O": [{"%": 260, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 32}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 260}, {"%": 213}], "O": [{"%": 261, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 262, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 212}, {"%": 262}], "O": [{"%": 263, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 261}, {"%": 263}], "O": [{"%": 264, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 217}, {"%": 264}], "O": [{"%": 265, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 265}, {"%": 216}], "O": [{"%": 266, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 266}], "O": [{"%": 267, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 215}, {"%": 267}], "O": [{"%": 268, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 268}, {"%": 214}], "O": [{"%": 269, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 269}, {"%": 207}], "O": [{"%": 270, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 271, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 206}, {"%": 271}], "O": [{"%": 272, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 270}, {"%": 272}], "O": [{"%": 273, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 211}, {"%": 273}], "O": [{"%": 274, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 274}, {"%": 210}], "O": [{"%": 275, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 275}], "O": [{"%": 276, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 209}, {"%": 276}], "O": [{"%": 277, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 277}, {"%": 208}], "O": [{"%": 278, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 64}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 278}, {"%": 201}], "O": [{"%": 279, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 280, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 200}, {"%": 280}], "O": [{"%": 281, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 279}, {"%": 281}], "O": [{"%": 282, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 205}, {"%": 282}], "O": [{"%": 283, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 283}, {"%": 204}], "O": [{"%": 284, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 284}], "O": [{"%": 285, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 203}, {"%": 285}], "O": [{"%": 286, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 286}, {"%": 202}], "O": [{"%": 287, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 287}, {"%": 195}], "O": [{"%": 288, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 289, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 194}, {"%": 289}], "O": [{"%": 290, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 288}, {"%": 290}], "O": [{"%": 291, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 199}, {"%": 291}], "O": [{"%": 292, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 292}, {"%": 198}], "O": [{"%": 293, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 293}], "O": [{"%": 294, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 197}, {"%": 294}], "O": [{"%": 295, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_1/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 295}, {"%": 196}], "O": [{"%": 296, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 24, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 64}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 296}, {"%": 189}], "O": [{"%": 297, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 298, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 188}, {"%": 298}], "O": [{"%": 299, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 297}, {"%": 299}], "O": [{"%": 300, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 193}, {"%": 300}], "O": [{"%": 301, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 301}, {"%": 192}], "O": [{"%": 302, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 302}], "O": [{"%": 303, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 191}, {"%": 303}], "O": [{"%": 304, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 304}, {"%": 190}], "O": [{"%": 305, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 305}, {"%": 183}], "O": [{"%": 306, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 307, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 182}, {"%": 307}], "O": [{"%": 308, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 306}, {"%": 308}], "O": [{"%": 309, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 187}, {"%": 309}], "O": [{"%": 310, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 310}, {"%": 186}], "O": [{"%": 311, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 311}], "O": [{"%": 312, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 185}, {"%": 312}], "O": [{"%": 313, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 313}, {"%": 184}], "O": [{"%": 314, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 128}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 314}, {"%": 177}], "O": [{"%": 315, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 316, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 176}, {"%": 316}], "O": [{"%": 317, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 315}, {"%": 317}], "O": [{"%": 318, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 181}, {"%": 318}], "O": [{"%": 319, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 319}, {"%": 180}], "O": [{"%": 320, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 320}], "O": [{"%": 321, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 179}, {"%": 321}], "O": [{"%": 322, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 322}, {"%": 178}], "O": [{"%": 323, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 323}, {"%": 171}], "O": [{"%": 324, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 325, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 170}, {"%": 325}], "O": [{"%": 326, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 324}, {"%": 326}], "O": [{"%": 327, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 175}, {"%": 327}], "O": [{"%": 328, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 328}, {"%": 174}], "O": [{"%": 329, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 329}], "O": [{"%": 330, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 173}, {"%": 330}], "O": [{"%": 331, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_2/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 331}, {"%": 172}], "O": [{"%": 332, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 128}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 332}, {"%": 165}], "O": [{"%": 333, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 334, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 164}, {"%": 334}], "O": [{"%": 335, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 333}, {"%": 335}], "O": [{"%": 336, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 169}, {"%": 336}], "O": [{"%": 337, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 337}, {"%": 168}], "O": [{"%": 338, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 338}], "O": [{"%": 339, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 167}, {"%": 339}], "O": [{"%": 340, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 340}, {"%": 166}], "O": [{"%": 341, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 341}, {"%": 159}], "O": [{"%": 342, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 343, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 158}, {"%": 343}], "O": [{"%": 344, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 342}, {"%": 344}], "O": [{"%": 345, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 163}, {"%": 345}], "O": [{"%": 346, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 346}, {"%": 162}], "O": [{"%": 347, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 347}], "O": [{"%": 348, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 161}, {"%": 348}], "O": [{"%": 349, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 349}, {"%": 160}], "O": [{"%": 350, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 240}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 350}, {"%": 153}], "O": [{"%": 351, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 352, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 152}, {"%": 352}], "O": [{"%": 353, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 351}, {"%": 353}], "O": [{"%": 354, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 157}, {"%": 354}], "O": [{"%": 355, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 355}, {"%": 156}], "O": [{"%": 356, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 356}], "O": [{"%": 357, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 155}, {"%": 357}], "O": [{"%": 358, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 358}, {"%": 154}], "O": [{"%": 359, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 359}, {"%": 147}], "O": [{"%": 360, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 361, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 146}, {"%": 361}], "O": [{"%": 362, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 360}, {"%": 362}], "O": [{"%": 363, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 151}, {"%": 363}], "O": [{"%": 364, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 364}, {"%": 150}], "O": [{"%": 365, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 365}], "O": [{"%": 366, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 149}, {"%": 366}], "O": [{"%": 367, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 367}, {"%": 148}], "O": [{"%": 368, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 240}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 368}, {"%": 141}], "O": [{"%": 369, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 370, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 140}, {"%": 370}], "O": [{"%": 371, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 369}, {"%": 371}], "O": [{"%": 372, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 145}, {"%": 372}], "O": [{"%": 373, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 373}, {"%": 144}], "O": [{"%": 374, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 374}], "O": [{"%": 375, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 143}, {"%": 375}], "O": [{"%": 376, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 376}, {"%": 142}], "O": [{"%": 377, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 377}, {"%": 135}], "O": [{"%": 378, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 379, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 134}, {"%": 379}], "O": [{"%": 380, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 378}, {"%": 380}], "O": [{"%": 381, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 139}, {"%": 381}], "O": [{"%": 382, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 382}, {"%": 138}], "O": [{"%": 383, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 383}], "O": [{"%": 384, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 137}, {"%": 384}], "O": [{"%": 385, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_2/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 385}, {"%": 136}], "O": [{"%": 386, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 240}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 386}, {"%": 129}], "O": [{"%": 387, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 388, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 128}, {"%": 388}], "O": [{"%": 389, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 387}, {"%": 389}], "O": [{"%": 390, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 133}, {"%": 390}], "O": [{"%": 391, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 391}, {"%": 132}], "O": [{"%": 392, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 392}], "O": [{"%": 393, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 131}, {"%": 393}], "O": [{"%": 394, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 394}, {"%": 130}], "O": [{"%": 395, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 395}, {"%": 123}], "O": [{"%": 396, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 397, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 122}, {"%": 397}], "O": [{"%": 398, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 396}, {"%": 398}], "O": [{"%": 399, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 127}, {"%": 399}], "O": [{"%": 400, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 400}, {"%": 126}], "O": [{"%": 401, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 401}], "O": [{"%": 402, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 125}, {"%": 402}], "O": [{"%": 403, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_3/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 403}, {"%": 124}], "O": [{"%": 404, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 240}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 404}, {"%": 117}], "O": [{"%": 405, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 406, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 116}, {"%": 406}], "O": [{"%": 407, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 405}, {"%": 407}], "O": [{"%": 408, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 121}, {"%": 408}], "O": [{"%": 409, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 409}, {"%": 120}], "O": [{"%": 410, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 410}], "O": [{"%": 411, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 119}, {"%": 411}], "O": [{"%": 412, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 412}, {"%": 118}], "O": [{"%": 413, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 413}, {"%": 111}], "O": [{"%": 414, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 415, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 110}, {"%": 415}], "O": [{"%": 416, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 414}, {"%": 416}], "O": [{"%": 417, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 115}, {"%": 417}], "O": [{"%": 418, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 418}, {"%": 114}], "O": [{"%": 419, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 419}], "O": [{"%": 420, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 113}, {"%": 420}], "O": [{"%": 421, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_3/LCNetV3Block_4/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 421}, {"%": 112}], "O": [{"%": 422, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 12, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 240}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 422}, {"%": 105}], "O": [{"%": 423, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 424, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 104}, {"%": 424}], "O": [{"%": 425, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 423}, {"%": 425}], "O": [{"%": 426, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 109}, {"%": 426}], "O": [{"%": 427, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 427}, {"%": 108}], "O": [{"%": 428, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 428}], "O": [{"%": 429, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 107}, {"%": 429}], "O": [{"%": 430, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 430}, {"%": 106}], "O": [{"%": 431, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 432, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 431}, {"%": 432}], "O": [{"%": 433, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 433}, {"%": 103}], "O": [{"%": 434, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 60, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 435, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 102}, {"%": 435}], "O": [{"%": 436, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 60, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 434}, {"%": 436}], "O": [{"%": 437, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 60, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/ReLU/"}, "N": "struct_name"}], "I": [{"%": 437}], "O": [{"%": 438, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 60, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 438}, {"%": 101}], "O": [{"%": 439, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 440, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 100}, {"%": 440}], "O": [{"%": 441, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 439}, {"%": 441}], "O": [{"%": 442, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.16666670143604279}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/Hardsigmoid/"}, "N": "struct_name"}], "I": [{"%": 442}], "O": [{"%": 443, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/SELayer/"}, "N": "struct_name"}], "I": [{"%": 431}, {"%": 443}], "O": [{"%": 444, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 240, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 444}, {"%": 95}], "O": [{"%": 445, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 446, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 94}, {"%": 446}], "O": [{"%": 447, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 445}, {"%": 447}], "O": [{"%": 448, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 99}, {"%": 448}], "O": [{"%": 449, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 449}, {"%": 98}], "O": [{"%": 450, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 450}], "O": [{"%": 451, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 97}, {"%": 451}], "O": [{"%": 452, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 452}, {"%": 96}], "O": [{"%": 453, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 480}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 453}, {"%": 89}], "O": [{"%": 454, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 455, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 88}, {"%": 455}], "O": [{"%": 456, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 454}, {"%": 456}], "O": [{"%": 457, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 93}, {"%": 457}], "O": [{"%": 458, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 458}, {"%": 92}], "O": [{"%": 459, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 459}], "O": [{"%": 460, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 91}, {"%": 460}], "O": [{"%": 461, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 461}, {"%": 90}], "O": [{"%": 462, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 463, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/AdaptiveAvgPool2D/"}, "N": "struct_name"}], "I": [{"%": 462}, {"%": 463}], "O": [{"%": 464, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 464}, {"%": 87}], "O": [{"%": 465, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 466, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 86}, {"%": 466}], "O": [{"%": 467, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 120, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 465}, {"%": 467}], "O": [{"%": 468, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/ReLU/"}, "N": "struct_name"}], "I": [{"%": 468}], "O": [{"%": 469, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 469}, {"%": 85}], "O": [{"%": 470, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 471, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 84}, {"%": 471}], "O": [{"%": 472, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 470}, {"%": 472}], "O": [{"%": 473, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_f32", "D": 0.16666670143604279}, "N": "slope"}, {"AT": {"#": "0.a_f32", "D": 0.5}, "N": "offset"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/Hardsigmoid/"}, "N": "struct_name"}], "I": [{"%": 473}], "O": [{"%": 474, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/SELayer/"}, "N": "struct_name"}], "I": [{"%": 462}, {"%": 474}], "O": [{"%": 475, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 475}, {"%": 79}], "O": [{"%": 476, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 477, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 78}, {"%": 477}], "O": [{"%": 478, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 476}, {"%": 478}], "O": [{"%": 479, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 83}, {"%": 479}], "O": [{"%": 480, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 480}, {"%": 82}], "O": [{"%": 481, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 481}], "O": [{"%": 482, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 81}, {"%": 482}], "O": [{"%": 483, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_1/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 483}, {"%": 80}], "O": [{"%": 484, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 6, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 480}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 484}, {"%": 73}], "O": [{"%": 485, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 486, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 72}, {"%": 486}], "O": [{"%": 487, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 485}, {"%": 487}], "O": [{"%": 488, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 77}, {"%": 488}], "O": [{"%": 489, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 489}, {"%": 76}], "O": [{"%": 490, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 490}], "O": [{"%": 491, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 75}, {"%": 491}], "O": [{"%": 492, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 492}, {"%": 74}], "O": [{"%": 493, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 493}, {"%": 67}], "O": [{"%": 494, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 495, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 66}, {"%": 495}], "O": [{"%": 496, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 494}, {"%": 496}], "O": [{"%": 497, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 71}, {"%": 497}], "O": [{"%": 498, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 498}, {"%": 70}], "O": [{"%": 499, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 499}], "O": [{"%": 500, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 69}, {"%": 500}], "O": [{"%": 501, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_2/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 501}, {"%": 68}], "O": [{"%": 502, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 480}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 502}, {"%": 61}], "O": [{"%": 503, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 504, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 60}, {"%": 504}], "O": [{"%": 505, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 503}, {"%": 505}], "O": [{"%": 506, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 65}, {"%": 506}], "O": [{"%": 507, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 507}, {"%": 64}], "O": [{"%": 508, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 508}], "O": [{"%": 509, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 63}, {"%": 509}], "O": [{"%": 510, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 510}, {"%": 62}], "O": [{"%": 511, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 511}, {"%": 55}], "O": [{"%": 512, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 513, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 54}, {"%": 513}], "O": [{"%": 514, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 480, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 512}, {"%": 514}], "O": [{"%": 515, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 59}, {"%": 515}], "O": [{"%": 516, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 516}, {"%": 58}], "O": [{"%": 517, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.hardswish", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Act/Hardswish/"}, "N": "struct_name"}], "I": [{"%": 517}], "O": [{"%": 518, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.multiply", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 57}, {"%": 518}], "O": [{"%": 519, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPLCNetV3/Sequential_4/LCNetV3Block_3/LearnableRepLayer_1/Act/LearnableAffineBlock/"}, "N": "struct_name"}], "I": [{"%": 519}, {"%": 56}], "O": [{"%": 520, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 3, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/"}, "N": "struct_name"}], "I": [], "O": [{"%": 521, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "avg"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPLCNetV3/"}, "N": "struct_name"}], "I": [{"%": 520}, {"%": 521}], "O": [{"%": 522, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.assign", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 522}], "O": [{"%": 523, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 523}, {"%": 53}], "O": [{"%": 524, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 60, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 524}, {"%": 50}, {"%": 49}, {"%": 52}, {"%": 51}], "O": [{"%": 525, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 60, 1, -1], "NCHW", [], 0]}}, {"%": 526, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, {"%": 527, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, {"%": 528, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, {"%": 529, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, {"%": 530, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer/Swish/"}, "N": "struct_name"}], "I": [{"%": 525}], "O": [{"%": 531, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 60, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 531}, {"%": 48}], "O": [{"%": 532, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 532}, {"%": 45}, {"%": 44}, {"%": 47}, {"%": 46}], "O": [{"%": 533, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}, {"%": 534, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 535, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 536, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 537, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 538, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_1/Swish/"}, "N": "struct_name"}], "I": [{"%": 533}], "O": [{"%": 539, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.shape64", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 539}], "O": [{"%": 540, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 541, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 4}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 542, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 540}, {"%": 541}, {"%": 542}], "O": [{"%": 543, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.flatten", "A": [{"AT": {"#": "0.a_i32", "D": 2}, "N": "start_axis"}, {"AT": {"#": "0.a_i32", "D": 3}, "N": "stop_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 539}], "O": [{"%": 544, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 544}], "O": [{"%": 545, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/LayerNorm/"}, "N": "struct_name"}], "I": [{"%": 545}, {"%": 43}, {"%": 42}], "O": [{"%": 546, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 547, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 548, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 546}, {"%": 41}], "O": [{"%": 549, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 549}, {"%": 40}], "O": [{"%": 550, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 8}, {"#": "0.a_i64", "D": 15}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 551, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [5], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 550}, {"%": 551}], "O": [{"%": 552, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 3, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 4}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 552}], "O": [{"%": 553, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [3, -1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 554, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 555, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 553}, {"%": 554}, {"%": 555}], "O": [{"%": 556, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.25819888710975647}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 557, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.scale", "A": [{"AT": {"#": "0.a_f32", "D": 0.0}, "N": "bias"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "bias_after_scale"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 556}, {"%": 557}], "O": [{"%": 558, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 559, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 560, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 553}, {"%": 559}, {"%": 560}], "O": [{"%": 561, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 562, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 563, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 553}, {"%": 562}, {"%": 563}], "O": [{"%": 564, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 2}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 561}], "O": [{"%": 565, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 15, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 558}, {"%": 565}], "O": [{"%": 566, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": -1}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 566}], "O": [{"%": 567, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 568, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout/"}, "N": "struct_name"}], "I": [{"%": 567}, {"%": 0}, {"%": 568}], "O": [{"%": 569, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}, {"%": 570, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 569}, {"%": 564}], "O": [{"%": 571, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 571}], "O": [{"%": 572, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 120}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 573, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [3], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/"}, "N": "struct_name"}], "I": [{"%": 572}, {"%": 573}], "O": [{"%": 574, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 574}, {"%": 39}], "O": [{"%": 575, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 575}, {"%": 38}], "O": [{"%": 576, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 577, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 576}, {"%": 0}, {"%": 577}], "O": [{"%": 578, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 579, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/"}, "N": "struct_name"}], "I": [{"%": 545}, {"%": 578}], "O": [{"%": 580, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/LayerNorm_1/"}, "N": "struct_name"}], "I": [{"%": 580}, {"%": 37}, {"%": 36}], "O": [{"%": 581, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 582, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 583, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 581}, {"%": 35}], "O": [{"%": 584, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 584}, {"%": 34}], "O": [{"%": 585, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Swish/"}, "N": "struct_name"}], "I": [{"%": 585}], "O": [{"%": 586, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 587, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout/"}, "N": "struct_name"}], "I": [{"%": 586}, {"%": 0}, {"%": 587}], "O": [{"%": 588, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}, {"%": 589, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 588}, {"%": 33}], "O": [{"%": 590, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 590}, {"%": 32}], "O": [{"%": 591, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 592, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 591}, {"%": 0}, {"%": 592}], "O": [{"%": 593, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 594, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block/"}, "N": "struct_name"}], "I": [{"%": 580}, {"%": 593}], "O": [{"%": 595, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/LayerNorm/"}, "N": "struct_name"}], "I": [{"%": 595}, {"%": 31}, {"%": 30}], "O": [{"%": 596, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 597, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 598, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 596}, {"%": 29}], "O": [{"%": 599, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear/"}, "N": "struct_name"}], "I": [{"%": 599}, {"%": 28}], "O": [{"%": 600, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 360], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 3}, {"#": "0.a_i64", "D": 8}, {"#": "0.a_i64", "D": 15}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 601, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [5], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 600}, {"%": 601}], "O": [{"%": 602, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 3, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 4}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 602}], "O": [{"%": 603, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [3, -1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 604, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 605, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 604}, {"%": 605}], "O": [{"%": 606, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.25819888710975647}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 607, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.scale", "A": [{"AT": {"#": "0.a_f32", "D": 0.0}, "N": "bias"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "bias_after_scale"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 606}, {"%": 607}], "O": [{"%": 608, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 609, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 610, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 609}, {"%": 610}], "O": [{"%": 611, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 612, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 613, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 612}, {"%": 613}], "O": [{"%": 614, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 2}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 611}], "O": [{"%": 615, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, 15, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 608}, {"%": 615}], "O": [{"%": 616, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": -1}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 616}], "O": [{"%": 617, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 618, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout/"}, "N": "struct_name"}], "I": [{"%": 617}, {"%": 0}, {"%": 618}], "O": [{"%": 619, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, -1], "NCHW", [], 0]}}, {"%": 620, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, 8, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 619}, {"%": 614}], "O": [{"%": 621, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 8, -1, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 3}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 621}], "O": [{"%": 622, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 8, 15], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 120}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [], "O": [{"%": 623, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [3], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/"}, "N": "struct_name"}], "I": [{"%": 622}, {"%": 623}], "O": [{"%": 624, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 624}, {"%": 27}], "O": [{"%": 625, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 625}, {"%": 26}], "O": [{"%": 626, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 627, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Attention/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 626}, {"%": 0}, {"%": 627}], "O": [{"%": 628, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 629, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/"}, "N": "struct_name"}], "I": [{"%": 595}, {"%": 628}], "O": [{"%": 630, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/LayerNorm_1/"}, "N": "struct_name"}], "I": [{"%": 630}, {"%": 25}, {"%": 24}], "O": [{"%": 631, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 632, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 633, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 631}, {"%": 23}], "O": [{"%": 634, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear/"}, "N": "struct_name"}], "I": [{"%": 634}, {"%": 22}], "O": [{"%": 635, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Swish/"}, "N": "struct_name"}], "I": [{"%": 635}], "O": [{"%": 636, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout/"}, "N": "struct_name"}], "I": [], "O": [{"%": 637, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout/"}, "N": "struct_name"}], "I": [{"%": 636}, {"%": 0}, {"%": 637}], "O": [{"%": 638, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 240], "NCHW", [], 0]}}, {"%": 639, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 240], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 638}, {"%": 21}], "O": [{"%": 640, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Linear_1/"}, "N": "struct_name"}], "I": [{"%": 640}, {"%": 20}], "O": [{"%": 641, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.10000000149011612}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 642, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.dropout", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_str", "D": "upscale_in_train"}, "N": "mode"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "seed"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "fix_seed"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/Mlp/Dropout_1/"}, "N": "struct_name"}], "I": [{"%": 641}, {"%": 0}, {"%": 642}], "O": [{"%": 643, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 644, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/Block_1/"}, "N": "struct_name"}], "I": [{"%": 630}, {"%": 643}], "O": [{"%": 645, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.layer_norm", "A": [{"AT": {"#": "0.a_f32", "D": 9.999999974752427e-07}, "N": "epsilon"}, {"AT": {"#": "0.a_i32", "D": 2}, "N": "begin_norm_axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/LayerNorm/"}, "N": "struct_name"}], "I": [{"%": 645}, {"%": 19}, {"%": 18}], "O": [{"%": 646, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}, {"%": 647, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}, {"%": 648, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": []}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 649, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": []}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 650, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": []}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 120.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 651, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 649}, {"%": 650}, {"%": 543}, {"%": 651}], "O": [{"%": 652, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.stack", "A": [{"AT": {"#": "0.a_i32", "D": 0}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 652}], "O": [{"%": 653, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 646}, {"%": 653}], "O": [{"%": 654, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 2}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 654}], "O": [{"%": 655, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 655}, {"%": 17}], "O": [{"%": 656, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 656}, {"%": 14}, {"%": 13}, {"%": 16}, {"%": 15}], "O": [{"%": 657, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, -1], "NCHW", [], 0]}}, {"%": 658, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, {"%": 659, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, {"%": 660, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, {"%": 661, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [480], "NCHW", [], 0]}}, {"%": 662, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_2/Swish/"}, "N": "struct_name"}], "I": [{"%": 657}], "O": [{"%": 663, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [], "O": [{"%": 664, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 523}, {"%": 663}], "O": [{"%": 665, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 480, 1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/"}, "N": "struct_name"}], "I": [{"%": 665}, {"%": 664}], "O": [{"%": 666, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 960, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 666}, {"%": 12}], "O": [{"%": 667, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 60, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 667}, {"%": 9}, {"%": 8}, {"%": 11}, {"%": 10}], "O": [{"%": 668, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 60, 1, -1], "NCHW", [], 0]}}, {"%": 669, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, {"%": 670, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, {"%": 671, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, {"%": 672, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [60], "NCHW", [], 0]}}, {"%": 673, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_3/Swish/"}, "N": "struct_name"}], "I": [{"%": 668}], "O": [{"%": 674, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 60, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 674}, {"%": 7}], "O": [{"%": 675, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 675}, {"%": 4}, {"%": 3}, {"%": 6}, {"%": 5}], "O": [{"%": 676, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}, {"%": 677, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 678, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 679, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 680, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [120], "NCHW", [], 0]}}, {"%": 681, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.swish", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/EncoderWithSVTR/ConvBNLayer_4/Swish/"}, "N": "struct_name"}], "I": [{"%": 676}], "O": [{"%": 682, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, 1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/Im2Seq/"}, "N": "struct_name"}], "I": [], "O": [{"%": 683, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.squeeze", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/Im2Seq/"}, "N": "struct_name"}], "I": [{"%": 682}, {"%": 683}], "O": [{"%": 684, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 120, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 1}]}, "N": "perm"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/SequenceEncoder/Im2Seq/"}, "N": "struct_name"}], "I": [{"%": 684}], "O": [{"%": 685, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 120], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.<PERSON><PERSON><PERSON>", "A": [{"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_x"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "transpose_y"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/CTCHead/Linear/"}, "N": "struct_name"}], "I": [{"%": 685}, {"%": 2}], "O": [{"%": 686, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 18385], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/MultiHead/CTCHead/Linear/"}, "N": "struct_name"}], "I": [{"%": 686}, {"%": 1}], "O": [{"%": 687, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 18385], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.softmax", "A": [{"AT": {"#": "0.a_i32", "D": 2}, "N": "axis"}, {"AT": {"#": "0.a_str", "D": "/MultiHead/CTCHead/"}, "N": "struct_name"}], "I": [{"%": 687}], "O": [{"%": 688, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 18385], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.fetch", "A": [{"AT": {"#": "0.a_str", "D": "fetch_name_0"}, "N": "name"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "col"}], "I": [{"%": 688}], "O": [{"%": 689, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, 18385], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "persistable"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}]}]}]}}
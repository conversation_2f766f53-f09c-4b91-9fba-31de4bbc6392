{"_name": "", "type": "pdf-document", "description": {"title": null, "abstract": null, "authors": null, "affiliations": null, "subjects": null, "keywords": null, "publication_date": null, "languages": null, "license": null, "publishers": null, "url_refs": null, "references": null, "publication": null, "reference_count": null, "citation_count": null, "citation_date": null, "advanced": null, "analytics": null, "logs": [], "collection": null, "acquisition": null}, "file-info": {"filename": "2305.03393v1-pg9.pdf", "filename-prov": null, "document-hash": "1a36870a3e6aa062b563b50c1eaed40685b651ee03e0538453de65e7013b742f", "#-pages": 1, "collection-name": null, "description": null, "page-hashes": [{"hash": "8a5a8d9a1ae6cbd1dcedcad02ed10195aa71d1ac3e4d56be4ab72c858d7f543e", "model": "default", "page": 1}]}, "main-text": [{"prov": [{"bbox": [134.765, 639.09302, 480.59665, 675.53699], "page": 1, "span": [0, 163], "__ref_s3_data": null}], "text": "order to compute the TED score. Inference timing results for all experiments were obtained from the same machine on a single core with AMD EPYC 7763 CPU @2.45 GHz.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 612.79181, 318.45145, 625.29486], "page": 1, "span": [0, 32], "__ref_s3_data": null}], "text": "5.1 Hyper Parameter Optimization", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.765, 536.57599, 480.59567, 608.88495], "page": 1, "span": [0, 423], "__ref_s3_data": null}], "text": "We have chosen the PubTabNet data set to perform HPO, since it includes a highly diverse set of tables. Also we report TED scores separately for simple and complex tables (tables with cell spans). Results are presented in Table. 1. It is evident that with OTSL, our model achieves the same TED score and slightly better mAP scores in comparison to HTML. However OTSL yields a 2x speed up in the inference runtime over HTML.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/0"}, {"prov": [{"bbox": [134.765, 464.01782, 480.59890999999993, 519.20526], "page": 1, "span": [0, 398], "__ref_s3_data": null}], "text": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [134.765, 273.82581000000005, 264.40829, 286.32889], "page": 1, "span": [0, 24], "__ref_s3_data": null}], "text": "5.2 Quantitative Results", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.765, 173.70000000000005, 480.72003, 269.91995], "page": 1, "span": [0, 555], "__ref_s3_data": null}], "text": "We picked the model parameter configuration that produced the best prediction quality (enc=6, dec=6, heads=8) with PubTabNet alone, then independently trained and evaluated it on three publicly available data sets: PubTabNet (395k samples), FinTabNet (113k samples) and PubTables-1M (about 1M samples). Performance results are presented in Table. 2. It is clearly evident that the model trained on OTSL outperforms HTML across the board, keeping high TEDs and mAP scores even on difficult financial tables (FinTabNet) that contain sparse and large tables.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 125.88, 480.59857000000005, 174.27795000000003], "page": 1, "span": [0, 289], "__ref_s3_data": null}], "text": "Additionally, the results show that OTSL has an advantage over HTML when applied on a bigger data set like PubTables-1M and achieves significantly improved scores. Finally, OTSL achieves faster inference due to fewer decoding steps which is a result of the reduced sequence representation.", "type": "paragraph", "payload": null, "name": "Text", "font": null}], "figures": [], "tables": [{"prov": [{"bbox": [139.66741943359375, 322.5054626464844, 475.***********, 454.***********], "page": 1, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart.", "type": "table", "payload": null, "#-cols": 8, "#-rows": 6, "data": [[{"bbox": [160.37, 339.45749, 168.04523, 350.74619], "spans": [[0, 0], [1, 0]], "text": "# enc-layers", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 2]}, {"bbox": [207.974, 339.45749, 215.64923000000002, 350.74619], "spans": [[0, 1], [1, 1]], "text": "# dec-layers", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 2]}, {"bbox": [239.79799999999997, 344.93649, 278.3338, 356.22519000000005], "spans": [[0, 2], [1, 2]], "text": "Language", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 2]}, {"bbox": [324.67001, 339.45749, 348.26419, 350.74619], "spans": [[0, 3], [0, 4], [0, 5]], "text": "TEDs", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 6], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [324.67001, 339.45749, 348.26419, 350.74619], "spans": [[0, 3], [0, 4], [0, 5]], "text": "TEDs", "type": "col_header", "col": 4, "col-header": true, "col-span": [3, 6], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [324.67001, 339.45749, 348.26419, 350.74619], "spans": [[0, 3], [0, 4], [0, 5]], "text": "TEDs", "type": "col_header", "col": 5, "col-header": true, "col-span": [3, 6], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [396.271, 339.45749, 417.12595, 350.74619], "spans": [[0, 6]], "text": "mAP", "type": "col_header", "col": 6, "col-header": true, "col-span": [6, 7], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [430.771, 339.45749, 467.14142000000004, 350.74619], "spans": [[0, 7]], "text": "Inference", "type": "col_header", "col": 7, "col-header": true, "col-span": [7, 8], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [160.37, 339.45749, 168.04523, 350.74619], "spans": [[0, 0], [1, 0]], "text": "# enc-layers", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [0, 2]}, {"bbox": [207.974, 339.45749, 215.64923000000002, 350.74619], "spans": [[0, 1], [1, 1]], "text": "# dec-layers", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [0, 2]}, {"bbox": [239.79799999999997, 344.93649, 278.3338, 356.22519000000005], "spans": [[0, 2], [1, 2]], "text": "Language", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [0, 2]}, {"bbox": [286.686, 352.40848, 312.32812, 363.69717], "spans": [[1, 3]], "text": "simple", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [320.702, 352.40848, 353.71539, 363.69717], "spans": [[1, 4]], "text": "complex", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [369.306, 352.40848, 379.02914, 363.69717], "spans": [[1, 5]], "text": "all", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 6], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [394.927, 350.41647, 418.46921, 361.70517], "spans": [[1, 6]], "text": "(0.75)", "type": "col_header", "col": 6, "col-header": true, "col-span": [6, 7], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [427.14801, 350.41647, 470.76955999999996, 361.70517], "spans": [[1, 7]], "text": "time (secs)", "type": "col_header", "col": 7, "col-header": true, "col-span": [7, 8], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [161.90601, 371.23849, 166.51474, 382.52719], "spans": [[2, 0]], "text": "6", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [209.509, 371.23849, 214.11774, 382.52719], "spans": [[2, 1]], "text": "6", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [246.71000999999998, 365.75848, 271.41064, 377.04717999999997], "spans": [[2, 2]], "text": "OTSL HTML", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [289.017, 365.75848, 310.00732, 377.04717999999997], "spans": [[2, 3]], "text": "0.965 0.969", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [326.71701, 365.75848, 347.70734, 377.04717999999997], "spans": [[2, 4]], "text": "0.934 0.927", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [363.67599, 365.75848, 384.66632, 377.04717999999997], "spans": [[2, 5]], "text": "0.955 0.955", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [397.26999, 365.69571, 416.12634, 377.10098000000005], "spans": [[2, 6]], "text": "0.88 0.857", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [439.52701, 365.69571, 458.38336, 377.10098000000005], "spans": [[2, 7]], "text": "2.73 5.39", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [161.90601, 397.53949, 166.51474, 408.82819], "spans": [[3, 0]], "text": "4", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [209.509, 397.53949, 214.11774, 408.82819], "spans": [[3, 1]], "text": "4", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [246.71000999999998, 392.06049, 271.41064, 403.34918], "spans": [[3, 2]], "text": "OTSL HTML", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [289.017, 392.06049, 310.00732, 403.34918], "spans": [[3, 3]], "text": "0.938 0.952", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [326.71701, 392.06049, 347.70734, 403.34918], "spans": [[3, 4]], "text": "0.904 0.909", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [363.67599, 392.06049, 384.66632, 403.34918], "spans": [[3, 5]], "text": "0.927 0.938", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [394.61801, 391.99771, 418.77798, 403.40298], "spans": [[3, 6]], "text": "0.853 0.843", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [439.52701, 391.99771, 458.38336, 403.40298], "spans": [[3, 7]], "text": "1.97 3.77", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [161.90601, 423.84048, 166.51474, 435.12918], "spans": [[4, 0]], "text": "2", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [209.509, 423.84048, 214.11774, 435.12918], "spans": [[4, 1]], "text": "4", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [246.71000999999998, 418.3614799999999, 271.41064, 429.65018], "spans": [[4, 2]], "text": "OTSL HTML", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [289.017, 418.3614799999999, 310.00732, 429.65018], "spans": [[4, 3]], "text": "0.923 0.945", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [326.71701, 418.3614799999999, 347.70734, 429.65018], "spans": [[4, 4]], "text": "0.897 0.901", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [363.67599, 418.3614799999999, 384.66632, 429.65018], "spans": [[4, 5]], "text": "0.915 0.931", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [394.61801, 418.29871, 418.77798, 429.70398], "spans": [[4, 6]], "text": "0.859 0.834", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [439.52701, 418.29871, 458.38336, 429.70398], "spans": [[4, 7]], "text": "1.91 3.81", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [161.90601, 450.14248999999995, 166.51474, 461.43118], "spans": [[5, 0]], "text": "4", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [209.509, 450.14248999999995, 214.11774, 461.43118], "spans": [[5, 1]], "text": "2", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [246.71000999999998, 444.66248, 271.41064, 455.95117], "spans": [[5, 2]], "text": "OTSL HTML", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [289.017, 444.66248, 310.00732, 455.95117], "spans": [[5, 3]], "text": "0.952 0.944", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [329.021, 444.66248, 345.40439, 455.95117], "spans": [[5, 4]], "text": "0.92 0.903", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [362.08801, 444.5996999999999, 386.24799, 456.00497], "spans": [[5, 5]], "text": "0.942 0.931", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [394.61801, 444.5996999999999, 418.77798, 456.00497], "spans": [[5, 6]], "text": "0.857 0.824", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [439.52701, 444.5996999999999, 458.38336, 456.00497], "spans": [[5, 7]], "text": "1.22 2", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 5, "row-header": false, "row-span": [5, 6]}]], "model": null, "bounding-box": null}], "bitmaps": null, "equations": [], "footnotes": [], "page-dimensions": [{"height": 792.0, "page": 1, "width": 612.0}], "page-footers": [], "page-headers": [], "_s3_data": null, "identifiers": null}
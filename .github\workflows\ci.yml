name: "Run CI"

on:
  pull_request:
    types: [opened, reopened, synchronize]
  push:
    branches:
      - "**"
      - "!main"
      - "!gh-pages"

jobs:
  code-checks:
    if: ${{ github.event_name == 'push' || (github.event.pull_request.head.repo.full_name != 'docling-project/docling' && github.event.pull_request.head.repo.full_name != 'docling-project/docling') }}
    uses: ./.github/workflows/checks.yml
    secrets:
      CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

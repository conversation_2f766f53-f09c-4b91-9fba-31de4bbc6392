[{"page_no": 0, "size": {"width": 841.92, "height": 595.2}, "parsed_page": {"dimension": {"angle": 0.0, "rect": {"r_x0": 0.0, "r_y0": 0.0, "r_x1": 841.92, "r_y1": 0.0, "r_x2": 841.92, "r_y2": 595.2, "r_x3": 0.0, "r_y3": 595.2, "coord_origin": "BOTTOMLEFT"}, "boundary_type": "crop_box", "art_bbox": {"l": 0.0, "t": 595.2, "r": 841.92, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "bleed_bbox": {"l": 0.0, "t": 595.2, "r": 841.92, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "crop_bbox": {"l": 0.0, "t": 595.2, "r": 841.92, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "media_bbox": {"l": 0.0, "t": 595.2, "r": 841.92, "b": 0.0, "coord_origin": "BOTTOMLEFT"}, "trim_bbox": {"l": 0.0, "t": 595.2, "r": 841.92, "b": 0.0, "coord_origin": "BOTTOMLEFT"}}, "bitmap_resources": [{"index": 0, "rect": {"r_x0": 0.0, "r_y0": 0.0, "r_x1": 841.92, "r_y1": 0.0, "r_x2": 841.92, "r_y2": 595.2, "r_x3": 0.0, "r_y3": 595.2, "coord_origin": "BOTTOMLEFT"}, "uri": null}], "char_cells": [], "word_cells": [], "textline_cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 744.09, "r_y0": 504.87, "r_x1": 764.9, "r_y1": 504.87, "r_x2": 764.9, "r_y2": 73.35, "r_x3": 744.09, "r_y3": 73.35, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 717.17, "r_y0": 504.87, "r_x1": 737.97, "r_y1": 504.87, "r_x2": 737.97, "r_y2": 70.9, "r_x3": 717.17, "r_y3": 70.9, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 690.24, "r_y0": 152.81, "r_x1": 709.83, "r_y1": 152.81, "r_x2": 709.83, "r_y2": 72.12, "r_x3": 690.24, "r_y3": 72.12, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "has_chars": false, "has_words": false, "has_lines": true, "image": null, "lines": []}, "predictions": {"layout": {"clusters": [{"id": 0, "label": "page_header", "bbox": {"l": 717.17, "t": 70.9, "r": 764.9, "b": 504.87, "coord_origin": "TOPLEFT"}, "confidence": 0.692, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 744.09, "r_y0": 504.87, "r_x1": 764.9, "r_y1": 504.87, "r_x2": 764.9, "r_y2": 73.35, "r_x3": 744.09, "r_y3": 73.35, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 717.17, "r_y0": 504.87, "r_x1": 737.97, "r_y1": 504.87, "r_x2": 737.97, "r_y2": 70.9, "r_x3": 717.17, "r_y3": 70.9, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, {"id": 8, "label": "text", "bbox": {"l": 690.24, "t": 72.12, "r": 709.83, "b": 152.81, "coord_origin": "TOPLEFT"}, "confidence": 1.0, "cells": [{"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 690.24, "r_y0": 152.81, "r_x1": 709.83, "r_y1": 152.81, "r_x2": 709.83, "r_y2": 72.12, "r_x3": 690.24, "r_y3": 72.12, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}]}, "tablestructure": {"table_map": {}}, "figures_classification": null, "equations_prediction": null, "vlm_response": null}, "assembled": {"elements": [{"label": "page_header", "id": 0, "page_no": 0, "cluster": {"id": 0, "label": "page_header", "bbox": {"l": 717.17, "t": 70.9, "r": 764.9, "b": 504.87, "coord_origin": "TOPLEFT"}, "confidence": 0.692, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 744.09, "r_y0": 504.87, "r_x1": 764.9, "r_y1": 504.87, "r_x2": 764.9, "r_y2": 73.35, "r_x3": 744.09, "r_y3": 73.35, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 717.17, "r_y0": 504.87, "r_x1": 737.97, "r_y1": 504.87, "r_x2": 737.97, "r_y2": 70.9, "r_x3": 717.17, "r_y3": 70.9, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "Docling bundles PDF document conversion to JSON and Markdown in an easy self contained"}, {"label": "text", "id": 8, "page_no": 0, "cluster": {"id": 8, "label": "text", "bbox": {"l": 690.24, "t": 72.12, "r": 709.83, "b": 152.81, "coord_origin": "TOPLEFT"}, "confidence": 1.0, "cells": [{"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 690.24, "r_y0": 152.81, "r_x1": 709.83, "r_y1": 152.81, "r_x2": 709.83, "r_y2": 72.12, "r_x3": 690.24, "r_y3": 72.12, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "package"}], "body": [{"label": "text", "id": 8, "page_no": 0, "cluster": {"id": 8, "label": "text", "bbox": {"l": 690.24, "t": 72.12, "r": 709.83, "b": 152.81, "coord_origin": "TOPLEFT"}, "confidence": 1.0, "cells": [{"index": 2, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 690.24, "r_y0": 152.81, "r_x1": 709.83, "r_y1": 152.81, "r_x2": 709.83, "r_y2": 72.12, "r_x3": 690.24, "r_y3": 72.12, "coord_origin": "TOPLEFT"}, "text": "package", "orig": "package", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "package"}], "headers": [{"label": "page_header", "id": 0, "page_no": 0, "cluster": {"id": 0, "label": "page_header", "bbox": {"l": 717.17, "t": 70.9, "r": 764.9, "b": 504.87, "coord_origin": "TOPLEFT"}, "confidence": 0.692, "cells": [{"index": 0, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 744.09, "r_y0": 504.87, "r_x1": 764.9, "r_y1": 504.87, "r_x2": 764.9, "r_y2": 73.35, "r_x3": 744.09, "r_y3": 73.35, "coord_origin": "TOPLEFT"}, "text": "Docling bundles PDF document conversion to", "orig": "Docling bundles PDF document conversion to", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}, {"index": 1, "rgba": {"r": 0, "g": 0, "b": 0, "a": 255}, "rect": {"r_x0": 717.17, "r_y0": 504.87, "r_x1": 737.97, "r_y1": 504.87, "r_x2": 737.97, "r_y2": 70.9, "r_x3": 717.17, "r_y3": 70.9, "coord_origin": "TOPLEFT"}, "text": "JSON and <PERSON><PERSON> in an easy self contained", "orig": "JSON and <PERSON><PERSON> in an easy self contained", "text_direction": "left_to_right", "confidence": 1.0, "from_ocr": true}], "children": []}, "text": "Docling bundles PDF document conversion to JSON and Markdown in an easy self contained"}]}}]
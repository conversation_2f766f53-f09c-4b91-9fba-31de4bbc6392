<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>demo_document</title>
<meta name="generator" content="Docling HTML Serializer">
<style>
    html {
        background-color: #f5f5f5;
        font-family: Arial, sans-serif;
        line-height: 1.6;
    }
    body {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        background-color: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1, h2, h3, h4, h5, h6 {
        color: #333;
        margin-top: 1.5em;
        margin-bottom: 0.5em;
    }
    h1 {
        font-size: 2em;
        border-bottom: 1px solid #eee;
        padding-bottom: 0.3em;
    }
    table {
        border-collapse: collapse;
        margin: 1em 0;
        width: 100%;
    }
    th, td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }
    th {
        background-color: #f2f2f2;
        font-weight: bold;
    }
    figure {
        margin: 1.5em 0;
        text-align: center;
    }
    figcaption {
        color: #666;
        font-style: italic;
        margin-top: 0.5em;
    }
    img {
        max-width: 100%;
        height: auto;
    }
    pre {
        background-color: #f6f8fa;
        border-radius: 3px;
        padding: 1em;
        overflow: auto;
    }
    code {
        font-family: monospace;
        background-color: #f6f8fa;
        padding: 0.2em 0.4em;
        border-radius: 3px;
    }
    pre code {
        background-color: transparent;
        padding: 0;
    }
    .formula {
        text-align: center;
        padding: 0.5em;
        margin: 1em 0;
        background-color: #f9f9f9;
    }
    .formula-not-decoded {
        text-align: center;
        padding: 0.5em;
        margin: 1em 0;
        background: repeating-linear-gradient(
            45deg,
            #f0f0f0,
            #f0f0f0 10px,
            #f9f9f9 10px,
            #f9f9f9 20px
        );
    }
    .page-break {
        page-break-after: always;
        border-top: 1px dashed #ccc;
        margin: 2em 0;
    }
    .key-value-region {
        background-color: #f9f9f9;
        padding: 1em;
        border-radius: 4px;
        margin: 1em 0;
    }
    .key-value-region dt {
        font-weight: bold;
    }
    .key-value-region dd {
        margin-left: 1em;
        margin-bottom: 0.5em;
    }
    .form-container {
        border: 1px solid #ddd;
        padding: 1em;
        border-radius: 4px;
        margin: 1em 0;
    }
    .form-item {
        margin-bottom: 0.5em;
    }
    .image-classification {
        font-size: 0.9em;
        color: #666;
        margin-top: 0.5em;
    }
</style>
</head>
<body>
<div class='page'>
<h1>Docling演示文档</h1>
<h2>简介</h2>
<p>Docling是一个强大的文档处理工具，支持多种格式的文档解析和转换。</p>
<h2>主要特性</h2>
<ul>
<li>📄 支持PDF、DOCX、PPTX等多种格式</li>
<li>🔍 高级PDF理解能力</li>
<li>🤖 AI集成支持</li>
<li>🔒 本地执行保护隐私</li>
</ul>
<h2>使用示例</h2>
<pre><code>from docling.document_converter import DocumentConverter

converter = DocumentConverter()
result = converter.convert("document.pdf")
markdown = result.document.export_to_markdown()</code></pre>
<h2>表格示例</h2>
<table><tbody><tr><th>功能</th><th>描述</th><th>状态</th></tr><tr><td>PDF解析</td><td>解析复杂PDF文档</td><td>✅</td></tr><tr><td>格式转换</td><td>多种输出格式</td><td>✅</td></tr><tr><td>OCR支持</td><td>图像文字识别</td><td>✅</td></tr></tbody></table>
<p>这是一个演示文档。</p>
</div>
</body>
</html>